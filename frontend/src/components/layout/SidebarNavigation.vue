<template>
  <div class="mt-5 flex-grow flex flex-col overflow-hidden">
    <nav class="flex-1 px-2 space-y-1">
      <!-- Dashboard -->
      <SidebarNavItem
        v-if="showDashboard"
        :item="{
          name: 'Dashboard',
          path: '/app/dashboard',
          icon: 'dashboard'
        }"
        :is-collapsed="isCollapsed"
        @click="$emit('item-click')"
      />

      <!-- Personale con submenu -->
      <SidebarNavItemCollapsible
        v-if="showPersonnel"
        :item="{
          name: 'Personale',
          icon: 'users',
          children: [
            { name: 'Directory', path: '/app/personnel', icon: 'directory' },
            { name: 'Organigramma', path: '/app/personnel/orgchart', icon: 'orgchart' },
            { name: 'Competenze', path: '/app/personnel/skills', icon: 'skills' },
            { name: 'Allocazione Risorse', path: '/app/personnel/allocation', icon: 'allocation' },
            { name: 'Dipartimenti', path: '/app/personnel/departments', icon: 'departments', admin: true },
            { name: 'Amministrazione', path: '/app/personnel/admin', icon: 'admin', admin: true }
          ]
        }"
        :is-collapsed="isCollapsed"
        @click="$emit('item-click')"
      />

      <!-- Progetti -->
      <SidebarNavItem
        v-if="showProjects"
        :item="{
          name: 'Progetti',
          path: '/app/projects',
          icon: 'projects'
        }"
        :is-collapsed="isCollapsed"
        @click="$emit('item-click')"
      />

      <!-- Timesheet con submenu -->
      <SidebarNavItemCollapsible
        v-if="showTimesheet"
        :item="{
          name: 'Timesheet',
          icon: 'clock',
          children: [
            { name: 'Le Mie Ore', path: '/app/timesheet/entry', icon: 'clock-play' },
            { name: 'Richieste', path: '/app/timesheet/requests', icon: 'calendar-plus' },
            { name: 'Dashboard', path: '/app/timesheet/dashboard', icon: 'chart-bar' },
            { name: 'Storico', path: '/app/timesheet/history', icon: 'archive' },
            { name: 'Approvazioni Team', path: '/app/timesheet/approvals', icon: 'user-group', manager: true },
            { name: 'Report & Analytics', path: '/app/timesheet/analytics', icon: 'chart-line', admin: true }
          ]
        }"
        :is-collapsed="isCollapsed"
        @click="$emit('item-click')"
      />

      <!-- CRM -->
      <SidebarNavItem
        v-if="showCRM"
        :item="{
          name: 'CRM',
          path: '#',
          icon: 'clients'
        }"
        :is-collapsed="isCollapsed"
        @click="$emit('item-click')"
      />

      <!-- Prodotti e Servizi -->
      <SidebarNavItem
        v-if="showProducts"
        :item="{
          name: 'Prodotti',
          path: '#',
          icon: 'products'
        }"
        :is-collapsed="isCollapsed"
        @click="$emit('item-click')"
      />

      <!-- Performance -->
      <SidebarNavItem
        v-if="showPerformance"
        :item="{
          name: 'Performance',
          path: '#',
          icon: 'reports'
        }"
        :is-collapsed="isCollapsed"
        @click="$emit('item-click')"
      />

      <!-- Comunicazione -->
      <SidebarNavItem
        v-if="showCommunications"
        :item="{
          name: 'Comunicazione',
          path: '#',
          icon: 'communications'
        }"
        :is-collapsed="isCollapsed"
        @click="$emit('item-click')"
      />

      <!-- Bandi e Finanziamenti -->
      <SidebarNavItem
        v-if="showFunding"
        :item="{
          name: 'Finanziamenti',
          path: '#',
          icon: 'funding'
        }"
        :is-collapsed="isCollapsed"
        @click="$emit('item-click')"
      />

      <!-- Rendicontazione -->
      <SidebarNavItem
        v-if="showReports"
        :item="{
          name: 'Rendicontazione',
          path: '#',
          icon: 'reporting'
        }"
        :is-collapsed="isCollapsed"
        @click="$emit('item-click')"
      />

      <!-- Amministrazione con submenu -->
      <SidebarNavItemCollapsible
        v-if="showAdminSection"
        :item="{
          name: 'Amministrazione',
          icon: 'settings',
          children: [
            { name: 'Gestione Utenti', path: '/app/admin/users', icon: 'user-management' },
            { name: 'Template KPI', path: '/app/admin/kpi-templates', icon: 'reports' }
          ]
        }"
        :is-collapsed="isCollapsed"
        @click="$emit('item-click')"
      />
    </nav>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { usePermissions } from '@/composables/usePermissions'
import SidebarNavItem from './SidebarNavItem.vue'
import SidebarNavItemCollapsible from './SidebarNavItemCollapsible.vue'

const props = defineProps({
  isCollapsed: {
    type: Boolean,
    default: false
  }
})

defineEmits(['item-click'])

const { hasPermission, isAdmin } = usePermissions()

// Controlli di visibilità basati sui permessi
const showDashboard = computed(() => hasPermission.value('view_dashboard'))
const showPersonnel = computed(() => hasPermission.value('view_personnel_data'))
const showProjects = computed(() => hasPermission.value('view_all_projects'))
const showTimesheet = computed(() => hasPermission.value('manage_timesheets') || hasPermission.value('view_all_projects'))
const showCRM = computed(() => hasPermission.value('view_crm'))
const showProducts = computed(() => hasPermission.value('view_products'))
const showPerformance = computed(() => hasPermission.value('view_performance'))
const showCommunications = computed(() => hasPermission.value('view_communications'))
const showFunding = computed(() => hasPermission.value('view_funding'))
const showReports = computed(() => hasPermission.value('view_reports'))
const showAdminSection = computed(() => hasPermission.value('admin_access'))
</script>
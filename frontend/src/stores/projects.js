import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '@/utils/api'

export const useProjectsStore = defineStore('projects', () => {
  // State
  const projects = ref([])
  const currentProject = ref(null)
  const loading = ref(false)
  const error = ref(null)
  
  // Cache per progetti singoli
  const projectsCache = ref(new Map())
  
  // Pagination
  const pagination = ref({
    page: 1,
    perPage: 20,
    total: 0,
    totalPages: 0
  })
  
  // Filters
  const filters = ref({
    search: '',
    status: '',
    client: '',
    type: ''
  })

  // Getters
  const filteredProjects = computed(() => {
    let filtered = projects.value

    if (filters.value.search) {
      const search = filters.value.search.toLowerCase()
      filtered = filtered.filter(project => 
        project.name.toLowerCase().includes(search) ||
        project.description?.toLowerCase().includes(search) ||
        project.client?.name?.toLowerCase().includes(search)
      )
    }

    if (filters.value.status) {
      filtered = filtered.filter(project => project.status === filters.value.status)
    }

    if (filters.value.client) {
      filtered = filtered.filter(project => project.client_id === filters.value.client)
    }

    if (filters.value.type) {
      filtered = filtered.filter(project => project.project_type === filters.value.type)
    }

    return filtered
  })

  const projectsByStatus = computed(() => {
    const grouped = {}
    projects.value.forEach(project => {
      if (!grouped[project.status]) {
        grouped[project.status] = []
      }
      grouped[project.status].push(project)
    })
    return grouped
  })

  // Actions
  const fetchProjects = async (params = {}) => {
    loading.value = true
    error.value = null
    
    try {
      const queryParams = new URLSearchParams({
        page: params.page || pagination.value.page,
        per_page: params.perPage || pagination.value.perPage,
        search: params.search || filters.value.search,
        status: params.status || filters.value.status,
        client: params.client || filters.value.client,
        type: params.type || filters.value.type
      })

      const response = await api.get(`/api/projects?${queryParams}`)

      if (response.data.success) {
        // Handle both paginated and non-paginated responses
        if (response.data.data.items) {
          projects.value = response.data.data.items
          pagination.value = response.data.data.pagination
        } else if (response.data.data.projects) {
          projects.value = response.data.data.projects
          pagination.value = response.data.data.pagination
        } else if (Array.isArray(response.data.data)) {
          projects.value = response.data.data
          pagination.value = { total: response.data.data.length, page: 1, per_page: response.data.data.length }
        } else {
          projects.value = []
          pagination.value = { total: 0, page: 1, per_page: 10 }
        }
      }
    } catch (err) {
      error.value = err.response?.data?.message || 'Errore nel caricamento progetti'
      console.error('Error fetching projects:', err)
    } finally {
      loading.value = false
    }
  }

  const fetchProject = async (projectId, forceRefresh = false) => {
    // Controlla se il progetto è già in cache e non forziamo il refresh
    if (!forceRefresh && projectsCache.value.has(projectId)) {
      const cachedProject = projectsCache.value.get(projectId)
      currentProject.value = cachedProject
      return cachedProject
    }
    
    loading.value = true
    error.value = null
    
    try {
      const response = await api.get(`/api/projects/${projectId}`)
      
      if (response.data.success) {
        const project = response.data.data.project
        currentProject.value = project
        
        // Salva nel cache
        projectsCache.value.set(projectId, project)
        
        return project
      }
    } catch (err) {
      error.value = err.response?.data?.message || 'Errore nel caricamento progetto'
      console.error('Error fetching project:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const createProject = async (projectData) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await api.post('/api/projects', projectData)
      
      if (response.data.success) {
        const newProject = response.data.data.project
        projects.value.unshift(newProject)
        return newProject
      }
    } catch (err) {
      error.value = err.response?.data?.message || 'Errore nella creazione progetto'
      console.error('Error creating project:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateProject = async (projectId, projectData) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await api.put(`/api/projects/${projectId}`, projectData)
      
      if (response.data.success) {
        const updatedProject = response.data.data.project
        
        // Update in projects list
        const index = projects.value.findIndex(p => p.id === projectId)
        if (index !== -1) {
          projects.value[index] = updatedProject
        }
        
        // Update current project if it's the same
        if (currentProject.value?.id === projectId) {
          currentProject.value = updatedProject
        }
        
        // Update cache
        projectsCache.value.set(projectId, updatedProject)
        
        return updatedProject
      }
    } catch (err) {
      error.value = err.response?.data?.message || 'Errore nell\'aggiornamento progetto'
      console.error('Error updating project:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const deleteProject = async (projectId) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await api.delete(`/api/projects/${projectId}`)
      
      if (response.data.success) {
        // Remove from projects list
        projects.value = projects.value.filter(p => p.id !== projectId)
        
        // Clear current project if it's the same
        if (currentProject.value?.id === projectId) {
          currentProject.value = null
        }
        
        // Remove from cache
        projectsCache.value.delete(projectId)
      }
    } catch (err) {
      error.value = err.response?.data?.message || 'Errore nell\'eliminazione progetto'
      console.error('Error deleting project:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const setFilters = (newFilters) => {
    filters.value = { ...filters.value, ...newFilters }
  }

  const clearFilters = () => {
    filters.value = {
      search: '',
      status: '',
      client: '',
      type: ''
    }
  }

  const setCurrentProject = (project) => {
    currentProject.value = project
  }

  const clearCurrentProject = () => {
    currentProject.value = null
  }

  // Cache utilities
  const clearCache = () => {
    projectsCache.value.clear()
  }
  
  const refreshProject = async (projectId) => {
    return await fetchProject(projectId, true)
  }
  
  const getCachedProject = (projectId) => {
    return projectsCache.value.get(projectId)
  }

  // Reset store
  const $reset = () => {
    projects.value = []
    currentProject.value = null
    loading.value = false
    error.value = null
    projectsCache.value.clear()
    pagination.value = {
      page: 1,
      perPage: 20,
      total: 0,
      totalPages: 0
    }
    filters.value = {
      search: '',
      status: '',
      client: '',
      type: ''
    }
  }

  return {
    // State
    projects,
    currentProject,
    loading,
    error,
    pagination,
    filters,
    
    // Getters
    filteredProjects,
    projectsByStatus,
    
    // Actions
    fetchProjects,
    fetchProject,
    createProject,
    updateProject,
    deleteProject,
    setFilters,
    clearFilters,
    setCurrentProject,
    clearCurrentProject,
    
    // Cache utilities
    clearCache,
    refreshProject,
    getCachedProject,
    
    $reset
  }
})

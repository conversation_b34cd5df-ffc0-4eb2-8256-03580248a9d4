import{r as x,f as W,A as Z,w as tt,c as d,j as t,g as A,a as $,m as h,i as N,b as et,v as D,G as T,F as b,k as _,t as s,o as n,z as at,n as C}from"./vendor.js";import{u as rt}from"./app.js";const st={class:"space-y-6"},ot={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},it={class:"flex justify-between items-center"},lt={class:"flex space-x-3"},dt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-4"},nt={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},ut=["value"],ct=["value"],gt={class:"flex items-end"},xt=["disabled"],pt={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},mt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},yt={class:"flex items-center"},ht={class:"ml-5 w-0 flex-1"},vt={class:"text-lg font-medium text-gray-900 dark:text-white"},kt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},ft={class:"flex items-center"},wt={class:"ml-5 w-0 flex-1"},bt={class:"text-lg font-medium text-gray-900 dark:text-white"},_t={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},jt={class:"flex items-center"},Pt={class:"ml-5 w-0 flex-1"},Ct={class:"text-lg font-medium text-gray-900 dark:text-white"},Mt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},zt={class:"flex items-center"},St={class:"ml-5 w-0 flex-1"},At={class:"text-lg font-medium text-gray-900 dark:text-white"},Dt={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Tt={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},Ft={class:"flex items-center justify-between"},Vt={class:"text-lg font-medium text-gray-900 dark:text-white"},$t={class:"text-sm text-gray-500 dark:text-gray-400"},Nt={class:"overflow-x-auto"},Ht={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},Et={class:"bg-gray-50 dark:bg-gray-700"},Bt={key:0},Ot={key:1},Rt={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},Gt={class:"px-6 py-4 whitespace-nowrap"},It={class:"flex items-center"},Yt={class:"text-sm font-medium text-gray-900 dark:text-white"},Lt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},Ut={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},Xt={class:"text-xs text-gray-500 dark:text-gray-400 ml-1"},Jt={class:"px-6 py-4 whitespace-nowrap"},qt={class:"flex items-center"},Kt={class:"flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2 max-w-[60px]"},Qt={class:"text-sm text-gray-900 dark:text-white"},Wt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},Zt={class:"flex flex-wrap gap-1"},te={key:0,class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"},ee={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},ae={class:"px-6 py-4 whitespace-nowrap"},re={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},se=["onClick"],oe=["onClick"],ie={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},le={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},de={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},ne={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},ue={class:"px-6 py-4 whitespace-nowrap"},ce={class:"px-6 py-4 whitespace-nowrap"},ge={class:"px-6 py-4 text-sm text-gray-900 dark:text-white max-w-xs truncate"},xe={key:0,class:"text-center py-12"},pe={class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},me={class:"mt-4"},ye={key:0,class:"bg-white dark:bg-gray-800 px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-gray-700 sm:px-6 rounded-lg shadow"},he={class:"flex-1 flex justify-between sm:hidden"},ve=["disabled"],ke=["disabled"],fe={class:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"},we={class:"text-sm text-gray-700 dark:text-gray-300"},be={class:"font-medium"},_e={class:"font-medium"},je={class:"font-medium"},Pe={class:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px"},Ce=["onClick"],Ae={__name:"TimesheetHistory",setup(Me){const z=rt(),j=x([]),v=x([]),f=x([]),M=x(!1),c=x(new Date().getFullYear()),p=x(""),g=x("monthly"),F=x([]),i=x({currentPage:1,perPage:50,total:0,totalPages:0}),P=x({totalHours:0,billableHours:0,activeProjects:0,dailyAverage:0}),H=W(()=>{const r=[],e=Math.max(1,i.value.currentPage-2),u=Math.min(i.value.totalPages,i.value.currentPage+2);for(let a=e;a<=u;a++)r.push(a);return r}),E=()=>{const r=new Date().getFullYear(),e=[];for(let u=0;u<5;u++)e.push(r-u);F.value=e},B=async()=>{try{const r=await fetch("/api/projects/",{headers:{"Content-Type":"application/json","X-CSRFToken":z.csrfToken}});if(r.ok){const e=await r.json();e.data&&Array.isArray(e.data.items)?j.value=e.data.items:e.data&&Array.isArray(e.data)?j.value=e.data:j.value=[]}}catch(r){console.error("Error loading projects:",r),j.value=[]}},m=async()=>{M.value=!0;try{g.value==="monthly"?await O():await R(),G()}catch(r){console.error("Error loading data:",r)}finally{M.value=!1}},O=async()=>{const r=new URLSearchParams({start_date:`${c.value}-01-01`,end_date:`${c.value}-12-31`});p.value&&r.append("project_id",p.value);const e=await fetch(`/api/timesheets/?${r}`,{headers:{"Content-Type":"application/json","X-CSRFToken":z.csrfToken}});if(e.ok){const a=(await e.json()).data||[],o={};a.forEach(l=>{const w=new Date(l.date),y=w.getMonth()+1;o[y]||(o[y]={month:y,total_hours:0,billable_hours:0,entries:[],projects:new Set,working_days:new Set}),o[y].total_hours+=l.hours,l.billable&&(o[y].billable_hours+=l.hours),o[y].entries.push(l),l.project&&o[y].projects.add(JSON.stringify({id:l.project.id,name:l.project.name})),o[y].working_days.add(w.getDate())}),v.value=Object.values(o).map(l=>({...l,projects:Array.from(l.projects).map(w=>JSON.parse(w)),daily_average:l.total_hours/Math.max(l.working_days.size,1),productivity:l.billable_hours/l.total_hours*100||0,status:l.total_hours>0?"active":"inactive"})).sort((l,w)=>l.month-w.month)}},R=async()=>{const r=new URLSearchParams({start_date:`${c.value}-01-01`,end_date:`${c.value}-12-31`,page:i.value.currentPage,per_page:i.value.perPage});p.value&&r.append("project_id",p.value);const e=await fetch(`/api/timesheets/?${r}`,{headers:{"Content-Type":"application/json","X-CSRFToken":z.csrfToken}});if(e.ok){const u=await e.json();f.value=u.data||[],u.pagination&&(i.value={...i.value,...u.pagination})}},G=()=>{let r=0,e=0,u=new Set;g.value==="monthly"?(r=v.value.reduce((a,o)=>a+o.total_hours,0),e=v.value.reduce((a,o)=>a+o.billable_hours,0),v.value.forEach(a=>{a.projects.forEach(o=>u.add(o.id))})):(r=f.value.reduce((a,o)=>a+o.hours,0),e=f.value.reduce((a,o)=>a+(o.billable?o.hours:0),0),u=new Set(f.value.map(a=>a.project_id))),P.value={totalHours:r,billableHours:e,activeProjects:u.size,dailyAverage:r/365}},S=r=>{r>=1&&r<=i.value.totalPages&&(i.value.currentPage=r,m())},V=r=>["Gennaio","Febbraio","Marzo","Aprile","Maggio","Giugno","Luglio","Agosto","Settembre","Ottobre","Novembre","Dicembre"][r-1],I=r=>r>=80?"bg-green-500":r>=60?"bg-yellow-500":"bg-red-500",Y=r=>{switch(r){case"active":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";case"inactive":return"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300";default:return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"}},L=r=>{switch(r){case"active":return"Attivo";case"inactive":return"Inattivo";default:return"Parziale"}},U=r=>{switch(r){case"approved":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";case"rejected":return"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";default:return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"}},X=r=>{switch(r){case"approved":return"Approvato";case"rejected":return"Rifiutato";default:return"In Attesa"}},J=r=>{g.value="detailed",m()},q=r=>{alert(`Esportazione dati per ${V(r.month)} ${c.value} - Funzionalità in sviluppo`)},K=()=>{alert("Esportazione report completo - Funzionalità in sviluppo")},Q=r=>new Date(r).toLocaleDateString("it-IT"),k=r=>!r||r===0?"0h":r%1===0?`${r}h`:`${r.toFixed(1)}h`;return Z(()=>{E(),B(),m()}),tt([c,p,g],()=>{i.value.currentPage=1,m()}),(r,e)=>{const u=et("router-link");return n(),d("div",st,[t("div",ot,[t("div",it,[e[7]||(e[7]=t("div",null,[t("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Storico Timesheet"),t("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Visualizza i riepiloghi mensili delle tue ore lavorate ")],-1)),t("div",lt,[t("button",{onClick:K,class:"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center"},e[5]||(e[5]=[t("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),h(" Esporta Report ")])),$(u,{to:"/app/timesheet/entry",class:"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center"},{default:N(()=>e[6]||(e[6]=[t("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),h(" Registra Ore ")])),_:1,__:[6]})])])]),t("div",dt,[t("div",nt,[t("div",null,[e[8]||(e[8]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Anno ",-1)),D(t("select",{"onUpdate:modelValue":e[0]||(e[0]=a=>c.value=a),onChange:m,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[(n(!0),d(b,null,_(F.value,a=>(n(),d("option",{key:a,value:a},s(a),9,ut))),128))],544),[[T,c.value]])]),t("div",null,[e[10]||(e[10]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Progetto ",-1)),D(t("select",{"onUpdate:modelValue":e[1]||(e[1]=a=>p.value=a),onChange:m,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[e[9]||(e[9]=t("option",{value:""},"Tutti i progetti",-1)),(n(!0),d(b,null,_(j.value,a=>(n(),d("option",{key:a.id,value:a.id},s(a.name),9,ct))),128))],544),[[T,p.value]])]),t("div",null,[e[12]||(e[12]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Visualizzazione ",-1)),D(t("select",{"onUpdate:modelValue":e[2]||(e[2]=a=>g.value=a),onChange:m,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},e[11]||(e[11]=[t("option",{value:"monthly"},"Riepilogo Mensile",-1),t("option",{value:"detailed"},"Dettaglio Giornaliero",-1)]),544),[[T,g.value]])]),t("div",gt,[t("button",{onClick:m,disabled:M.value,class:"w-full bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md text-sm font-medium"},s(M.value?"Caricando...":"Aggiorna"),9,xt)])])]),t("div",pt,[t("div",mt,[t("div",yt,[e[14]||(e[14]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",ht,[t("dl",null,[e[13]||(e[13]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Ore Totali ",-1)),t("dd",vt,s(k(P.value.totalHours)),1)])])])]),t("div",kt,[t("div",ft,[e[16]||(e[16]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",wt,[t("dl",null,[e[15]||(e[15]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Ore Fatturabili ",-1)),t("dd",bt,s(k(P.value.billableHours)),1)])])])]),t("div",_t,[t("div",jt,[e[18]||(e[18]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])])],-1)),t("div",Pt,[t("dl",null,[e[17]||(e[17]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Progetti Attivi ",-1)),t("dd",Ct,s(P.value.activeProjects),1)])])])]),t("div",Mt,[t("div",zt,[e[20]||(e[20]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",St,[t("dl",null,[e[19]||(e[19]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Media Giornaliera ",-1)),t("dd",At,s(k(P.value.dailyAverage)),1)])])])])]),t("div",Dt,[t("div",Tt,[t("div",Ft,[t("h3",Vt,s(g.value==="monthly"?"Riepiloghi Mensili":"Dettaglio Giornaliero")+" - "+s(c.value),1),t("div",$t,s(v.value.length)+" "+s(g.value==="monthly"?"mesi":"giorni")+" trovati ",1)])]),t("div",Nt,[t("table",Ht,[t("thead",Et,[g.value==="monthly"?(n(),d("tr",Bt,e[21]||(e[21]=[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Mese ",-1),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Ore Totali ",-1),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Ore Fatturabili ",-1),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Produttività ",-1),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Progetti ",-1),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Media Giornaliera ",-1),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Stato ",-1),t("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Azioni ",-1)]))):(n(),d("tr",Ot,e[22]||(e[22]=[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Data ",-1),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Progetto ",-1),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Task ",-1),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Ore ",-1),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Fatturabile ",-1),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Stato ",-1),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Descrizione ",-1)])))]),t("tbody",Rt,[g.value==="monthly"?(n(!0),d(b,{key:0},_(v.value,a=>(n(),d("tr",{key:a.month,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[t("td",Gt,[t("div",It,[t("div",Yt,s(V(a.month))+" "+s(c.value),1)])]),t("td",Lt,s(k(a.total_hours)),1),t("td",Ut,[h(s(k(a.billable_hours))+" ",1),t("span",Xt," ("+s(Math.round(a.billable_hours/a.total_hours*100)||0)+"%) ",1)]),t("td",Jt,[t("div",qt,[t("div",Kt,[t("div",{class:C(["h-2 rounded-full",I(a.productivity)]),style:at({width:Math.min(a.productivity,100)+"%"})},null,6)]),t("span",Qt,s(Math.round(a.productivity))+"%",1)])]),t("td",Wt,[t("div",Zt,[(n(!0),d(b,null,_(a.projects.slice(0,2),o=>(n(),d("span",{key:o.id,class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"},s(o.name),1))),128)),a.projects.length>2?(n(),d("span",te," +"+s(a.projects.length-2),1)):A("",!0)])]),t("td",ee,s(k(a.daily_average)),1),t("td",ae,[t("span",{class:C(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",Y(a.status)])},s(L(a.status)),3)]),t("td",re,[t("button",{onClick:o=>J(),class:"text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 mr-3"}," Dettagli ",8,se),t("button",{onClick:o=>q(a),class:"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"}," Esporta ",8,oe)])]))),128)):(n(!0),d(b,{key:1},_(f.value,a=>{var o,l;return n(),d("tr",{key:a.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[t("td",ie,s(Q(a.date)),1),t("td",le,s(((o=a.project)==null?void 0:o.name)||"N/A"),1),t("td",de,s(((l=a.task)==null?void 0:l.name)||"N/A"),1),t("td",ne,s(k(a.hours)),1),t("td",ue,[t("span",{class:C(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",a.billable?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"])},s(a.billable?"Fatturabile":"Interno"),3)]),t("td",ce,[t("span",{class:C(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",U(a.status)])},s(X(a.status)),3)]),t("td",ge,s(a.description||"-"),1)])}),128))])]),(g.value==="monthly"?v.value:f.value).length===0?(n(),d("div",xe,[e[24]||(e[24]=t("div",{class:"mx-auto h-12 w-12 text-gray-400"},[t("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])],-1)),e[25]||(e[25]=t("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun dato disponibile",-1)),t("p",pe," Non ci sono registrazioni per l'anno "+s(c.value)+s(p.value?" e progetto selezionato":"")+". ",1),t("div",me,[$(u,{to:"/app/timesheet/entry",class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 dark:bg-primary-900 dark:text-primary-300 dark:hover:bg-primary-800"},{default:N(()=>e[23]||(e[23]=[h(" Inizia a registrare ore ")])),_:1,__:[23]})])])):A("",!0)])]),i.value.totalPages>1?(n(),d("div",ye,[t("div",he,[t("button",{onClick:e[3]||(e[3]=a=>S(i.value.currentPage-1)),disabled:i.value.currentPage===1,class:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"}," Precedente ",8,ve),t("button",{onClick:e[4]||(e[4]=a=>S(i.value.currentPage+1)),disabled:i.value.currentPage===i.value.totalPages,class:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"}," Successivo ",8,ke)]),t("div",fe,[t("div",null,[t("p",we,[e[26]||(e[26]=h(" Mostrando ")),t("span",be,s((i.value.currentPage-1)*i.value.perPage+1),1),e[27]||(e[27]=h(" a ")),t("span",_e,s(Math.min(i.value.currentPage*i.value.perPage,i.value.total)),1),e[28]||(e[28]=h(" di ")),t("span",je,s(i.value.total),1),e[29]||(e[29]=h(" risultati "))])]),t("div",null,[t("nav",Pe,[(n(!0),d(b,null,_(H.value,a=>(n(),d("button",{key:a,onClick:o=>S(a),class:C(["relative inline-flex items-center px-4 py-2 border text-sm font-medium",a===i.value.currentPage?"z-10 bg-primary-50 border-primary-500 text-primary-600":"bg-white border-gray-300 text-gray-500 hover:bg-gray-50"])},s(a),11,Ce))),128))])])])])):A("",!0)])}}};export{Ae as default};

import{r as p,f as W,A as Z,w as ee,c as n,j as e,g as S,a as $,m as f,i as N,b as te,v as D,G as T,F as _,k as j,t as s,o as u,z as ae,n as P}from"./vendor.js";import{u as re}from"./app.js";const se={class:"space-y-6"},oe={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},ie={class:"flex justify-between items-center"},le={class:"flex space-x-3"},de={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-4"},ne={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},ue=["value"],ce=["value"],ge={class:"flex items-end"},xe=["disabled"],pe={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},me={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},ye={class:"flex items-center"},he={class:"ml-5 w-0 flex-1"},ve={class:"text-lg font-medium text-gray-900 dark:text-white"},fe={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},ke={class:"flex items-center"},be={class:"ml-5 w-0 flex-1"},we={class:"text-lg font-medium text-gray-900 dark:text-white"},_e={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},je={class:"flex items-center"},Ae={class:"ml-5 w-0 flex-1"},Pe={class:"text-lg font-medium text-gray-900 dark:text-white"},Ce={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Me={class:"flex items-center"},ze={class:"ml-5 w-0 flex-1"},Se={class:"text-lg font-medium text-gray-900 dark:text-white"},De={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Te={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},Fe={class:"flex items-center justify-between"},Ve={class:"text-lg font-medium text-gray-900 dark:text-white"},$e={class:"text-sm text-gray-500 dark:text-gray-400"},Ne={class:"overflow-x-auto"},Ee={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},He={class:"bg-gray-50 dark:bg-gray-700"},Be={key:0},Oe={key:1},Re={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},Le={class:"px-6 py-4 whitespace-nowrap"},Ge={class:"flex items-center"},Ie={class:"text-sm font-medium text-gray-900 dark:text-white"},Ye={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},Ue={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},Xe={class:"text-xs text-gray-500 dark:text-gray-400 ml-1"},Je={class:"px-6 py-4 whitespace-nowrap"},qe={class:"flex items-center"},Ke={class:"flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2 max-w-[60px]"},Qe={class:"text-sm text-gray-900 dark:text-white"},We={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},Ze={class:"flex flex-wrap gap-1"},et={key:0,class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"},tt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},at={class:"px-6 py-4 whitespace-nowrap"},rt={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},st=["onClick"],ot=["onClick"],it={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},lt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},dt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},nt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},ut={class:"px-6 py-4 whitespace-nowrap"},ct={class:"px-6 py-4 whitespace-nowrap"},gt={class:"px-6 py-4 text-sm text-gray-900 dark:text-white max-w-xs truncate"},xt={key:0,class:"text-center py-12"},pt={class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},mt={class:"mt-4"},yt={key:0,class:"bg-white dark:bg-gray-800 px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-gray-700 sm:px-6 rounded-lg shadow"},ht={class:"flex-1 flex justify-between sm:hidden"},vt=["disabled"],ft=["disabled"],kt={class:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"},bt={class:"text-sm text-gray-700 dark:text-gray-300"},wt={class:"font-medium"},_t={class:"font-medium"},jt={class:"font-medium"},At={class:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px"},Pt=["onClick"],St={__name:"TimesheetHistory",setup(Ct){const M=re(),m=p([]),b=p([]),x=p([]),C=p(!1),c=p(new Date().getFullYear()),y=p(""),g=p("monthly"),F=p([]),d=p({currentPage:1,perPage:50,total:0,totalPages:0}),A=p({totalHours:0,billableHours:0,activeProjects:0,dailyAverage:0}),E=W(()=>{const r=[],t=Math.max(1,d.value.currentPage-2),o=Math.min(d.value.totalPages,d.value.currentPage+2);for(let a=t;a<=o;a++)r.push(a);return r}),H=()=>{const r=new Date().getFullYear(),t=[];for(let o=0;o<5;o++)t.push(r-o);F.value=t},B=async()=>{try{const r=await fetch("/api/projects/",{headers:{"Content-Type":"application/json","X-CSRFToken":M.csrfToken}});if(r.ok){const t=await r.json();t.data&&Array.isArray(t.data.items)?m.value=t.data.items:t.data&&Array.isArray(t.data)?m.value=t.data:Array.isArray(t)?m.value=t:m.value=[],console.log("Loaded projects:",m.value)}else console.error("Failed to load projects:",r.status),m.value=[]}catch(r){console.error("Error loading projects:",r),m.value=[]}},h=async()=>{C.value=!0;try{g.value==="monthly"?await O():await R(),L()}catch(r){console.error("Error loading data:",r)}finally{C.value=!1}},O=async()=>{const r=new URLSearchParams({start_date:`${c.value}-01-01`,end_date:`${c.value}-12-31`});y.value&&r.append("project_id",y.value);const t=await fetch(`/api/timesheets/?${r}`,{headers:{"Content-Type":"application/json","X-CSRFToken":M.csrfToken}});if(t.ok){const o=await t.json();let a=[];o.data&&Array.isArray(o.data)?a=o.data:o.data&&Array.isArray(o.data.items)?a=o.data.items:Array.isArray(o)&&(a=o),console.log("Loaded timesheets for monthly data:",a.length,"entries");const i={};a.forEach(l=>{const w=new Date(l.date),v=w.getMonth()+1;i[v]||(i[v]={month:v,total_hours:0,billable_hours:0,entries:[],projects:new Set,working_days:new Set}),i[v].total_hours+=l.hours||0,l.billable&&(i[v].billable_hours+=l.hours||0),i[v].entries.push(l),l.project&&i[v].projects.add(JSON.stringify({id:l.project.id,name:l.project.name})),i[v].working_days.add(w.getDate())}),b.value=Object.values(i).map(l=>({...l,projects:Array.from(l.projects).map(w=>JSON.parse(w)),daily_average:l.total_hours/Math.max(l.working_days.size,1),productivity:l.billable_hours/l.total_hours*100||0,status:l.total_hours>0?"active":"inactive"})).sort((l,w)=>l.month-w.month)}},R=async()=>{const r=new URLSearchParams({start_date:`${c.value}-01-01`,end_date:`${c.value}-12-31`,page:d.value.currentPage,per_page:d.value.perPage});y.value&&r.append("project_id",y.value);const t=await fetch(`/api/timesheets/?${r}`,{headers:{"Content-Type":"application/json","X-CSRFToken":M.csrfToken}});if(t.ok){const o=await t.json();o.data&&Array.isArray(o.data)?x.value=o.data:o.data&&Array.isArray(o.data.items)?x.value=o.data.items:Array.isArray(o)?x.value=o:x.value=[],console.log("Loaded detailed entries:",x.value.length,"entries"),o.pagination&&(d.value={...d.value,...o.pagination})}else console.error("Failed to load detailed data:",t.status),x.value=[]},L=()=>{let r=0,t=0,o=new Set;if(g.value==="monthly"){const a=Array.isArray(b.value)?b.value:[];r=a.reduce((i,l)=>i+(l.total_hours||0),0),t=a.reduce((i,l)=>i+(l.billable_hours||0),0),a.forEach(i=>{i.projects&&Array.isArray(i.projects)&&i.projects.forEach(l=>o.add(l.id))})}else{const a=Array.isArray(x.value)?x.value:[];r=a.reduce((i,l)=>i+(l.hours||0),0),t=a.reduce((i,l)=>i+(l.billable&&l.hours||0),0),a.forEach(i=>{i.project_id&&o.add(i.project_id)})}A.value={totalHours:r,billableHours:t,activeProjects:o.size,dailyAverage:r/365}},z=r=>{r>=1&&r<=d.value.totalPages&&(d.value.currentPage=r,h())},V=r=>["Gennaio","Febbraio","Marzo","Aprile","Maggio","Giugno","Luglio","Agosto","Settembre","Ottobre","Novembre","Dicembre"][r-1],G=r=>r>=80?"bg-green-500":r>=60?"bg-yellow-500":"bg-red-500",I=r=>{switch(r){case"active":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";case"inactive":return"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300";default:return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"}},Y=r=>{switch(r){case"active":return"Attivo";case"inactive":return"Inattivo";default:return"Parziale"}},U=r=>{switch(r){case"approved":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";case"rejected":return"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";default:return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"}},X=r=>{switch(r){case"approved":return"Approvato";case"rejected":return"Rifiutato";default:return"In Attesa"}},J=r=>{g.value="detailed",h()},q=r=>{alert(`Esportazione dati per ${V(r.month)} ${c.value} - Funzionalità in sviluppo`)},K=()=>{alert("Esportazione report completo - Funzionalità in sviluppo")},Q=r=>new Date(r).toLocaleDateString("it-IT"),k=r=>!r||r===0?"0h":r%1===0?`${r}h`:`${r.toFixed(1)}h`;return Z(()=>{H(),B(),h()}),ee([c,y,g],()=>{d.value.currentPage=1,h()}),(r,t)=>{const o=te("router-link");return u(),n("div",se,[e("div",oe,[e("div",ie,[t[7]||(t[7]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Storico Timesheet"),e("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Visualizza i riepiloghi mensili delle tue ore lavorate ")],-1)),e("div",le,[e("button",{onClick:K,class:"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center"},t[5]||(t[5]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),f(" Esporta Report ")])),$(o,{to:"/app/timesheet/entry",class:"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center"},{default:N(()=>t[6]||(t[6]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),f(" Registra Ore ")])),_:1,__:[6]})])])]),e("div",de,[e("div",ne,[e("div",null,[t[8]||(t[8]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Anno ",-1)),D(e("select",{"onUpdate:modelValue":t[0]||(t[0]=a=>c.value=a),onChange:h,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[(u(!0),n(_,null,j(F.value,a=>(u(),n("option",{key:a,value:a},s(a),9,ue))),128))],544),[[T,c.value]])]),e("div",null,[t[10]||(t[10]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Progetto ",-1)),D(e("select",{"onUpdate:modelValue":t[1]||(t[1]=a=>y.value=a),onChange:h,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[t[9]||(t[9]=e("option",{value:""},"Tutti i progetti",-1)),(u(!0),n(_,null,j(m.value,a=>(u(),n("option",{key:a.id,value:a.id},s(a.name),9,ce))),128))],544),[[T,y.value]])]),e("div",null,[t[12]||(t[12]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Visualizzazione ",-1)),D(e("select",{"onUpdate:modelValue":t[2]||(t[2]=a=>g.value=a),onChange:h,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},t[11]||(t[11]=[e("option",{value:"monthly"},"Riepilogo Mensile",-1),e("option",{value:"detailed"},"Dettaglio Giornaliero",-1)]),544),[[T,g.value]])]),e("div",ge,[e("button",{onClick:h,disabled:C.value,class:"w-full bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md text-sm font-medium"},s(C.value?"Caricando...":"Aggiorna"),9,xe)])])]),e("div",pe,[e("div",me,[e("div",ye,[t[14]||(t[14]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),e("div",he,[e("dl",null,[t[13]||(t[13]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Ore Totali ",-1)),e("dd",ve,s(k(A.value.totalHours)),1)])])])]),e("div",fe,[e("div",ke,[t[16]||(t[16]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),e("div",be,[e("dl",null,[t[15]||(t[15]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Ore Fatturabili ",-1)),e("dd",we,s(k(A.value.billableHours)),1)])])])]),e("div",_e,[e("div",je,[t[18]||(t[18]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])])],-1)),e("div",Ae,[e("dl",null,[t[17]||(t[17]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Progetti Attivi ",-1)),e("dd",Pe,s(A.value.activeProjects),1)])])])]),e("div",Ce,[e("div",Me,[t[20]||(t[20]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),e("div",ze,[e("dl",null,[t[19]||(t[19]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Media Giornaliera ",-1)),e("dd",Se,s(k(A.value.dailyAverage)),1)])])])])]),e("div",De,[e("div",Te,[e("div",Fe,[e("h3",Ve,s(g.value==="monthly"?"Riepiloghi Mensili":"Dettaglio Giornaliero")+" - "+s(c.value),1),e("div",$e,s(b.value.length)+" "+s(g.value==="monthly"?"mesi":"giorni")+" trovati ",1)])]),e("div",Ne,[e("table",Ee,[e("thead",He,[g.value==="monthly"?(u(),n("tr",Be,t[21]||(t[21]=[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Mese ",-1),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Ore Totali ",-1),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Ore Fatturabili ",-1),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Produttività ",-1),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Progetti ",-1),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Media Giornaliera ",-1),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Stato ",-1),e("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Azioni ",-1)]))):(u(),n("tr",Oe,t[22]||(t[22]=[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Data ",-1),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Progetto ",-1),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Task ",-1),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Ore ",-1),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Fatturabile ",-1),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Stato ",-1),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Descrizione ",-1)])))]),e("tbody",Re,[g.value==="monthly"?(u(!0),n(_,{key:0},j(b.value,a=>(u(),n("tr",{key:a.month,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[e("td",Le,[e("div",Ge,[e("div",Ie,s(V(a.month))+" "+s(c.value),1)])]),e("td",Ye,s(k(a.total_hours)),1),e("td",Ue,[f(s(k(a.billable_hours))+" ",1),e("span",Xe," ("+s(Math.round(a.billable_hours/a.total_hours*100)||0)+"%) ",1)]),e("td",Je,[e("div",qe,[e("div",Ke,[e("div",{class:P(["h-2 rounded-full",G(a.productivity)]),style:ae({width:Math.min(a.productivity,100)+"%"})},null,6)]),e("span",Qe,s(Math.round(a.productivity))+"%",1)])]),e("td",We,[e("div",Ze,[(u(!0),n(_,null,j(a.projects.slice(0,2),i=>(u(),n("span",{key:i.id,class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"},s(i.name),1))),128)),a.projects.length>2?(u(),n("span",et," +"+s(a.projects.length-2),1)):S("",!0)])]),e("td",tt,s(k(a.daily_average)),1),e("td",at,[e("span",{class:P(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",I(a.status)])},s(Y(a.status)),3)]),e("td",rt,[e("button",{onClick:i=>J(),class:"text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 mr-3"}," Dettagli ",8,st),e("button",{onClick:i=>q(a),class:"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"}," Esporta ",8,ot)])]))),128)):(u(!0),n(_,{key:1},j(x.value,a=>{var i,l;return u(),n("tr",{key:a.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[e("td",it,s(Q(a.date)),1),e("td",lt,s(((i=a.project)==null?void 0:i.name)||"N/A"),1),e("td",dt,s(((l=a.task)==null?void 0:l.name)||"N/A"),1),e("td",nt,s(k(a.hours)),1),e("td",ut,[e("span",{class:P(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",a.billable?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"])},s(a.billable?"Fatturabile":"Interno"),3)]),e("td",ct,[e("span",{class:P(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",U(a.status)])},s(X(a.status)),3)]),e("td",gt,s(a.description||"-"),1)])}),128))])]),(g.value==="monthly"?b.value:x.value).length===0?(u(),n("div",xt,[t[24]||(t[24]=e("div",{class:"mx-auto h-12 w-12 text-gray-400"},[e("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])],-1)),t[25]||(t[25]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun dato disponibile",-1)),e("p",pt," Non ci sono registrazioni per l'anno "+s(c.value)+s(y.value?" e progetto selezionato":"")+". ",1),e("div",mt,[$(o,{to:"/app/timesheet/entry",class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 dark:bg-primary-900 dark:text-primary-300 dark:hover:bg-primary-800"},{default:N(()=>t[23]||(t[23]=[f(" Inizia a registrare ore ")])),_:1,__:[23]})])])):S("",!0)])]),d.value.totalPages>1?(u(),n("div",yt,[e("div",ht,[e("button",{onClick:t[3]||(t[3]=a=>z(d.value.currentPage-1)),disabled:d.value.currentPage===1,class:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"}," Precedente ",8,vt),e("button",{onClick:t[4]||(t[4]=a=>z(d.value.currentPage+1)),disabled:d.value.currentPage===d.value.totalPages,class:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"}," Successivo ",8,ft)]),e("div",kt,[e("div",null,[e("p",bt,[t[26]||(t[26]=f(" Mostrando ")),e("span",wt,s((d.value.currentPage-1)*d.value.perPage+1),1),t[27]||(t[27]=f(" a ")),e("span",_t,s(Math.min(d.value.currentPage*d.value.perPage,d.value.total)),1),t[28]||(t[28]=f(" di ")),e("span",jt,s(d.value.total),1),t[29]||(t[29]=f(" risultati "))])]),e("div",null,[e("nav",At,[(u(!0),n(_,null,j(E.value,a=>(u(),n("button",{key:a,onClick:i=>z(a),class:P(["relative inline-flex items-center px-4 py-2 border text-sm font-medium",a===d.value.currentPage?"z-10 bg-primary-50 border-primary-500 text-primary-600":"bg-white border-gray-300 text-gray-500 hover:bg-gray-50"])},s(a),11,Pt))),128))])])])])):S("",!0)])}}};export{St as default};

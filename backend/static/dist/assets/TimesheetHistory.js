import{r as x,f as R,w as $,A as B,c as d,j as t,g as w,a as O,i as U,b as L,v as h,x as C,G as S,F as b,k as _,t as i,H as X,m as v,o as l,n as j}from"./vendor.js";import{u as G}from"./app.js";const q={class:"space-y-6"},Y={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},J={class:"flex justify-between items-center"},K={class:"flex space-x-3"},Q={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-4"},W={class:"grid grid-cols-1 md:grid-cols-5 gap-4"},Z=["value"],tt={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},et={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},at={class:"flex items-center"},st={class:"ml-5 w-0 flex-1"},rt={class:"text-lg font-medium text-gray-900 dark:text-white"},ot={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},it={class:"flex items-center"},dt={class:"ml-5 w-0 flex-1"},lt={class:"text-lg font-medium text-gray-900 dark:text-white"},nt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},ut={class:"flex items-center"},gt={class:"ml-5 w-0 flex-1"},ct={class:"text-lg font-medium text-gray-900 dark:text-white"},xt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},pt={class:"flex items-center"},mt={class:"ml-5 w-0 flex-1"},vt={class:"text-lg font-medium text-gray-900 dark:text-white"},yt={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},ht={class:"overflow-x-auto"},ft={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},kt={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},wt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},bt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},_t={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},jt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},Dt={class:"px-6 py-4 whitespace-nowrap"},Pt={class:"px-6 py-4 whitespace-nowrap"},Ct={class:"px-6 py-4 text-sm text-gray-900 dark:text-white max-w-xs truncate"},St={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},Tt=["onClick"],zt=["onClick"],Mt={key:2,class:"text-gray-400 dark:text-gray-500"},At={key:0,class:"text-center py-8"},Vt={key:0,class:"bg-white dark:bg-gray-800 px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-gray-700 sm:px-6 rounded-lg shadow"},Et={class:"flex-1 flex justify-between sm:hidden"},It=["disabled"],Ft=["disabled"],Ht={class:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"},Nt={class:"text-sm text-gray-700 dark:text-gray-300"},Rt={class:"font-medium"},$t={class:"font-medium"},Bt={class:"font-medium"},Ot={class:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px"},Ut=["onClick"],qt={__name:"TimesheetHistory",setup(Lt){const f=G(),c=x([]),D=x([]),P=x(!1),r=x({startDate:"",endDate:"",projectId:"",status:""}),o=x({currentPage:1,perPage:50,total:0,totalPages:0}),p=x({totalHours:0,billableHours:0,activeProjects:0,dailyAverage:0}),T=R(()=>{const a=[],e=Math.max(1,o.value.currentPage-2),n=Math.min(o.value.totalPages,o.value.currentPage+2);for(let s=e;s<=n;s++)a.push(s);return a}),z=async()=>{try{const a=await fetch("/api/projects/",{headers:{"Content-Type":"application/json","X-CSRFToken":f.csrfToken}});if(a.ok){const e=await a.json();D.value=e.data||[]}}catch(a){console.error("Error loading projects:",a)}},m=async()=>{P.value=!0;try{const a=new URLSearchParams({page:o.value.currentPage,per_page:o.value.perPage});r.value.startDate&&a.append("start_date",r.value.startDate),r.value.endDate&&a.append("end_date",r.value.endDate),r.value.projectId&&a.append("project_id",r.value.projectId),r.value.status&&a.append("status",r.value.status);const e=await fetch(`/api/timesheets/?${a}`,{headers:{"Content-Type":"application/json","X-CSRFToken":f.csrfToken}});if(e.ok){const n=await e.json();c.value=n.data||[],n.pagination&&(o.value={...o.value,...n.pagination}),M()}}catch(a){console.error("Error loading timesheets:",a)}finally{P.value=!1}},M=()=>{const a=c.value.reduce((u,g)=>u+g.hours,0),e=c.value.reduce((u,g)=>u+(g.billable?g.hours:0),0),n=new Set(c.value.map(u=>u.project_id)),s=r.value.startDate&&r.value.endDate?Math.max(1,Math.ceil((new Date(r.value.endDate)-new Date(r.value.startDate))/(1e3*60*60*24))):30;p.value={totalHours:a,billableHours:e,activeProjects:n.size,dailyAverage:a/s}},k=a=>{a>=1&&a<=o.value.totalPages&&(o.value.currentPage=a,m())},A=a=>{console.log("Edit entry:",a)},V=async a=>{if(confirm("Sei sicuro di voler eliminare questa registrazione?"))try{(await fetch(`/api/timesheets/${a}`,{method:"DELETE",headers:{"Content-Type":"application/json","X-CSRFToken":f.csrfToken}})).ok&&await m()}catch(e){console.error("Error deleting entry:",e)}},E=()=>{console.log("Export data")},y=a=>!a||a===0?"0h":a%1===0?`${a}h`:`${a.toFixed(1)}h`,I=a=>new Date(a).toLocaleDateString("it-IT"),F=a=>{switch(a){case"approved":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";case"rejected":return"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";default:return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"}},H=a=>{switch(a){case"approved":return"Approvato";case"rejected":return"Rifiutato";default:return"In Attesa"}};return $([()=>r.value.startDate,()=>r.value.endDate,()=>r.value.projectId,()=>r.value.status],()=>{o.value.currentPage=1,m()}),B(()=>{const a=new Date,e=new Date(a.getFullYear(),a.getMonth()-3,a.getDate());r.value.startDate=e.toISOString().split("T")[0],r.value.endDate=a.toISOString().split("T")[0],z(),m()}),(a,e)=>{const n=L("router-link");return l(),d("div",q,[t("div",Y,[t("div",J,[e[7]||(e[7]=t("div",null,[t("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Storico Timesheet"),t("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Visualizza e gestisci lo storico delle tue registrazioni ore ")],-1)),t("div",K,[t("button",{onClick:E,class:"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"}," Esporta CSV "),O(n,{to:"/app/timesheet/entry",class:"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium"},{default:U(()=>e[6]||(e[6]=[v(" Nuova Registrazione ")])),_:1,__:[6]})])])]),t("div",Q,[t("div",W,[t("div",null,[e[8]||(e[8]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Da Data ",-1)),h(t("input",{"onUpdate:modelValue":e[0]||(e[0]=s=>r.value.startDate=s),type:"date",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[C,r.value.startDate]])]),t("div",null,[e[9]||(e[9]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," A Data ",-1)),h(t("input",{"onUpdate:modelValue":e[1]||(e[1]=s=>r.value.endDate=s),type:"date",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[C,r.value.endDate]])]),t("div",null,[e[11]||(e[11]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Progetto ",-1)),h(t("select",{"onUpdate:modelValue":e[2]||(e[2]=s=>r.value.projectId=s),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[e[10]||(e[10]=t("option",{value:""},"Tutti i progetti",-1)),(l(!0),d(b,null,_(D.value,s=>(l(),d("option",{key:s.id,value:s.id},i(s.name),9,Z))),128))],512),[[S,r.value.projectId]])]),t("div",null,[e[13]||(e[13]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Stato ",-1)),h(t("select",{"onUpdate:modelValue":e[3]||(e[3]=s=>r.value.status=s),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},e[12]||(e[12]=[t("option",{value:""},"Tutti gli stati",-1),t("option",{value:"pending"},"In Attesa",-1),t("option",{value:"approved"},"Approvato",-1),t("option",{value:"rejected"},"Rifiutato",-1)]),512),[[S,r.value.status]])]),t("div",{class:"flex items-end"},[t("button",{onClick:m,class:"w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium"}," Filtra ")])])]),t("div",tt,[t("div",et,[t("div",at,[e[15]||(e[15]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",st,[t("dl",null,[e[14]||(e[14]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Ore Totali ",-1)),t("dd",rt,i(y(p.value.totalHours)),1)])])])]),t("div",ot,[t("div",it,[e[17]||(e[17]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",dt,[t("dl",null,[e[16]||(e[16]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Ore Fatturabili ",-1)),t("dd",lt,i(y(p.value.billableHours)),1)])])])]),t("div",nt,[t("div",ut,[e[19]||(e[19]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])])],-1)),t("div",gt,[t("dl",null,[e[18]||(e[18]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Progetti Attivi ",-1)),t("dd",ct,i(p.value.activeProjects),1)])])])]),t("div",xt,[t("div",pt,[e[21]||(e[21]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",mt,[t("dl",null,[e[20]||(e[20]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Media Giornaliera ",-1)),t("dd",vt,i(y(p.value.dailyAverage)),1)])])])])]),t("div",yt,[e[24]||(e[24]=t("div",{class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},[t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Registrazioni Timesheet ")],-1)),t("div",ht,[t("table",ft,[e[22]||(e[22]=t("thead",{class:"bg-gray-50 dark:bg-gray-700"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Data "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Progetto "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Task "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Ore "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Fatturabile "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Stato "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Descrizione "),t("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Azioni ")])],-1)),t("tbody",kt,[(l(!0),d(b,null,_(c.value,s=>{var u,g;return l(),d("tr",{key:s.id},[t("td",wt,i(I(s.date)),1),t("td",bt,i(((u=s.project)==null?void 0:u.name)||"N/A"),1),t("td",_t,i(((g=s.task)==null?void 0:g.name)||"N/A"),1),t("td",jt,i(y(s.hours)),1),t("td",Dt,[t("span",{class:j(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",s.billable?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"])},i(s.billable?"Fatturabile":"Interno"),3)]),t("td",Pt,[t("span",{class:j(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",F(s.status)])},i(H(s.status)),3)]),t("td",Ct,i(s.description||"-"),1),t("td",St,[s.status==="pending"?(l(),d("button",{key:0,onClick:N=>A(s),class:"text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 mr-3"}," Modifica ",8,Tt)):w("",!0),s.status==="pending"?(l(),d("button",{key:1,onClick:N=>V(s.id),class:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"}," Elimina ",8,zt)):(l(),d("span",Mt,i(s.status==="approved"?"Approvato":"Rifiutato"),1))])])}),128))])]),c.value.length===0?(l(),d("div",At,e[23]||(e[23]=[X('<div class="mx-auto h-12 w-12 text-gray-400"><svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg></div><h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Nessun timesheet</h3><p class="mt-1 text-sm text-gray-500 dark:text-gray-400"> Non ci sono registrazioni per i filtri selezionati. </p>',3)]))):w("",!0)])]),o.value.totalPages>1?(l(),d("div",Vt,[t("div",Et,[t("button",{onClick:e[4]||(e[4]=s=>k(o.value.currentPage-1)),disabled:o.value.currentPage===1,class:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"}," Precedente ",8,It),t("button",{onClick:e[5]||(e[5]=s=>k(o.value.currentPage+1)),disabled:o.value.currentPage===o.value.totalPages,class:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"}," Successivo ",8,Ft)]),t("div",Ht,[t("div",null,[t("p",Nt,[e[25]||(e[25]=v(" Mostrando ")),t("span",Rt,i((o.value.currentPage-1)*o.value.perPage+1),1),e[26]||(e[26]=v(" a ")),t("span",$t,i(Math.min(o.value.currentPage*o.value.perPage,o.value.total)),1),e[27]||(e[27]=v(" di ")),t("span",Bt,i(o.value.total),1),e[28]||(e[28]=v(" risultati "))])]),t("div",null,[t("nav",Ot,[(l(!0),d(b,null,_(T.value,s=>(l(),d("button",{key:s,onClick:u=>k(s),class:j(["relative inline-flex items-center px-4 py-2 border text-sm font-medium",s===o.value.currentPage?"z-10 bg-primary-50 border-primary-500 text-primary-600":"bg-white border-gray-300 text-gray-500 hover:bg-gray-50"])},i(s),11,Ut))),128))])])])])):w("",!0)])}}};export{qt as default};

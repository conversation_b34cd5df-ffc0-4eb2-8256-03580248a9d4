import{r as d,f as K,A as ge,c as o,j as e,g as c,m as W,t as r,F as A,k as M,v as p,G as z,x as Z,n as $,o as l,C as ce}from"./vendor.js";import{u as xe}from"./app.js";const ve={class:"space-y-6"},pe={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},me={class:"flex items-center justify-between"},ye={class:"flex items-center space-x-3"},ke=["disabled"],be={key:0,class:"relative"},he={key:0,class:"absolute right-0 mt-2 w-48 bg-white dark:bg-gray-700 rounded-md shadow-lg z-10"},fe={key:0,class:"mt-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4"},we={class:"flex items-start"},_e={class:"ml-3"},Ce={class:"text-sm font-medium text-red-800 dark:text-red-200"},Te={class:"mt-2 space-y-1"},je=["onClick"],Ae={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},Me={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},ze={class:"flex items-center"},Se={class:"ml-4"},De={class:"text-2xl font-semibold text-gray-900 dark:text-white"},Re={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Be={class:"flex items-center"},$e={class:"ml-4"},Le={class:"text-2xl font-semibold text-gray-900 dark:text-white"},Ue={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Ve={class:"flex items-center"},Ee={class:"ml-4"},Fe={class:"text-2xl font-semibold text-gray-900 dark:text-white"},Oe={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Pe={class:"flex items-center"},Ne={class:"ml-4"},Ge={class:"text-2xl font-semibold text-gray-900 dark:text-white"},Ie={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-4"},Xe={class:"grid grid-cols-1 md:grid-cols-5 gap-4"},He=["value"],Je=["value"],Qe={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Ye={class:"overflow-x-auto"},qe={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},Ke={class:"bg-gray-50 dark:bg-gray-700"},We={class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"},Ze=["checked"],et={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},tt={key:0},st={key:1},at={class:"px-6 py-4 whitespace-nowrap"},rt=["value"],ot={class:"px-6 py-4 whitespace-nowrap"},lt={class:"flex items-center"},dt={class:"flex-shrink-0 h-10 w-10"},nt={class:"h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center"},it={class:"text-sm font-medium text-gray-700 dark:text-gray-300"},ut={class:"ml-4"},gt={class:"text-sm font-medium text-gray-900 dark:text-white"},ct={class:"text-sm text-gray-500 dark:text-gray-400"},xt={key:0,class:"ml-2"},vt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},pt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},mt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},yt={class:"px-6 py-4 whitespace-nowrap"},kt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"},bt={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},ht={class:"flex items-center justify-end space-x-2"},ft=["onClick"],wt=["onClick"],_t=["onClick"],Ct={key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},Tt={class:"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white dark:bg-gray-800"},jt={class:"mt-3"},At={class:"flex items-center justify-between mb-4"},Mt={class:"text-lg font-medium text-gray-900 dark:text-white"},zt={class:"space-y-4"},St={class:"grid grid-cols-2 gap-4"},Dt={class:"text-sm text-gray-900 dark:text-white"},Rt={class:"text-sm text-gray-900 dark:text-white"},Bt={class:"text-sm text-gray-900 dark:text-white"},$t={key:0,class:"flex space-x-3"},Lt={key:1,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},Ut={class:"relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 shadow-lg rounded-md bg-white dark:bg-gray-800"},Vt={class:"mt-3"},Et={class:"space-y-4"},Ft={class:"flex space-x-3"},Ot=["disabled"],It={__name:"TimesheetApprovals",setup(Pt){const h=xe(),u=d([]),L=d([]),m=d([]),g=d([]),n=d(null),S=d(!1),f=d(!1),y=d(!1),k=d(!1),w=d(!1),b=d(""),D=d(new Date().getMonth()+1),_=d("submitted"),C=d(""),U=d(!1),V=d(""),R=K(()=>{const a=u.value.filter(v=>v.status==="submitted").length,t=u.value.filter(v=>v.status==="approved").length,i=u.value.reduce((v,T)=>v+(T.total_hours||0),0);return{pending:a,approved:t,totalHours:i}}),ee=K(()=>[{value:1,label:"Gennaio"},{value:2,label:"Febbraio"},{value:3,label:"Marzo"},{value:4,label:"Aprile"},{value:5,label:"Maggio"},{value:6,label:"Giugno"},{value:7,label:"Luglio"},{value:8,label:"Agosto"},{value:9,label:"Settembre"},{value:10,label:"Ottobre"},{value:11,label:"Novembre"},{value:12,label:"Dicembre"}]),x=async()=>{S.value=!0;try{const a=new URLSearchParams({month:D.value,year:new Date().getFullYear()});_.value&&a.append("status",_.value),C.value&&a.append("user_id",C.value);const t=await fetch(`/api/monthly-timesheets/?${a}`,{headers:{"Content-Type":"application/json","X-CSRFToken":h.csrfToken}});if(t.ok){const i=await t.json();u.value=i.data||[]}}catch(a){console.error("Error loading timesheets:",a)}finally{S.value=!1}},te=async()=>{var a;try{const t=await fetch("/api/personnel/users",{headers:{"Content-Type":"application/json","X-CSRFToken":h.csrfToken}});if(t.ok){const i=await t.json();L.value=((a=i.data)==null?void 0:a.users)||[]}}catch(t){console.error("Error loading team members:",t)}},se=async()=>{f.value=!0;try{await new Promise(a=>setTimeout(a,2e3)),m.value=[{id:1,user_name:"Mario Rossi",description:"Ore eccessive nel weekend (16h sabato)",confidence:95,type:"weekend_overtime"},{id:2,user_name:"Giulia Bianchi",description:"Pattern insolito: 12h consecutive senza pause",confidence:87,type:"unusual_pattern"}]}catch(a){console.error("Error running anomaly detection:",a)}finally{f.value=!1}},ae=a=>{alert(`Anomalia: ${a.description}
Confidenza: ${a.confidence}%`)},re=async()=>{console.log("Bulk approve:",g.value),y.value=!1},oe=async()=>{console.log("Bulk reject:",g.value),y.value=!1},le=()=>{g.value.length===u.value.length?g.value=[]:g.value=u.value.map(a=>a.id)},E=a=>m.value.some(t=>{var i;return t.user_name===((i=a.user)==null?void 0:i.full_name)}),de=a=>a?a.split(" ").map(t=>t[0]).join("").toUpperCase():"??",F=a=>{const t={draft:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300",submitted:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",approved:"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",rejected:"bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400"};return t[a]||t.draft},O=a=>({draft:"Bozza",submitted:"Da Approvare",approved:"Approvato",rejected:"Rifiutato"})[a]||"Sconosciuto",ne=a=>a?new Date(a).toLocaleDateString("it-IT"):"-",ie=a=>{n.value=a,k.value=!0},P=async a=>{try{const t=await fetch(`/api/monthly-timesheets/${a.id}/approve`,{method:"PUT",headers:{"Content-Type":"application/json","X-CSRFToken":h.csrfToken}});if(t.ok)await x(),k.value=!1,alert("Timesheet approvato con successo!");else{const i=await t.json();alert(`Errore: ${i.message}`)}}catch(t){console.error("Error approving timesheet:",t),alert("Errore durante l'approvazione")}},N=a=>{n.value=a,b.value="",k.value=!1,w.value=!0},ue=async()=>{try{const a=await fetch(`/api/monthly-timesheets/${n.value.id}/reject`,{method:"PUT",headers:{"Content-Type":"application/json","X-CSRFToken":h.csrfToken},body:JSON.stringify({reason:b.value})});if(a.ok)await x(),w.value=!1,alert("Timesheet rifiutato");else{const t=await a.json();alert(`Errore: ${t.message}`)}}catch(a){console.error("Error rejecting timesheet:",a),alert("Errore durante il rifiuto")}};return ge(()=>{x(),te()}),(a,t)=>{var i,v,T,G,I,X,H,J,Q;return l(),o("div",ve,[e("div",pe,[e("div",me,[t[13]||(t[13]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Approvazioni Timesheet"),e("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Gestisci approvazioni con assistenza AI e operazioni bulk ")],-1)),e("div",ye,[e("button",{onClick:se,disabled:f.value,class:"bg-purple-600 hover:bg-purple-700 disabled:bg-purple-400 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center"},[t[12]||(t[12]=e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})],-1)),W(" "+r(f.value?"Analizzando...":"Rileva Anomalie AI"),1)],8,ke),g.value.length?(l(),o("div",be,[e("button",{onClick:t[0]||(t[0]=s=>y.value=!y.value),class:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"}," Azioni Multiple ("+r(g.value.length)+") ",1),y.value?(l(),o("div",he,[e("div",{class:"py-1"},[e("button",{onClick:re,class:"block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600"}," Approva Tutti "),e("button",{onClick:oe,class:"block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600"}," Rifiuta Tutti ")])])):c("",!0)])):c("",!0)])]),m.value.length?(l(),o("div",fe,[e("div",we,[t[14]||(t[14]=e("svg",{class:"w-5 h-5 text-red-400 mt-0.5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"})],-1)),e("div",_e,[e("h3",Ce," Anomalie Rilevate ("+r(m.value.length)+") ",1),e("div",Te,[(l(!0),o(A,null,M(m.value,s=>(l(),o("div",{key:s.id,class:"text-sm text-red-700 dark:text-red-300"},[W(" • "+r(s.user_name)+": "+r(s.description)+" ",1),e("button",{onClick:B=>ae(s),class:"ml-2 text-red-600 dark:text-red-400 underline"}," Dettagli ",8,je)]))),128))])])])])):c("",!0)]),e("div",Ae,[e("div",Me,[e("div",ze,[t[16]||(t[16]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-yellow-100 dark:bg-yellow-900/20 rounded-md flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-yellow-600 dark:text-yellow-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),e("div",Se,[t[15]||(t[15]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Da Approvare",-1)),e("p",De,r(R.value.pending),1)])])]),e("div",Re,[e("div",Be,[t[18]||(t[18]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-md flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})])])],-1)),e("div",$e,[t[17]||(t[17]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Approvati",-1)),e("p",Le,r(R.value.approved),1)])])]),e("div",Ue,[e("div",Ve,[t[20]||(t[20]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-red-100 dark:bg-red-900/20 rounded-md flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-red-600 dark:text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"})])])],-1)),e("div",Ee,[t[19]||(t[19]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Con Anomalie",-1)),e("p",Fe,r(m.value.length),1)])])]),e("div",Oe,[e("div",Pe,[t[22]||(t[22]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-md flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),e("div",Ne,[t[21]||(t[21]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Ore Totali",-1)),e("p",Ge,r(R.value.totalHours)+"h",1)])])])]),e("div",Ie,[e("div",Xe,[e("div",null,[t[23]||(t[23]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Mese",-1)),p(e("select",{"onUpdate:modelValue":t[1]||(t[1]=s=>D.value=s),onChange:x,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"},[(l(!0),o(A,null,M(ee.value,s=>(l(),o("option",{key:s.value,value:s.value},r(s.label),9,He))),128))],544),[[z,D.value]])]),e("div",null,[t[25]||(t[25]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Stato",-1)),p(e("select",{"onUpdate:modelValue":t[2]||(t[2]=s=>_.value=s),onChange:x,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"},t[24]||(t[24]=[e("option",{value:""},"Tutti",-1),e("option",{value:"submitted"},"Da Approvare",-1),e("option",{value:"approved"},"Approvati",-1),e("option",{value:"rejected"},"Rifiutati",-1)]),544),[[z,_.value]])]),e("div",null,[t[27]||(t[27]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Membro Team",-1)),p(e("select",{"onUpdate:modelValue":t[3]||(t[3]=s=>C.value=s),onChange:x,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"},[t[26]||(t[26]=e("option",{value:""},"Tutti",-1)),(l(!0),o(A,null,M(L.value,s=>(l(),o("option",{key:s.id,value:s.id},r(s.full_name),9,Je))),128))],544),[[z,C.value]])]),e("div",null,[t[29]||(t[29]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Anomalie",-1)),p(e("select",{"onUpdate:modelValue":t[4]||(t[4]=s=>U.value=s),onChange:x,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"},t[28]||(t[28]=[e("option",{value:!1},"Tutti",-1),e("option",{value:!0},"Solo con Anomalie",-1)]),544),[[z,U.value]])]),e("div",null,[t[30]||(t[30]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Cerca",-1)),p(e("input",{"onUpdate:modelValue":t[5]||(t[5]=s=>V.value=s),onInput:x,type:"text",placeholder:"Nome utente...",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"},null,544),[[Z,V.value]])])])]),e("div",Qe,[t[41]||(t[41]=e("div",{class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},[e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Timesheet da Approvare")],-1)),e("div",Ye,[e("table",qe,[e("thead",Ke,[e("tr",null,[e("th",We,[e("input",{type:"checkbox",onChange:le,checked:g.value.length===u.value.length&&u.value.length>0,class:"rounded border-gray-300 dark:border-gray-600"},null,40,Ze)]),t[31]||(t[31]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Dipendente ",-1)),t[32]||(t[32]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Periodo ",-1)),t[33]||(t[33]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Ore Totali ",-1)),t[34]||(t[34]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Ore Fatturabili ",-1)),t[35]||(t[35]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Stato ",-1)),t[36]||(t[36]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Sottomesso ",-1)),t[37]||(t[37]=e("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Azioni ",-1))])]),e("tbody",et,[S.value?(l(),o("tr",tt,t[38]||(t[38]=[e("td",{colspan:"8",class:"px-6 py-4 text-center text-gray-500 dark:text-gray-400"}," Caricamento... ",-1)]))):u.value.length===0?(l(),o("tr",st,t[39]||(t[39]=[e("td",{colspan:"8",class:"px-6 py-4 text-center text-gray-500 dark:text-gray-400"}," Nessun timesheet trovato ",-1)]))):(l(!0),o(A,{key:2},M(u.value,s=>{var B,Y,q;return l(),o("tr",{key:s.id,class:$({"bg-red-50 dark:bg-red-900/10":E(s)})},[e("td",at,[p(e("input",{type:"checkbox",value:s.id,"onUpdate:modelValue":t[6]||(t[6]=j=>g.value=j),class:"rounded border-gray-300 dark:border-gray-600"},null,8,rt),[[ce,g.value]])]),e("td",ot,[e("div",lt,[e("div",dt,[e("div",nt,[e("span",it,r(de((B=s.user)==null?void 0:B.full_name)),1)])]),e("div",ut,[e("div",gt,r((Y=s.user)==null?void 0:Y.full_name),1),e("div",ct,r((q=s.user)==null?void 0:q.email),1)]),E(s)?(l(),o("div",xt,t[40]||(t[40]=[e("svg",{class:"w-5 h-5 text-red-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"})],-1)]))):c("",!0)])]),e("td",vt,r(s.month)+"/"+r(s.year),1),e("td",pt,r(s.total_hours)+"h ",1),e("td",mt,r(s.billable_hours)+"h ",1),e("td",yt,[e("span",{class:$([F(s.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},r(O(s.status)),3)]),e("td",kt,r(ne(s.submission_date)),1),e("td",bt,[e("div",ht,[e("button",{onClick:j=>ie(s),class:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"}," Dettagli ",8,ft),s.status==="submitted"?(l(),o("button",{key:0,onClick:j=>P(s),class:"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"}," Approva ",8,wt)):c("",!0),s.status==="submitted"?(l(),o("button",{key:1,onClick:j=>N(s),class:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"}," Rifiuta ",8,_t)):c("",!0)])])],2)}),128))])])])]),k.value?(l(),o("div",Ct,[e("div",Tt,[e("div",jt,[e("div",At,[e("h3",Mt," Dettagli Timesheet - "+r((v=(i=n.value)==null?void 0:i.user)==null?void 0:v.full_name),1),e("button",{onClick:t[7]||(t[7]=s=>k.value=!1),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},t[42]||(t[42]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("div",zt,[e("div",St,[e("div",null,[t[43]||(t[43]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Periodo",-1)),e("p",Dt,r((T=n.value)==null?void 0:T.month)+"/"+r((G=n.value)==null?void 0:G.year),1)]),e("div",null,[t[44]||(t[44]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Stato",-1)),e("span",{class:$([F((I=n.value)==null?void 0:I.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},r(O((X=n.value)==null?void 0:X.status)),3)]),e("div",null,[t[45]||(t[45]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Ore Totali",-1)),e("p",Rt,r((H=n.value)==null?void 0:H.total_hours)+"h",1)]),e("div",null,[t[46]||(t[46]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Ore Fatturabili",-1)),e("p",Bt,r((J=n.value)==null?void 0:J.billable_hours)+"h",1)])]),((Q=n.value)==null?void 0:Q.status)==="submitted"?(l(),o("div",$t,[e("button",{onClick:t[8]||(t[8]=s=>P(n.value)),class:"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"}," Approva "),e("button",{onClick:t[9]||(t[9]=s=>N(n.value)),class:"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium"}," Rifiuta ")])):c("",!0)])])])])):c("",!0),w.value?(l(),o("div",Lt,[e("div",Ut,[e("div",Vt,[t[48]||(t[48]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Rifiuta Timesheet ",-1)),e("div",Et,[e("div",null,[t[47]||(t[47]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Motivo del rifiuto * ",-1)),p(e("textarea",{"onUpdate:modelValue":t[10]||(t[10]=s=>b.value=s),rows:"4",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white",placeholder:"Specifica il motivo del rifiuto..."},null,512),[[Z,b.value]])]),e("div",Ft,[e("button",{onClick:ue,disabled:!b.value.trim(),class:"bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white px-4 py-2 rounded-md text-sm font-medium"}," Conferma Rifiuto ",8,Ot),e("button",{onClick:t[11]||(t[11]=s=>w.value=!1),class:"bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium"}," Annulla ")])])])])])):c("",!0)])}}};export{It as default};

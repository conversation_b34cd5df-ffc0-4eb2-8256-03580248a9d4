import{r as n,f as K,A as ce,c as o,j as e,g,m as W,t as r,F as S,k as D,v as m,G as R,x as Z,n as V,o as l,C as ge}from"./vendor.js";import{u as xe}from"./app.js";const ve={class:"space-y-6"},pe={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},me={class:"flex items-center justify-between"},ye={class:"flex items-center space-x-3"},ke=["disabled"],be={key:0,class:"relative"},he={key:0,class:"absolute right-0 mt-2 w-48 bg-white dark:bg-gray-700 rounded-md shadow-lg z-10"},fe={key:0,class:"mt-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4"},we={class:"flex items-start"},_e={class:"ml-3"},Ce={class:"text-sm font-medium text-red-800 dark:text-red-200"},Ae={class:"mt-2 space-y-1"},Te=["onClick"],je={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},Me={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},ze={class:"flex items-center"},Se={class:"ml-4"},De={class:"text-2xl font-semibold text-gray-900 dark:text-white"},Re={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Be={class:"flex items-center"},$e={class:"ml-4"},Le={class:"text-2xl font-semibold text-gray-900 dark:text-white"},Fe={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Ue={class:"flex items-center"},Ve={class:"ml-4"},Ee={class:"text-2xl font-semibold text-gray-900 dark:text-white"},Oe={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Pe={class:"flex items-center"},Ne={class:"ml-4"},Ge={class:"text-2xl font-semibold text-gray-900 dark:text-white"},Ie={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-4"},Xe={class:"grid grid-cols-1 md:grid-cols-5 gap-4"},He=["value"],Je=["value"],Qe={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Ye={class:"overflow-x-auto"},qe={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},Ke={class:"bg-gray-50 dark:bg-gray-700"},We={class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"},Ze=["checked"],et={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},tt={key:0},st={key:1},at={class:"px-6 py-4 whitespace-nowrap"},rt=["value"],ot={class:"px-6 py-4 whitespace-nowrap"},lt={class:"flex items-center"},dt={class:"flex-shrink-0 h-10 w-10"},nt={class:"h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center"},it={class:"text-sm font-medium text-gray-700 dark:text-gray-300"},ut={class:"ml-4"},ct={class:"text-sm font-medium text-gray-900 dark:text-white"},gt={class:"text-sm text-gray-500 dark:text-gray-400"},xt={key:0,class:"ml-2"},vt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},pt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},mt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},yt={class:"px-6 py-4 whitespace-nowrap"},kt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"},bt={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},ht={class:"flex items-center justify-end space-x-2"},ft=["onClick"],wt=["onClick"],_t=["onClick"],Ct={key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},At={class:"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white dark:bg-gray-800"},Tt={class:"mt-3"},jt={class:"flex items-center justify-between mb-4"},Mt={class:"text-lg font-medium text-gray-900 dark:text-white"},zt={class:"space-y-4"},St={class:"grid grid-cols-2 gap-4"},Dt={class:"text-sm text-gray-900 dark:text-white"},Rt={class:"text-sm text-gray-900 dark:text-white"},Bt={class:"text-sm text-gray-900 dark:text-white"},$t={key:0,class:"flex space-x-3"},Lt={key:1,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},Ft={class:"relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 shadow-lg rounded-md bg-white dark:bg-gray-800"},Ut={class:"mt-3"},Vt={class:"space-y-4"},Et={class:"flex space-x-3"},Ot=["disabled"],It={__name:"TimesheetApprovals",setup(Pt){const f=xe(),i=n([]),p=n([]),y=n([]),c=n([]),u=n(null),B=n(!1),w=n(!1),k=n(!1),b=n(!1),_=n(!1),h=n(""),$=n(new Date().getMonth()+1),C=n("submitted"),A=n(""),L=n(!1),T=n(""),F=K(()=>{const s=Array.isArray(i.value)?i.value:[],t=s.filter(v=>v.status==="submitted").length,d=s.filter(v=>v.status==="approved").length,j=s.reduce((v,M)=>v+(M.total_hours||0),0);return{pending:t,approved:d,totalHours:j}}),ee=K(()=>[{value:1,label:"Gennaio"},{value:2,label:"Febbraio"},{value:3,label:"Marzo"},{value:4,label:"Aprile"},{value:5,label:"Maggio"},{value:6,label:"Giugno"},{value:7,label:"Luglio"},{value:8,label:"Agosto"},{value:9,label:"Settembre"},{value:10,label:"Ottobre"},{value:11,label:"Novembre"},{value:12,label:"Dicembre"}]),x=async()=>{B.value=!0;try{const s=new URLSearchParams({month:$.value,year:new Date().getFullYear()});C.value&&s.append("status",C.value),A.value&&s.append("user_id",A.value),T.value&&s.append("search",T.value),L.value&&s.append("anomalies_only","true");const t=await fetch(`/api/monthly-timesheets/?${s}`,{headers:{"Content-Type":"application/json","X-CSRFToken":f.csrfToken}});if(t.ok){const d=await t.json();d.data&&Array.isArray(d.data)?i.value=d.data:d.data&&Array.isArray(d.data.items)?i.value=d.data.items:i.value=[],console.log("Loaded timesheets:",i.value)}else console.error("Failed to load timesheets:",t.status),i.value=[]}catch(s){console.error("Error loading timesheets:",s),i.value=[]}finally{B.value=!1}},te=async()=>{try{const s=await fetch("/api/personnel/users",{headers:{"Content-Type":"application/json","X-CSRFToken":f.csrfToken}});if(s.ok){const t=await s.json();t.data&&Array.isArray(t.data.users)?p.value=t.data.users:t.data&&Array.isArray(t.data)?p.value=t.data:p.value=[],console.log("Loaded team members:",p.value)}else console.error("Failed to load team members:",s.status),p.value=[]}catch(s){console.error("Error loading team members:",s),p.value=[]}},se=async()=>{w.value=!0;try{await new Promise(s=>setTimeout(s,2e3)),y.value=[{id:1,user_name:"Mario Rossi",description:"Ore eccessive nel weekend (16h sabato)",confidence:95,type:"weekend_overtime"},{id:2,user_name:"Giulia Bianchi",description:"Pattern insolito: 12h consecutive senza pause",confidence:87,type:"unusual_pattern"}]}catch(s){console.error("Error running anomaly detection:",s)}finally{w.value=!1}},ae=s=>{alert(`Anomalia: ${s.description}
Confidenza: ${s.confidence}%`)},re=async()=>{console.log("Bulk approve:",c.value),k.value=!1},oe=async()=>{console.log("Bulk reject:",c.value),k.value=!1},le=()=>{c.value.length===i.value.length?c.value=[]:c.value=i.value.map(s=>s.id)},E=s=>y.value.some(t=>{var d;return t.user_name===((d=s.user)==null?void 0:d.full_name)}),de=s=>s?s.split(" ").map(t=>t[0]).join("").toUpperCase():"??",O=s=>{const t={draft:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300",submitted:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",approved:"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",rejected:"bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400"};return t[s]||t.draft},P=s=>({draft:"Bozza",submitted:"Da Approvare",approved:"Approvato",rejected:"Rifiutato"})[s]||"Sconosciuto",ne=s=>s?new Date(s).toLocaleDateString("it-IT"):"-",ie=s=>{u.value=s,b.value=!0},N=async s=>{try{const t=await fetch(`/api/monthly-timesheets/${s.id}/approve`,{method:"PUT",headers:{"Content-Type":"application/json","X-CSRFToken":f.csrfToken}});if(t.ok)await x(),b.value=!1,alert("Timesheet approvato con successo!");else{const d=await t.json();alert(`Errore: ${d.message}`)}}catch(t){console.error("Error approving timesheet:",t),alert("Errore durante l'approvazione")}},G=s=>{u.value=s,h.value="",b.value=!1,_.value=!0},ue=async()=>{try{const s=await fetch(`/api/monthly-timesheets/${u.value.id}/reject`,{method:"PUT",headers:{"Content-Type":"application/json","X-CSRFToken":f.csrfToken},body:JSON.stringify({reason:h.value})});if(s.ok)await x(),_.value=!1,alert("Timesheet rifiutato");else{const t=await s.json();alert(`Errore: ${t.message}`)}}catch(s){console.error("Error rejecting timesheet:",s),alert("Errore durante il rifiuto")}};return ce(()=>{x(),te()}),(s,t)=>{var d,j,v,M,I,X,H,J,Q;return l(),o("div",ve,[e("div",pe,[e("div",me,[t[13]||(t[13]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Approvazioni Timesheet"),e("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Gestisci approvazioni con assistenza AI e operazioni bulk ")],-1)),e("div",ye,[e("button",{onClick:se,disabled:w.value,class:"bg-purple-600 hover:bg-purple-700 disabled:bg-purple-400 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center"},[t[12]||(t[12]=e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})],-1)),W(" "+r(w.value?"Analizzando...":"Rileva Anomalie AI"),1)],8,ke),c.value.length?(l(),o("div",be,[e("button",{onClick:t[0]||(t[0]=a=>k.value=!k.value),class:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"}," Azioni Multiple ("+r(c.value.length)+") ",1),k.value?(l(),o("div",he,[e("div",{class:"py-1"},[e("button",{onClick:re,class:"block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600"}," Approva Tutti "),e("button",{onClick:oe,class:"block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600"}," Rifiuta Tutti ")])])):g("",!0)])):g("",!0)])]),y.value.length?(l(),o("div",fe,[e("div",we,[t[14]||(t[14]=e("svg",{class:"w-5 h-5 text-red-400 mt-0.5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"})],-1)),e("div",_e,[e("h3",Ce," Anomalie Rilevate ("+r(y.value.length)+") ",1),e("div",Ae,[(l(!0),o(S,null,D(y.value,a=>(l(),o("div",{key:a.id,class:"text-sm text-red-700 dark:text-red-300"},[W(" • "+r(a.user_name)+": "+r(a.description)+" ",1),e("button",{onClick:U=>ae(a),class:"ml-2 text-red-600 dark:text-red-400 underline"}," Dettagli ",8,Te)]))),128))])])])])):g("",!0)]),e("div",je,[e("div",Me,[e("div",ze,[t[16]||(t[16]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-yellow-100 dark:bg-yellow-900/20 rounded-md flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-yellow-600 dark:text-yellow-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),e("div",Se,[t[15]||(t[15]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Da Approvare",-1)),e("p",De,r(F.value.pending),1)])])]),e("div",Re,[e("div",Be,[t[18]||(t[18]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-md flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})])])],-1)),e("div",$e,[t[17]||(t[17]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Approvati",-1)),e("p",Le,r(F.value.approved),1)])])]),e("div",Fe,[e("div",Ue,[t[20]||(t[20]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-red-100 dark:bg-red-900/20 rounded-md flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-red-600 dark:text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"})])])],-1)),e("div",Ve,[t[19]||(t[19]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Con Anomalie",-1)),e("p",Ee,r(y.value.length),1)])])]),e("div",Oe,[e("div",Pe,[t[22]||(t[22]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-md flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),e("div",Ne,[t[21]||(t[21]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Ore Totali",-1)),e("p",Ge,r(F.value.totalHours)+"h",1)])])])]),e("div",Ie,[e("div",Xe,[e("div",null,[t[23]||(t[23]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Mese",-1)),m(e("select",{"onUpdate:modelValue":t[1]||(t[1]=a=>$.value=a),onChange:x,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"},[(l(!0),o(S,null,D(ee.value,a=>(l(),o("option",{key:a.value,value:a.value},r(a.label),9,He))),128))],544),[[R,$.value]])]),e("div",null,[t[25]||(t[25]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Stato",-1)),m(e("select",{"onUpdate:modelValue":t[2]||(t[2]=a=>C.value=a),onChange:x,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"},t[24]||(t[24]=[e("option",{value:""},"Tutti",-1),e("option",{value:"submitted"},"Da Approvare",-1),e("option",{value:"approved"},"Approvati",-1),e("option",{value:"rejected"},"Rifiutati",-1)]),544),[[R,C.value]])]),e("div",null,[t[27]||(t[27]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Membro Team",-1)),m(e("select",{"onUpdate:modelValue":t[3]||(t[3]=a=>A.value=a),onChange:x,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"},[t[26]||(t[26]=e("option",{value:""},"Tutti",-1)),(l(!0),o(S,null,D(p.value,a=>(l(),o("option",{key:a.id,value:a.id},r(a.full_name),9,Je))),128))],544),[[R,A.value]])]),e("div",null,[t[29]||(t[29]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Anomalie",-1)),m(e("select",{"onUpdate:modelValue":t[4]||(t[4]=a=>L.value=a),onChange:x,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"},t[28]||(t[28]=[e("option",{value:!1},"Tutti",-1),e("option",{value:!0},"Solo con Anomalie",-1)]),544),[[R,L.value]])]),e("div",null,[t[30]||(t[30]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Cerca",-1)),m(e("input",{"onUpdate:modelValue":t[5]||(t[5]=a=>T.value=a),onInput:x,type:"text",placeholder:"Nome utente...",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"},null,544),[[Z,T.value]])])])]),e("div",Qe,[t[41]||(t[41]=e("div",{class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},[e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Timesheet da Approvare")],-1)),e("div",Ye,[e("table",qe,[e("thead",Ke,[e("tr",null,[e("th",We,[e("input",{type:"checkbox",onChange:le,checked:c.value.length===i.value.length&&i.value.length>0,class:"rounded border-gray-300 dark:border-gray-600"},null,40,Ze)]),t[31]||(t[31]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Dipendente ",-1)),t[32]||(t[32]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Periodo ",-1)),t[33]||(t[33]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Ore Totali ",-1)),t[34]||(t[34]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Ore Fatturabili ",-1)),t[35]||(t[35]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Stato ",-1)),t[36]||(t[36]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Sottomesso ",-1)),t[37]||(t[37]=e("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Azioni ",-1))])]),e("tbody",et,[B.value?(l(),o("tr",tt,t[38]||(t[38]=[e("td",{colspan:"8",class:"px-6 py-4 text-center text-gray-500 dark:text-gray-400"}," Caricamento... ",-1)]))):i.value.length===0?(l(),o("tr",st,t[39]||(t[39]=[e("td",{colspan:"8",class:"px-6 py-4 text-center text-gray-500 dark:text-gray-400"}," Nessun timesheet trovato ",-1)]))):(l(!0),o(S,{key:2},D(i.value,a=>{var U,Y,q;return l(),o("tr",{key:a.id,class:V({"bg-red-50 dark:bg-red-900/10":E(a)})},[e("td",at,[m(e("input",{type:"checkbox",value:a.id,"onUpdate:modelValue":t[6]||(t[6]=z=>c.value=z),class:"rounded border-gray-300 dark:border-gray-600"},null,8,rt),[[ge,c.value]])]),e("td",ot,[e("div",lt,[e("div",dt,[e("div",nt,[e("span",it,r(de((U=a.user)==null?void 0:U.full_name)),1)])]),e("div",ut,[e("div",ct,r((Y=a.user)==null?void 0:Y.full_name),1),e("div",gt,r((q=a.user)==null?void 0:q.email),1)]),E(a)?(l(),o("div",xt,t[40]||(t[40]=[e("svg",{class:"w-5 h-5 text-red-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"})],-1)]))):g("",!0)])]),e("td",vt,r(a.month)+"/"+r(a.year),1),e("td",pt,r(a.total_hours)+"h ",1),e("td",mt,r(a.billable_hours)+"h ",1),e("td",yt,[e("span",{class:V([O(a.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},r(P(a.status)),3)]),e("td",kt,r(ne(a.submission_date)),1),e("td",bt,[e("div",ht,[e("button",{onClick:z=>ie(a),class:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"}," Dettagli ",8,ft),a.status==="submitted"?(l(),o("button",{key:0,onClick:z=>N(a),class:"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"}," Approva ",8,wt)):g("",!0),a.status==="submitted"?(l(),o("button",{key:1,onClick:z=>G(a),class:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"}," Rifiuta ",8,_t)):g("",!0)])])],2)}),128))])])])]),b.value?(l(),o("div",Ct,[e("div",At,[e("div",Tt,[e("div",jt,[e("h3",Mt," Dettagli Timesheet - "+r((j=(d=u.value)==null?void 0:d.user)==null?void 0:j.full_name),1),e("button",{onClick:t[7]||(t[7]=a=>b.value=!1),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},t[42]||(t[42]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("div",zt,[e("div",St,[e("div",null,[t[43]||(t[43]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Periodo",-1)),e("p",Dt,r((v=u.value)==null?void 0:v.month)+"/"+r((M=u.value)==null?void 0:M.year),1)]),e("div",null,[t[44]||(t[44]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Stato",-1)),e("span",{class:V([O((I=u.value)==null?void 0:I.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},r(P((X=u.value)==null?void 0:X.status)),3)]),e("div",null,[t[45]||(t[45]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Ore Totali",-1)),e("p",Rt,r((H=u.value)==null?void 0:H.total_hours)+"h",1)]),e("div",null,[t[46]||(t[46]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Ore Fatturabili",-1)),e("p",Bt,r((J=u.value)==null?void 0:J.billable_hours)+"h",1)])]),((Q=u.value)==null?void 0:Q.status)==="submitted"?(l(),o("div",$t,[e("button",{onClick:t[8]||(t[8]=a=>N(u.value)),class:"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"}," Approva "),e("button",{onClick:t[9]||(t[9]=a=>G(u.value)),class:"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium"}," Rifiuta ")])):g("",!0)])])])])):g("",!0),_.value?(l(),o("div",Lt,[e("div",Ft,[e("div",Ut,[t[48]||(t[48]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Rifiuta Timesheet ",-1)),e("div",Vt,[e("div",null,[t[47]||(t[47]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Motivo del rifiuto * ",-1)),m(e("textarea",{"onUpdate:modelValue":t[10]||(t[10]=a=>h.value=a),rows:"4",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white",placeholder:"Specifica il motivo del rifiuto..."},null,512),[[Z,h.value]])]),e("div",Et,[e("button",{onClick:ue,disabled:!h.value.trim(),class:"bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white px-4 py-2 rounded-md text-sm font-medium"}," Conferma Rifiuto ",8,Ot),e("button",{onClick:t[11]||(t[11]=a=>_.value=!1),class:"bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium"}," Annulla ")])])])])])):g("",!0)])}}};export{It as default};

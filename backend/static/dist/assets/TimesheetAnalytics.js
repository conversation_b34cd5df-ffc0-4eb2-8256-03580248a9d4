import{r as u,A as N,c,j as t,m as v,t as r,v as j,G as D,H as G,F as T,k as F,n as m,o as p,z as $}from"./vendor.js";import{u as X}from"./app.js";const Q={class:"space-y-6"},J={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},K={class:"flex items-center justify-between"},W={class:"flex items-center space-x-3"},Z=["disabled"],tt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-4"},et={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},at=["value"],st=["value"],rt={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},ot={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},nt={class:"flex items-center"},lt={class:"ml-4"},it={class:"text-2xl font-semibold text-gray-900 dark:text-white"},dt={class:"text-xs text-gray-500 dark:text-gray-400"},ut={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},gt={class:"flex items-center"},ct={class:"ml-4"},pt={class:"text-2xl font-semibold text-gray-900 dark:text-white"},xt={class:"text-xs text-gray-500 dark:text-gray-400"},vt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},mt={class:"flex items-center"},ht={class:"ml-4"},yt={class:"text-2xl font-semibold text-gray-900 dark:text-white"},kt={class:"text-xs text-gray-500 dark:text-gray-400"},bt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},wt={class:"flex items-center"},ft={class:"ml-4"},_t={class:"text-2xl font-semibold text-gray-900 dark:text-white"},Ct={class:"text-xs text-gray-500 dark:text-gray-400"},jt={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},Dt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Mt={class:"flex items-center justify-between mb-4"},zt={class:"flex items-center space-x-2"},Tt={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Ft={class:"overflow-x-auto"},Pt={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},St={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},At={key:0},Ht={key:1},Rt={class:"px-6 py-4 whitespace-nowrap"},Vt={class:"flex items-center"},Yt={class:"flex-shrink-0 h-10 w-10"},Bt={class:"h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center"},Ut={class:"text-sm font-medium text-gray-700 dark:text-gray-300"},It={class:"ml-4"},Lt={class:"text-sm font-medium text-gray-900 dark:text-white"},Et={class:"text-sm text-gray-500 dark:text-gray-400"},Ot={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},qt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},Nt={class:"text-xs text-gray-500 dark:text-gray-400"},Gt={class:"px-6 py-4 whitespace-nowrap"},$t={class:"flex items-center"},Xt={class:"flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2"},Qt={class:"text-sm text-gray-900 dark:text-white"},Jt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},Kt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},ee={__name:"TimesheetAnalytics",setup(Wt){const M=X(),k=u(!1),b=u([]),w=u([]),d=u([]),f=u("month"),z=u("current_month"),_=u(""),C=u(""),P=u("productivity"),l=u({totalHours:0,totalHoursChange:0,avgProductivity:0,productivityChange:0,revenue:0,revenueChange:0,utilization:0,utilizationChange:0}),Y=()=>{const a=new Date;let e,s;switch(z.value){case"current_month":e=new Date(a.getFullYear(),a.getMonth(),1),s=new Date(a.getFullYear(),a.getMonth()+1,0);break;case"last_month":e=new Date(a.getFullYear(),a.getMonth()-1,1),s=new Date(a.getFullYear(),a.getMonth(),0);break;case"current_quarter":const g=Math.floor(a.getMonth()/3);e=new Date(a.getFullYear(),g*3,1),s=new Date(a.getFullYear(),(g+1)*3,0);break;case"last_quarter":const y=Math.floor(a.getMonth()/3)-1,i=y<0?a.getFullYear()-1:a.getFullYear(),o=y<0?3:y;e=new Date(i,o*3,1),s=new Date(i,(o+1)*3,0);break;case"current_year":e=new Date(a.getFullYear(),0,1),s=new Date(a.getFullYear(),11,31);break;default:e=new Date(a.getFullYear(),a.getMonth(),1),s=new Date(a.getFullYear(),a.getMonth()+1,0)}return{startDate:e.toISOString().split("T")[0],endDate:s.toISOString().split("T")[0]}},B=()=>{const a=d.value.reduce((i,o)=>i+o.total_hours,0),e=d.value.reduce((i,o)=>i+o.billable_hours,0),s=d.value.reduce((i,o)=>i+o.revenue,0),g=d.value.length>0?d.value.reduce((i,o)=>i+o.productivity,0)/d.value.length:0,y=a>0?e/a*100:0;l.value={totalHours:Math.round(a),totalHoursChange:0,avgProductivity:Math.round(g),productivityChange:0,revenue:Math.round(s),revenueChange:0,utilization:Math.round(y),utilizationChange:0}},h=async()=>{k.value=!0;try{await Promise.all([U(),I(),L()]),B()}finally{k.value=!1}},U=async()=>{try{const{startDate:a,endDate:e}=Y();console.log("Loading analytics for date range:",a,"to",e);const s=new URLSearchParams({start_date:a,end_date:e});_.value&&s.append("department_id",_.value),C.value&&s.append("project_id",C.value),console.log("Fetching timesheets with params:",s.toString());const g=await fetch(`/api/timesheets/?${s}`,{headers:{"Content-Type":"application/json","X-CSRFToken":M.csrfToken}});if(g.ok){const i=(await g.json()).data||[];console.log("Received timesheets:",i.length,"entries"),console.log("Sample timesheet entry:",i[0]);const o={};i.forEach(n=>{var A,H,R,V;const x=n.user_id||((A=n.user)==null?void 0:A.id)||"unknown";o[x]||(o[x]={id:x,full_name:((H=n.user)==null?void 0:H.full_name)||((R=n.user)==null?void 0:R.name)||`User ${x}`,department:((V=n.user)==null?void 0:V.department)||"N/A",total_hours:0,billable_hours:0,projects:new Set,entries:[]}),o[x].total_hours+=n.hours,n.billable&&(o[x].billable_hours+=n.hours),n.project_id&&o[x].projects.add(n.project_id),o[x].entries.push(n)}),d.value=Object.values(o).map(n=>({...n,active_projects:n.projects.size,productivity:n.total_hours>0?Math.round(n.billable_hours/n.total_hours*100):0,revenue:Math.round(n.billable_hours*50)})),console.log("Final analytics data:",d.value)}else console.error("Failed to load analytics data:",g.status,g.statusText),d.value=[]}catch(a){console.error("Error loading analytics data:",a),d.value=[]}},I=async()=>{try{const a=await fetch("/api/departments/",{headers:{"Content-Type":"application/json","X-CSRFToken":M.csrfToken}});if(a.ok){const e=await a.json();b.value=e.data||[],console.log("Loaded departments:",b.value)}else console.log("Departments API not available, using mock data"),b.value=[{id:1,name:"Sviluppo"},{id:2,name:"Design"},{id:3,name:"Marketing"},{id:4,name:"Amministrazione"}]}catch(a){console.log("Error loading departments, using mock data:",a),b.value=[{id:1,name:"Sviluppo"},{id:2,name:"Design"},{id:3,name:"Marketing"},{id:4,name:"Amministrazione"}]}},L=async()=>{try{console.log("Loading projects...");const a=await fetch("/api/projects/",{headers:{"Content-Type":"application/json","X-CSRFToken":M.csrfToken}});if(a.ok){const e=await a.json();w.value=e.data||[],console.log("Loaded projects:",w.value)}else console.error("Failed to load projects:",a.status),w.value=[]}catch(a){console.error("Error loading projects:",a),w.value=[]}},E=()=>{alert("Funzionalità di export in sviluppo")},O=a=>a?a.split(" ").map(e=>e[0]).join("").toUpperCase():"??",q=a=>a>=90?"bg-green-500":a>=75?"bg-yellow-500":"bg-red-500",S=a=>new Intl.NumberFormat("it-IT").format(a);return N(()=>{h()}),(a,e)=>(p(),c("div",Q,[t("div",J,[t("div",K,[e[8]||(e[8]=t("div",null,[t("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Analytics Timesheet"),t("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Report avanzati e analisi delle performance del team ")],-1)),t("div",W,[t("button",{onClick:E,class:"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center"},e[6]||(e[6]=[t("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),v(" Esporta Report ")])),t("button",{onClick:h,disabled:k.value,class:"bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center"},[e[7]||(e[7]=t("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})],-1)),v(" "+r(k.value?"Caricando...":"Aggiorna"),1)],8,Z)])])]),t("div",tt,[t("div",et,[t("div",null,[e[10]||(e[10]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Periodo",-1)),j(t("select",{"onUpdate:modelValue":e[0]||(e[0]=s=>z.value=s),onChange:h,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"},e[9]||(e[9]=[G('<option value="current_month">Mese Corrente</option><option value="last_month">Mese Scorso</option><option value="current_quarter">Trimestre Corrente</option><option value="last_quarter">Trimestre Scorso</option><option value="current_year">Anno Corrente</option><option value="custom">Personalizzato</option>',6)]),544),[[D,z.value]])]),t("div",null,[e[12]||(e[12]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Dipartimento",-1)),j(t("select",{"onUpdate:modelValue":e[1]||(e[1]=s=>_.value=s),onChange:h,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"},[e[11]||(e[11]=t("option",{value:""},"Tutti",-1)),(p(!0),c(T,null,F(b.value,s=>(p(),c("option",{key:s.id,value:s.id},r(s.name),9,at))),128))],544),[[D,_.value]])]),t("div",null,[e[14]||(e[14]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Progetto",-1)),j(t("select",{"onUpdate:modelValue":e[2]||(e[2]=s=>C.value=s),onChange:h,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"},[e[13]||(e[13]=t("option",{value:""},"Tutti",-1)),(p(!0),c(T,null,F(w.value,s=>(p(),c("option",{key:s.id,value:s.id},r(s.name),9,st))),128))],544),[[D,C.value]])]),t("div",null,[e[16]||(e[16]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Tipo Analisi",-1)),j(t("select",{"onUpdate:modelValue":e[3]||(e[3]=s=>P.value=s),onChange:h,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"},e[15]||(e[15]=[t("option",{value:"productivity"},"Produttività",-1),t("option",{value:"utilization"},"Utilizzo",-1),t("option",{value:"billing"},"Fatturazione",-1),t("option",{value:"trends"},"Trend",-1)]),544),[[D,P.value]])])])]),t("div",rt,[t("div",ot,[t("div",nt,[e[19]||(e[19]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",lt,[e[18]||(e[18]=t("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Ore Totali",-1)),t("p",it,r(l.value.totalHours)+"h",1),t("p",dt,[t("span",{class:m(l.value.totalHoursChange>=0?"text-green-600":"text-red-600")},r(l.value.totalHoursChange>=0?"+":"")+r(l.value.totalHoursChange)+"% ",3),e[17]||(e[17]=v(" vs periodo precedente "))])])])]),t("div",ut,[t("div",gt,[e[22]||(e[22]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])])],-1)),t("div",ct,[e[21]||(e[21]=t("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Produttività Media",-1)),t("p",pt,r(l.value.avgProductivity)+"%",1),t("p",xt,[t("span",{class:m(l.value.productivityChange>=0?"text-green-600":"text-red-600")},r(l.value.productivityChange>=0?"+":"")+r(l.value.productivityChange)+"% ",3),e[20]||(e[20]=v(" vs periodo precedente "))])])])]),t("div",vt,[t("div",mt,[e[25]||(e[25]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-yellow-100 dark:bg-yellow-900/20 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-yellow-600 dark:text-yellow-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])])],-1)),t("div",ht,[e[24]||(e[24]=t("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Revenue Generato",-1)),t("p",yt,"€"+r(S(l.value.revenue)),1),t("p",kt,[t("span",{class:m(l.value.revenueChange>=0?"text-green-600":"text-red-600")},r(l.value.revenueChange>=0?"+":"")+r(l.value.revenueChange)+"% ",3),e[23]||(e[23]=v(" vs periodo precedente "))])])])]),t("div",bt,[t("div",wt,[e[28]||(e[28]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-purple-600 dark:text-purple-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})])])],-1)),t("div",ft,[e[27]||(e[27]=t("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Utilizzo Risorse",-1)),t("p",_t,r(l.value.utilization)+"%",1),t("p",Ct,[t("span",{class:m(l.value.utilizationChange>=0?"text-green-600":"text-red-600")},r(l.value.utilizationChange>=0?"+":"")+r(l.value.utilizationChange)+"% ",3),e[26]||(e[26]=v(" vs periodo precedente "))])])])])]),t("div",jt,[t("div",Dt,[t("div",Mt,[e[29]||(e[29]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Trend Produttività",-1)),t("div",zt,[t("button",{onClick:e[4]||(e[4]=s=>f.value="week"),class:m([f.value==="week"?"bg-blue-100 text-blue-700":"text-gray-500","px-3 py-1 rounded text-sm"])}," Settimana ",2),t("button",{onClick:e[5]||(e[5]=s=>f.value="month"),class:m([f.value==="month"?"bg-blue-100 text-blue-700":"text-gray-500","px-3 py-1 rounded text-sm"])}," Mese ",2)])]),e[30]||(e[30]=t("div",{class:"h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded"},[t("p",{class:"text-gray-500 dark:text-gray-400"},"Grafico Produttività (Chart.js placeholder)")],-1))]),e[31]||(e[31]=t("div",{class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},[t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Performance Team"),t("div",{class:"h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded"},[t("p",{class:"text-gray-500 dark:text-gray-400"},"Grafico Performance Team (Chart.js placeholder)")])],-1))]),t("div",Tt,[e[35]||(e[35]=t("div",{class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},[t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Analisi Dettagliata per Dipendente")],-1)),t("div",Ft,[t("table",Pt,[e[34]||(e[34]=t("thead",{class:"bg-gray-50 dark:bg-gray-700"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Dipendente "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Ore Lavorate "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Ore Fatturabili "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Produttività "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Revenue "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Progetti Attivi ")])],-1)),t("tbody",St,[k.value?(p(),c("tr",At,e[32]||(e[32]=[t("td",{colspan:"6",class:"px-6 py-4 text-center text-gray-500 dark:text-gray-400"}," Caricamento dati analytics... ",-1)]))):d.value.length===0?(p(),c("tr",Ht,e[33]||(e[33]=[t("td",{colspan:"6",class:"px-6 py-4 text-center text-gray-500 dark:text-gray-400"}," Nessun dato disponibile per il periodo selezionato ",-1)]))):(p(!0),c(T,{key:2},F(d.value,s=>(p(),c("tr",{key:s.id},[t("td",Rt,[t("div",Vt,[t("div",Yt,[t("div",Bt,[t("span",Ut,r(O(s.full_name)),1)])]),t("div",It,[t("div",Lt,r(s.full_name),1),t("div",Et,r(s.department),1)])])]),t("td",Ot,r(s.total_hours)+"h ",1),t("td",qt,[v(r(s.billable_hours)+"h ",1),t("span",Nt," ("+r(Math.round(s.billable_hours/s.total_hours*100))+"%) ",1)]),t("td",Gt,[t("div",$t,[t("div",Xt,[t("div",{class:m(["h-2 rounded-full",q(s.productivity)]),style:$({width:s.productivity+"%"})},null,6)]),t("span",Qt,r(s.productivity)+"%",1)])]),t("td",Jt," €"+r(S(s.revenue)),1),t("td",Kt,r(s.active_projects),1)]))),128))])])])])]))}};export{ee as default};

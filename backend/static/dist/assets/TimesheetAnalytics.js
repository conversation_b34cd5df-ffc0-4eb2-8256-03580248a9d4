import{r as o,A as V,c as i,j as t,m as n,t as r,v as p,G as v,H as B,F as y,k as h,n as u,o as d,z as H}from"./vendor.js";import{u as S}from"./app.js";const U={class:"space-y-6"},F={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},G={class:"flex items-center justify-between"},R={class:"flex items-center space-x-3"},N=["disabled"],I={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-4"},O={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},q=["value"],E=["value"],L={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},$={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},J={class:"flex items-center"},K={class:"ml-4"},Q={class:"text-2xl font-semibold text-gray-900 dark:text-white"},W={class:"text-xs text-gray-500 dark:text-gray-400"},X={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Y={class:"flex items-center"},Z={class:"ml-4"},tt={class:"text-2xl font-semibold text-gray-900 dark:text-white"},et={class:"text-xs text-gray-500 dark:text-gray-400"},st={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},rt={class:"flex items-center"},at={class:"ml-4"},ot={class:"text-2xl font-semibold text-gray-900 dark:text-white"},it={class:"text-xs text-gray-500 dark:text-gray-400"},dt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},lt={class:"flex items-center"},nt={class:"ml-4"},ut={class:"text-2xl font-semibold text-gray-900 dark:text-white"},gt={class:"text-xs text-gray-500 dark:text-gray-400"},xt={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},ct={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},pt={class:"flex items-center justify-between mb-4"},vt={class:"flex items-center space-x-2"},mt={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},yt={class:"overflow-x-auto"},ht={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},kt={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},bt={key:0},ft={key:1},wt={class:"px-6 py-4 whitespace-nowrap"},_t={class:"flex items-center"},Ct={class:"flex-shrink-0 h-10 w-10"},zt={class:"h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center"},jt={class:"text-sm font-medium text-gray-700 dark:text-gray-300"},Pt={class:"ml-4"},Mt={class:"text-sm font-medium text-gray-900 dark:text-white"},Tt={class:"text-sm text-gray-500 dark:text-gray-400"},Dt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},At={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},Vt={class:"text-xs text-gray-500 dark:text-gray-400"},Bt={class:"px-6 py-4 whitespace-nowrap"},Ht={class:"flex items-center"},St={class:"flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2"},Ut={class:"text-sm text-gray-900 dark:text-white"},Ft={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},Gt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},Ot={__name:"TimesheetAnalytics",setup(Rt){S();const x=o(!1),k=o([]),b=o([]),m=o([]),c=o("month"),f=o("current_month"),w=o(""),_=o(""),C=o("productivity"),a=o({totalHours:1247,totalHoursChange:12.5,avgProductivity:87,productivityChange:3.2,revenue:45680,revenueChange:8.7,utilization:92,utilizationChange:-1.3}),g=async()=>{x.value=!0;try{await Promise.all([j(),P(),M()])}finally{x.value=!1}},j=async()=>{m.value=[{id:1,full_name:"Mario Rossi",department:"Sviluppo",total_hours:160,billable_hours:144,productivity:90,revenue:7200,active_projects:3},{id:2,full_name:"Giulia Bianchi",department:"Design",total_hours:155,billable_hours:140,productivity:85,revenue:6800,active_projects:2}]},P=async()=>{k.value=[{id:1,name:"Sviluppo"},{id:2,name:"Design"},{id:3,name:"Marketing"}]},M=async()=>{b.value=[{id:1,name:"Progetto Alpha"},{id:2,name:"Progetto Beta"},{id:3,name:"Progetto Gamma"}]},T=()=>{alert("Funzionalità di export in sviluppo")},D=l=>l?l.split(" ").map(e=>e[0]).join("").toUpperCase():"??",A=l=>l>=90?"bg-green-500":l>=75?"bg-yellow-500":"bg-red-500",z=l=>new Intl.NumberFormat("it-IT").format(l);return V(()=>{g()}),(l,e)=>(d(),i("div",U,[t("div",F,[t("div",G,[e[8]||(e[8]=t("div",null,[t("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Analytics Timesheet"),t("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Report avanzati e analisi delle performance del team ")],-1)),t("div",R,[t("button",{onClick:T,class:"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center"},e[6]||(e[6]=[t("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),n(" Esporta Report ")])),t("button",{onClick:g,disabled:x.value,class:"bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center"},[e[7]||(e[7]=t("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})],-1)),n(" "+r(x.value?"Caricando...":"Aggiorna"),1)],8,N)])])]),t("div",I,[t("div",O,[t("div",null,[e[10]||(e[10]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Periodo",-1)),p(t("select",{"onUpdate:modelValue":e[0]||(e[0]=s=>f.value=s),onChange:g,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"},e[9]||(e[9]=[B('<option value="current_month">Mese Corrente</option><option value="last_month">Mese Scorso</option><option value="current_quarter">Trimestre Corrente</option><option value="last_quarter">Trimestre Scorso</option><option value="current_year">Anno Corrente</option><option value="custom">Personalizzato</option>',6)]),544),[[v,f.value]])]),t("div",null,[e[12]||(e[12]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Dipartimento",-1)),p(t("select",{"onUpdate:modelValue":e[1]||(e[1]=s=>w.value=s),onChange:g,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"},[e[11]||(e[11]=t("option",{value:""},"Tutti",-1)),(d(!0),i(y,null,h(k.value,s=>(d(),i("option",{key:s.id,value:s.id},r(s.name),9,q))),128))],544),[[v,w.value]])]),t("div",null,[e[14]||(e[14]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Progetto",-1)),p(t("select",{"onUpdate:modelValue":e[2]||(e[2]=s=>_.value=s),onChange:g,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"},[e[13]||(e[13]=t("option",{value:""},"Tutti",-1)),(d(!0),i(y,null,h(b.value,s=>(d(),i("option",{key:s.id,value:s.id},r(s.name),9,E))),128))],544),[[v,_.value]])]),t("div",null,[e[16]||(e[16]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Tipo Analisi",-1)),p(t("select",{"onUpdate:modelValue":e[3]||(e[3]=s=>C.value=s),onChange:g,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"},e[15]||(e[15]=[t("option",{value:"productivity"},"Produttività",-1),t("option",{value:"utilization"},"Utilizzo",-1),t("option",{value:"billing"},"Fatturazione",-1),t("option",{value:"trends"},"Trend",-1)]),544),[[v,C.value]])])])]),t("div",L,[t("div",$,[t("div",J,[e[19]||(e[19]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",K,[e[18]||(e[18]=t("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Ore Totali",-1)),t("p",Q,r(a.value.totalHours)+"h",1),t("p",W,[t("span",{class:u(a.value.totalHoursChange>=0?"text-green-600":"text-red-600")},r(a.value.totalHoursChange>=0?"+":"")+r(a.value.totalHoursChange)+"% ",3),e[17]||(e[17]=n(" vs periodo precedente "))])])])]),t("div",X,[t("div",Y,[e[22]||(e[22]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])])],-1)),t("div",Z,[e[21]||(e[21]=t("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Produttività Media",-1)),t("p",tt,r(a.value.avgProductivity)+"%",1),t("p",et,[t("span",{class:u(a.value.productivityChange>=0?"text-green-600":"text-red-600")},r(a.value.productivityChange>=0?"+":"")+r(a.value.productivityChange)+"% ",3),e[20]||(e[20]=n(" vs periodo precedente "))])])])]),t("div",st,[t("div",rt,[e[25]||(e[25]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-yellow-100 dark:bg-yellow-900/20 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-yellow-600 dark:text-yellow-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])])],-1)),t("div",at,[e[24]||(e[24]=t("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Revenue Generato",-1)),t("p",ot,"€"+r(z(a.value.revenue)),1),t("p",it,[t("span",{class:u(a.value.revenueChange>=0?"text-green-600":"text-red-600")},r(a.value.revenueChange>=0?"+":"")+r(a.value.revenueChange)+"% ",3),e[23]||(e[23]=n(" vs periodo precedente "))])])])]),t("div",dt,[t("div",lt,[e[28]||(e[28]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-purple-600 dark:text-purple-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})])])],-1)),t("div",nt,[e[27]||(e[27]=t("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Utilizzo Risorse",-1)),t("p",ut,r(a.value.utilization)+"%",1),t("p",gt,[t("span",{class:u(a.value.utilizationChange>=0?"text-green-600":"text-red-600")},r(a.value.utilizationChange>=0?"+":"")+r(a.value.utilizationChange)+"% ",3),e[26]||(e[26]=n(" vs periodo precedente "))])])])])]),t("div",xt,[t("div",ct,[t("div",pt,[e[29]||(e[29]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Trend Produttività",-1)),t("div",vt,[t("button",{onClick:e[4]||(e[4]=s=>c.value="week"),class:u([c.value==="week"?"bg-blue-100 text-blue-700":"text-gray-500","px-3 py-1 rounded text-sm"])}," Settimana ",2),t("button",{onClick:e[5]||(e[5]=s=>c.value="month"),class:u([c.value==="month"?"bg-blue-100 text-blue-700":"text-gray-500","px-3 py-1 rounded text-sm"])}," Mese ",2)])]),e[30]||(e[30]=t("div",{class:"h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded"},[t("p",{class:"text-gray-500 dark:text-gray-400"},"Grafico Produttività (Chart.js placeholder)")],-1))]),e[31]||(e[31]=t("div",{class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},[t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Performance Team"),t("div",{class:"h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded"},[t("p",{class:"text-gray-500 dark:text-gray-400"},"Grafico Performance Team (Chart.js placeholder)")])],-1))]),t("div",mt,[e[35]||(e[35]=t("div",{class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},[t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Analisi Dettagliata per Dipendente")],-1)),t("div",yt,[t("table",ht,[e[34]||(e[34]=t("thead",{class:"bg-gray-50 dark:bg-gray-700"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Dipendente "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Ore Lavorate "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Ore Fatturabili "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Produttività "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Revenue "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Progetti Attivi ")])],-1)),t("tbody",kt,[x.value?(d(),i("tr",bt,e[32]||(e[32]=[t("td",{colspan:"6",class:"px-6 py-4 text-center text-gray-500 dark:text-gray-400"}," Caricamento dati analytics... ",-1)]))):m.value.length===0?(d(),i("tr",ft,e[33]||(e[33]=[t("td",{colspan:"6",class:"px-6 py-4 text-center text-gray-500 dark:text-gray-400"}," Nessun dato disponibile per il periodo selezionato ",-1)]))):(d(!0),i(y,{key:2},h(m.value,s=>(d(),i("tr",{key:s.id},[t("td",wt,[t("div",_t,[t("div",Ct,[t("div",zt,[t("span",jt,r(D(s.full_name)),1)])]),t("div",Pt,[t("div",Mt,r(s.full_name),1),t("div",Tt,r(s.department),1)])])]),t("td",Dt,r(s.total_hours)+"h ",1),t("td",At,[n(r(s.billable_hours)+"h ",1),t("span",Vt," ("+r(Math.round(s.billable_hours/s.total_hours*100))+"%) ",1)]),t("td",Bt,[t("div",Ht,[t("div",St,[t("div",{class:u(["h-2 rounded-full",A(s.productivity)]),style:H({width:s.productivity+"%"})},null,6)]),t("span",Ut,r(s.productivity)+"%",1)])]),t("td",Ft," €"+r(z(s.revenue)),1),t("td",Gt,r(s.active_projects),1)]))),128))])])])])]))}};export{Ot as default};

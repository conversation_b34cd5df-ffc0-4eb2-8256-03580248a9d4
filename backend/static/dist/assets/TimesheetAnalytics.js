import{r as u,A as q,c as g,j as t,m as v,t as s,v as _,G as C,H as N,F as T,k as F,n as h,o as c,z as G}from"./vendor.js";import{u as L}from"./app.js";const X={class:"space-y-6"},$={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Q={class:"flex items-center justify-between"},J={class:"flex items-center space-x-3"},K=["disabled"],W={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-4"},Z={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},tt=["value"],et=["value"],at={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},rt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},st={class:"flex items-center"},ot={class:"ml-4"},nt={class:"text-2xl font-semibold text-gray-900 dark:text-white"},lt={class:"text-xs text-gray-500 dark:text-gray-400"},it={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},dt={class:"flex items-center"},ut={class:"ml-4"},gt={class:"text-2xl font-semibold text-gray-900 dark:text-white"},ct={class:"text-xs text-gray-500 dark:text-gray-400"},xt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},pt={class:"flex items-center"},vt={class:"ml-4"},ht={class:"text-2xl font-semibold text-gray-900 dark:text-white"},mt={class:"text-xs text-gray-500 dark:text-gray-400"},yt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},kt={class:"flex items-center"},bt={class:"ml-4"},wt={class:"text-2xl font-semibold text-gray-900 dark:text-white"},ft={class:"text-xs text-gray-500 dark:text-gray-400"},_t={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},Ct={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},jt={class:"flex items-center justify-between mb-4"},Dt={class:"flex items-center space-x-2"},Mt={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},zt={class:"overflow-x-auto"},Tt={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},Ft={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},Pt={key:0},St={key:1},Ht={class:"px-6 py-4 whitespace-nowrap"},At={class:"flex items-center"},Vt={class:"flex-shrink-0 h-10 w-10"},Yt={class:"h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center"},Rt={class:"text-sm font-medium text-gray-700 dark:text-gray-300"},Bt={class:"ml-4"},Ut={class:"text-sm font-medium text-gray-900 dark:text-white"},Et={class:"text-sm text-gray-500 dark:text-gray-400"},It={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},Ot={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},qt={class:"text-xs text-gray-500 dark:text-gray-400"},Nt={class:"px-6 py-4 whitespace-nowrap"},Gt={class:"flex items-center"},Lt={class:"flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2"},Xt={class:"text-sm text-gray-900 dark:text-white"},$t={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},Qt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},Zt={__name:"TimesheetAnalytics",setup(Jt){const j=L(),k=u(!1),D=u([]),M=u([]),d=u([]),b=u("month"),z=u("current_month"),w=u(""),f=u(""),P=u("productivity"),n=u({totalHours:0,totalHoursChange:0,avgProductivity:0,productivityChange:0,revenue:0,revenueChange:0,utilization:0,utilizationChange:0}),V=()=>{const a=new Date;let e,r;switch(z.value){case"current_month":e=new Date(a.getFullYear(),a.getMonth(),1),r=new Date(a.getFullYear(),a.getMonth()+1,0);break;case"last_month":e=new Date(a.getFullYear(),a.getMonth()-1,1),r=new Date(a.getFullYear(),a.getMonth(),0);break;case"current_quarter":const x=Math.floor(a.getMonth()/3);e=new Date(a.getFullYear(),x*3,1),r=new Date(a.getFullYear(),(x+1)*3,0);break;case"last_quarter":const y=Math.floor(a.getMonth()/3)-1,i=y<0?a.getFullYear()-1:a.getFullYear(),o=y<0?3:y;e=new Date(i,o*3,1),r=new Date(i,(o+1)*3,0);break;case"current_year":e=new Date(a.getFullYear(),0,1),r=new Date(a.getFullYear(),11,31);break;default:e=new Date(a.getFullYear(),a.getMonth(),1),r=new Date(a.getFullYear(),a.getMonth()+1,0)}return{startDate:e.toISOString().split("T")[0],endDate:r.toISOString().split("T")[0]}},Y=()=>{const a=d.value.reduce((i,o)=>i+o.total_hours,0),e=d.value.reduce((i,o)=>i+o.billable_hours,0),r=d.value.reduce((i,o)=>i+o.revenue,0),x=d.value.length>0?d.value.reduce((i,o)=>i+o.productivity,0)/d.value.length:0,y=a>0?e/a*100:0;n.value={totalHours:Math.round(a),totalHoursChange:0,avgProductivity:Math.round(x),productivityChange:0,revenue:Math.round(r),revenueChange:0,utilization:Math.round(y),utilizationChange:0}},m=async()=>{k.value=!0;try{await Promise.all([R(),B(),U()]),Y()}finally{k.value=!1}},R=async()=>{try{const{startDate:a,endDate:e}=V(),r=new URLSearchParams({start_date:a,end_date:e});w.value&&r.append("department_id",w.value),f.value&&r.append("project_id",f.value);const x=await fetch(`/api/timesheets/?${r}`,{headers:{"Content-Type":"application/json","X-CSRFToken":j.csrfToken}});if(x.ok){const i=(await x.json()).data||[],o={};i.forEach(l=>{var H,A;const p=l.user_id;o[p]||(o[p]={id:p,full_name:((H=l.user)==null?void 0:H.full_name)||"Unknown User",department:((A=l.user)==null?void 0:A.department)||"N/A",total_hours:0,billable_hours:0,projects:new Set,entries:[]}),o[p].total_hours+=l.hours,l.billable&&(o[p].billable_hours+=l.hours),l.project_id&&o[p].projects.add(l.project_id),o[p].entries.push(l)}),d.value=Object.values(o).map(l=>({...l,active_projects:l.projects.size,productivity:l.total_hours>0?l.billable_hours/l.total_hours*100:0,revenue:l.billable_hours*50}))}else console.error("Failed to load analytics data"),d.value=[]}catch(a){console.error("Error loading analytics data:",a),d.value=[]}},B=async()=>{try{const a=await fetch("/api/departments/",{headers:{"Content-Type":"application/json","X-CSRFToken":j.csrfToken}});if(a.ok){const e=await a.json();D.value=e.data||[]}}catch(a){console.error("Error loading departments:",a),D.value=[]}},U=async()=>{try{const a=await fetch("/api/projects/",{headers:{"Content-Type":"application/json","X-CSRFToken":j.csrfToken}});if(a.ok){const e=await a.json();M.value=e.data||[]}}catch(a){console.error("Error loading projects:",a),M.value=[]}},E=()=>{alert("Funzionalità di export in sviluppo")},I=a=>a?a.split(" ").map(e=>e[0]).join("").toUpperCase():"??",O=a=>a>=90?"bg-green-500":a>=75?"bg-yellow-500":"bg-red-500",S=a=>new Intl.NumberFormat("it-IT").format(a);return q(()=>{m()}),(a,e)=>(c(),g("div",X,[t("div",$,[t("div",Q,[e[8]||(e[8]=t("div",null,[t("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Analytics Timesheet"),t("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Report avanzati e analisi delle performance del team ")],-1)),t("div",J,[t("button",{onClick:E,class:"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center"},e[6]||(e[6]=[t("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),v(" Esporta Report ")])),t("button",{onClick:m,disabled:k.value,class:"bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center"},[e[7]||(e[7]=t("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})],-1)),v(" "+s(k.value?"Caricando...":"Aggiorna"),1)],8,K)])])]),t("div",W,[t("div",Z,[t("div",null,[e[10]||(e[10]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Periodo",-1)),_(t("select",{"onUpdate:modelValue":e[0]||(e[0]=r=>z.value=r),onChange:m,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"},e[9]||(e[9]=[N('<option value="current_month">Mese Corrente</option><option value="last_month">Mese Scorso</option><option value="current_quarter">Trimestre Corrente</option><option value="last_quarter">Trimestre Scorso</option><option value="current_year">Anno Corrente</option><option value="custom">Personalizzato</option>',6)]),544),[[C,z.value]])]),t("div",null,[e[12]||(e[12]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Dipartimento",-1)),_(t("select",{"onUpdate:modelValue":e[1]||(e[1]=r=>w.value=r),onChange:m,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"},[e[11]||(e[11]=t("option",{value:""},"Tutti",-1)),(c(!0),g(T,null,F(D.value,r=>(c(),g("option",{key:r.id,value:r.id},s(r.name),9,tt))),128))],544),[[C,w.value]])]),t("div",null,[e[14]||(e[14]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Progetto",-1)),_(t("select",{"onUpdate:modelValue":e[2]||(e[2]=r=>f.value=r),onChange:m,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"},[e[13]||(e[13]=t("option",{value:""},"Tutti",-1)),(c(!0),g(T,null,F(M.value,r=>(c(),g("option",{key:r.id,value:r.id},s(r.name),9,et))),128))],544),[[C,f.value]])]),t("div",null,[e[16]||(e[16]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Tipo Analisi",-1)),_(t("select",{"onUpdate:modelValue":e[3]||(e[3]=r=>P.value=r),onChange:m,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"},e[15]||(e[15]=[t("option",{value:"productivity"},"Produttività",-1),t("option",{value:"utilization"},"Utilizzo",-1),t("option",{value:"billing"},"Fatturazione",-1),t("option",{value:"trends"},"Trend",-1)]),544),[[C,P.value]])])])]),t("div",at,[t("div",rt,[t("div",st,[e[19]||(e[19]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",ot,[e[18]||(e[18]=t("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Ore Totali",-1)),t("p",nt,s(n.value.totalHours)+"h",1),t("p",lt,[t("span",{class:h(n.value.totalHoursChange>=0?"text-green-600":"text-red-600")},s(n.value.totalHoursChange>=0?"+":"")+s(n.value.totalHoursChange)+"% ",3),e[17]||(e[17]=v(" vs periodo precedente "))])])])]),t("div",it,[t("div",dt,[e[22]||(e[22]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])])],-1)),t("div",ut,[e[21]||(e[21]=t("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Produttività Media",-1)),t("p",gt,s(n.value.avgProductivity)+"%",1),t("p",ct,[t("span",{class:h(n.value.productivityChange>=0?"text-green-600":"text-red-600")},s(n.value.productivityChange>=0?"+":"")+s(n.value.productivityChange)+"% ",3),e[20]||(e[20]=v(" vs periodo precedente "))])])])]),t("div",xt,[t("div",pt,[e[25]||(e[25]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-yellow-100 dark:bg-yellow-900/20 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-yellow-600 dark:text-yellow-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])])],-1)),t("div",vt,[e[24]||(e[24]=t("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Revenue Generato",-1)),t("p",ht,"€"+s(S(n.value.revenue)),1),t("p",mt,[t("span",{class:h(n.value.revenueChange>=0?"text-green-600":"text-red-600")},s(n.value.revenueChange>=0?"+":"")+s(n.value.revenueChange)+"% ",3),e[23]||(e[23]=v(" vs periodo precedente "))])])])]),t("div",yt,[t("div",kt,[e[28]||(e[28]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-purple-600 dark:text-purple-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})])])],-1)),t("div",bt,[e[27]||(e[27]=t("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Utilizzo Risorse",-1)),t("p",wt,s(n.value.utilization)+"%",1),t("p",ft,[t("span",{class:h(n.value.utilizationChange>=0?"text-green-600":"text-red-600")},s(n.value.utilizationChange>=0?"+":"")+s(n.value.utilizationChange)+"% ",3),e[26]||(e[26]=v(" vs periodo precedente "))])])])])]),t("div",_t,[t("div",Ct,[t("div",jt,[e[29]||(e[29]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Trend Produttività",-1)),t("div",Dt,[t("button",{onClick:e[4]||(e[4]=r=>b.value="week"),class:h([b.value==="week"?"bg-blue-100 text-blue-700":"text-gray-500","px-3 py-1 rounded text-sm"])}," Settimana ",2),t("button",{onClick:e[5]||(e[5]=r=>b.value="month"),class:h([b.value==="month"?"bg-blue-100 text-blue-700":"text-gray-500","px-3 py-1 rounded text-sm"])}," Mese ",2)])]),e[30]||(e[30]=t("div",{class:"h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded"},[t("p",{class:"text-gray-500 dark:text-gray-400"},"Grafico Produttività (Chart.js placeholder)")],-1))]),e[31]||(e[31]=t("div",{class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},[t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Performance Team"),t("div",{class:"h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded"},[t("p",{class:"text-gray-500 dark:text-gray-400"},"Grafico Performance Team (Chart.js placeholder)")])],-1))]),t("div",Mt,[e[35]||(e[35]=t("div",{class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},[t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Analisi Dettagliata per Dipendente")],-1)),t("div",zt,[t("table",Tt,[e[34]||(e[34]=t("thead",{class:"bg-gray-50 dark:bg-gray-700"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Dipendente "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Ore Lavorate "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Ore Fatturabili "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Produttività "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Revenue "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Progetti Attivi ")])],-1)),t("tbody",Ft,[k.value?(c(),g("tr",Pt,e[32]||(e[32]=[t("td",{colspan:"6",class:"px-6 py-4 text-center text-gray-500 dark:text-gray-400"}," Caricamento dati analytics... ",-1)]))):d.value.length===0?(c(),g("tr",St,e[33]||(e[33]=[t("td",{colspan:"6",class:"px-6 py-4 text-center text-gray-500 dark:text-gray-400"}," Nessun dato disponibile per il periodo selezionato ",-1)]))):(c(!0),g(T,{key:2},F(d.value,r=>(c(),g("tr",{key:r.id},[t("td",Ht,[t("div",At,[t("div",Vt,[t("div",Yt,[t("span",Rt,s(I(r.full_name)),1)])]),t("div",Bt,[t("div",Ut,s(r.full_name),1),t("div",Et,s(r.department),1)])])]),t("td",It,s(r.total_hours)+"h ",1),t("td",Ot,[v(s(r.billable_hours)+"h ",1),t("span",qt," ("+s(Math.round(r.billable_hours/r.total_hours*100))+"%) ",1)]),t("td",Nt,[t("div",Gt,[t("div",Lt,[t("div",{class:h(["h-2 rounded-full",O(r.productivity)]),style:G({width:r.productivity+"%"})},null,6)]),t("span",Xt,s(r.productivity)+"%",1)])]),t("td",$t," €"+s(S(r.revenue)),1),t("td",Qt,s(r.active_projects),1)]))),128))])])])])]))}};export{Zt as default};

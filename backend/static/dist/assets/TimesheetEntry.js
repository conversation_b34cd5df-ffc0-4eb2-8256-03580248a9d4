import{r as p,f as S,A as ue,w as ce,c,j as t,g as B,t as l,F as w,k as j,v as N,G as H,C as ge,o as g,n as M}from"./vendor.js";import{u as ve}from"./app.js";const ye={class:"space-y-6"},ke={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},pe={class:"flex justify-between items-center"},xe={class:"flex items-center space-x-4"},me={class:"flex items-center space-x-2"},he={class:"text-center"},be={class:"text-lg font-semibold text-gray-900 dark:text-white"},fe={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},_e={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},we={class:"flex items-center justify-between"},je={class:"text-lg font-medium text-gray-900 dark:text-white"},Ae={class:"text-sm text-gray-500 dark:text-gray-400"},Ce={class:"overflow-x-auto"},$e={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},Se={class:"bg-gray-50 dark:bg-gray-700"},Me={class:"text-xs text-gray-400"},Ee={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},Te={key:0},De=["colspan"],Pe={class:"space-y-2"},Fe={class:"sticky left-0 z-10 bg-white dark:bg-gray-800 px-4 py-3 border-r border-gray-200 dark:border-gray-600"},ze={class:"text-sm font-medium text-gray-900 dark:text-white"},Ie={class:"text-xs text-gray-500 dark:text-gray-400"},Be={class:"flex items-center mt-1 space-x-2"},Ne=["onClick"],Re=["value","onInput","onBlur"],Le={class:"px-4 py-3 text-center text-sm font-medium text-gray-900 dark:text-white border-l border-gray-200 dark:border-gray-600"},Oe={class:"bg-gray-50 dark:bg-gray-700 font-medium"},Ge={class:"px-4 py-3 text-center text-sm font-bold text-gray-900 dark:text-white border-l border-gray-200 dark:border-gray-600"},He={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},Ue={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Ve={class:"flex items-center"},Xe={class:"ml-5 w-0 flex-1"},Ye={class:"text-lg font-medium text-gray-900 dark:text-white"},Je={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},We={class:"flex items-center"},qe={class:"ml-5 w-0 flex-1"},Qe={class:"text-lg font-medium text-gray-900 dark:text-white"},Ke={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Ze={class:"flex items-center"},et={class:"ml-5 w-0 flex-1"},tt={class:"text-lg font-medium text-gray-900 dark:text-white"},rt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},at={class:"flex items-center"},st={class:"ml-5 w-0 flex-1"},ot={class:"text-lg font-medium text-gray-900 dark:text-white"},lt={key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},nt={class:"relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 shadow-lg rounded-md bg-white dark:bg-gray-800"},it={class:"mt-3"},dt={class:"flex items-center justify-between mb-4"},ut={class:"space-y-4"},ct=["value"],gt={key:0},vt=["value"],yt={class:"flex items-center"},kt={class:"flex space-x-3"},pt=["disabled"],bt={__name:"TimesheetEntry",setup(xt){const b=ve(),n=p([]),x=p([]),h=p([]),k=p([]),f=p([]),R=p(!1),_=p(!1),A=p(new Map),y=p(new Date().getFullYear()),d=p(new Date().getMonth()+1),o=p({project_id:"",task_id:"",billable:!0}),L=["Gennaio","Febbraio","Marzo","Aprile","Maggio","Giugno","Luglio","Agosto","Settembre","Ottobre","Novembre","Dicembre"],C=S(()=>{const a=y.value,e=d.value,r=new Date(a,e,0).getDate();return Array.from({length:r},(s,i)=>i+1)}),T=S(()=>Array.isArray(n.value)?n.value.reduce((a,e)=>a+e.hours,0):0),O=S(()=>Array.isArray(n.value)?n.value.reduce((a,e)=>a+(e.billable?e.hours:0),0):0),U=S(()=>Array.isArray(n.value)?n.value.reduce((a,e)=>a+(e.status==="pending"?e.hours:0),0):0),V=S(()=>k.value.length),X=async()=>{try{const a=await fetch("/api/projects/",{headers:{"Content-Type":"application/json","X-CSRFToken":b.csrfToken}});if(a.ok){const e=await a.json();e.data&&e.data.projects?x.value=e.data.projects:e.data&&Array.isArray(e.data)?x.value=e.data:x.value=[]}}catch(a){console.error("Error loading projects:",a)}},Y=async()=>{try{const a=await fetch("/api/tasks/",{headers:{"Content-Type":"application/json","X-CSRFToken":b.csrfToken}});if(a.ok){const e=await a.json();e.data&&e.data.tasks?h.value=e.data.tasks:e.data&&Array.isArray(e.data)?h.value=e.data:h.value=[]}}catch(a){console.error("Error loading tasks:",a)}},$=async()=>{R.value=!0;try{const a=y.value,e=d.value,r=new Date(a,e,0).getDate(),s=new URLSearchParams({start_date:`${a}-${String(e).padStart(2,"0")}-01`,end_date:`${a}-${String(e).padStart(2,"0")}-${String(r).padStart(2,"0")}`}),i=await fetch(`/api/timesheets/?${s}`,{headers:{"Content-Type":"application/json","X-CSRFToken":b.csrfToken}});if(i.ok){const u=await i.json();u.data&&Array.isArray(u.data)?n.value=u.data:u.data&&u.data.timesheets?n.value=u.data.timesheets:n.value=[],console.log("Loaded timesheets:",n.value.length,"entries"),q()}}catch(a){console.error("Error loading timesheets:",a)}finally{R.value=!1}},J=()=>{d.value===1?(d.value=12,y.value--):d.value--,$()},W=()=>{d.value===12?(d.value=1,y.value++):d.value++,$()},q=()=>{if(!Array.isArray(n.value)){console.warn("timesheets.value is not an array:",n.value),n.value=[];return}console.log("Auto-populating project tasks from",n.value.length,"timesheet entries");const a=new Set;n.value.forEach(e=>{var s,i;const r=`${e.project_id}-${e.task_id||"no-task"}`;if(!a.has(r)&&(a.add(r),!k.value.some(v=>v.project_id===e.project_id&&v.task_id===e.task_id))){const v=Array.isArray(x.value)?x.value.find(I=>I.id===e.project_id):null,F=(v==null?void 0:v.name)||((s=e.project)==null?void 0:s.name)||`Progetto ${e.project_id}`,z=Array.isArray(h.value)?h.value.find(I=>I.id===e.task_id):null,de=(z==null?void 0:z.name)||((i=e.task)==null?void 0:i.name)||null,G={project_id:e.project_id,task_id:e.task_id,project_name:F,task_name:de,billable:e.billable||!0};console.log("Adding project task to grid:",G),k.value.push(G)}}),console.log("Final projectTasks:",k.value)},D=a=>{const e=new Date;return e.getDate()===a&&e.getMonth()+1===d.value&&e.getFullYear()===y.value},P=a=>{const r=new Date(y.value,d.value-1,a).getDay();return r===0||r===6},Q=a=>new Date(y.value,d.value-1,a).toLocaleDateString("it-IT",{weekday:"short"}).toUpperCase(),E=(a,e)=>{if(!Array.isArray(n.value))return 0;const r=`${y.value}-${String(d.value).padStart(2,"0")}-${String(e).padStart(2,"0")}`,s=n.value.find(i=>{const u=i.project_id===a.project_id,v=i.task_id===a.task_id||i.task_id===null&&a.task_id===null||i.task_id===void 0&&a.task_id===null,F=i.date===r;return u&&v&&F});return s?s.hours:0},K=(a,e,r)=>{const s=`${a.project_id}-${a.task_id||"no-task"}-${e}`;r===""||r==="0"?A.value.delete(s):A.value.set(s,{projectTask:a,day:e,hours:parseFloat(r)||0})},Z=(a,e)=>{const r=`${a.project_id}-${a.task_id||"no-task"}-${e}`;return A.value.has(r)},ee=async(a,e)=>{const r=`${a.project_id}-${a.task_id||"no-task"}-${e}`,s=A.value.get(r);if(!s)return;const i=`${y.value}-${String(d.value).padStart(2,"0")}-${String(e).padStart(2,"0")}`;try{const u=Array.isArray(n.value)?n.value.find(v=>v.project_id===a.project_id&&v.task_id===a.task_id&&v.date===i):null;if(s.hours===0)u&&await ae(u.id);else{const v={project_id:a.project_id,task_id:a.task_id||null,date:i,hours:s.hours,billable:a.billable,description:""};u?await re(u.id,v):await te(v)}A.value.delete(r),await $()}catch(u){console.error("Error saving entry:",u),alert("Errore nel salvare le ore. Riprova.")}},te=async a=>{const e=await fetch("/api/timesheets/",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":b.csrfToken},body:JSON.stringify(a)});if(!e.ok){const r=await e.json();throw new Error(r.message||"Errore nella creazione")}},re=async(a,e)=>{const r=await fetch(`/api/timesheets/${a}`,{method:"PUT",headers:{"Content-Type":"application/json","X-CSRFToken":b.csrfToken},body:JSON.stringify(e)});if(!r.ok){const s=await r.json();throw new Error(s.message||"Errore nell'aggiornamento")}},ae=async a=>{const e=await fetch(`/api/timesheets/${a}`,{method:"DELETE",headers:{"Content-Type":"application/json","X-CSRFToken":b.csrfToken}});if(!e.ok){const r=await e.json();throw new Error(r.message||"Errore nell'eliminazione")}},se=a=>C.value.reduce((e,r)=>e+E(a,r),0),oe=a=>k.value.reduce((e,r)=>e+E(r,a),0),le=async()=>{if(!o.value.project_id){f.value=[];return}f.value=Array.isArray(h.value)?h.value.filter(a=>a.project_id===parseInt(o.value.project_id)):[]},ne=()=>{const a=Array.isArray(x.value)?x.value.find(r=>r.id===parseInt(o.value.project_id)):null,e=o.value.task_id&&Array.isArray(f.value)?f.value.find(r=>r.id===parseInt(o.value.task_id)):null;if(`${o.value.project_id}${o.value.task_id||"no-task"}`,k.value.some(r=>r.project_id===parseInt(o.value.project_id)&&r.task_id===(o.value.task_id?parseInt(o.value.task_id):null))){alert("Questo progetto/task è già presente nella griglia");return}k.value.push({project_id:parseInt(o.value.project_id),task_id:o.value.task_id?parseInt(o.value.task_id):null,project_name:a.name,task_name:(e==null?void 0:e.name)||null,billable:o.value.billable}),o.value={project_id:"",task_id:"",billable:!0},f.value=[],_.value=!1},ie=a=>{if(confirm("Rimuovere questo progetto/task dalla griglia? Le ore registrate non verranno eliminate.")){const e=k.value.findIndex(r=>r.project_id===a.project_id&&r.task_id===a.task_id);e!==-1&&k.value.splice(e,1)}},m=a=>!a||a===0?"0h":a%1===0?`${a}h`:`${a.toFixed(1)}h`;return ue(async()=>{await Promise.all([X(),Y()]),await $()}),ce([d,y],()=>{$()}),(a,e)=>(g(),c("div",ye,[t("div",ke,[t("div",pe,[e[9]||(e[9]=t("div",null,[t("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Le Mie Ore"),t("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Registra le tue ore di lavoro con la griglia mensile ")],-1)),t("div",xe,[t("div",me,[t("button",{onClick:J,class:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},e[7]||(e[7]=[t("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)])),t("div",he,[t("h2",be,l(L[d.value-1])+" "+l(y.value),1)]),t("button",{onClick:W,class:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},e[8]||(e[8]=[t("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)]))]),t("button",{onClick:e[0]||(e[0]=r=>_.value=!0),class:"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium"}," Aggiungi Progetto ")])])]),t("div",fe,[t("div",_e,[t("div",we,[t("h3",je," Griglia Ore - "+l(L[d.value-1])+" "+l(y.value),1),t("div",Ae," Totale: "+l(m(T.value))+" | Fatturabili: "+l(m(O.value)),1)])]),t("div",Ce,[t("table",$e,[t("thead",Se,[t("tr",null,[e[10]||(e[10]=t("th",{class:"sticky left-0 z-10 bg-gray-50 dark:bg-gray-700 px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-r border-gray-200 dark:border-gray-600"}," Progetto/Task ",-1)),(g(!0),c(w,null,j(C.value,r=>(g(),c("th",{key:r,class:M(["px-2 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider min-w-[50px]",{"bg-blue-50 dark:bg-blue-900/20":D(r),"bg-red-50 dark:bg-red-900/20":P(r)}])},[t("div",null,l(r),1),t("div",Me,l(Q(r)),1)],2))),128)),e[11]||(e[11]=t("th",{class:"px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-l border-gray-200 dark:border-gray-600"}," Totale ",-1))])]),t("tbody",Ee,[k.value.length===0?(g(),c("tr",Te,[t("td",{colspan:C.value.length+2,class:"px-6 py-8 text-center text-gray-500 dark:text-gray-400"},[t("div",Pe,[e[12]||(e[12]=t("p",null,"Nessun progetto configurato",-1)),t("button",{onClick:e[1]||(e[1]=r=>_.value=!0),class:"text-primary-600 hover:text-primary-700 dark:text-primary-400"}," Aggiungi il tuo primo progetto ")])],8,De)])):B("",!0),(g(!0),c(w,null,j(k.value,r=>(g(),c("tr",{key:`${r.project_id}-${r.task_id||"no-task"}`},[t("td",Fe,[t("div",ze,l(r.project_name),1),t("div",Ie,l(r.task_name||"Nessun task specifico"),1),t("div",Be,[t("span",{class:M(["inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium",r.billable?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"])},l(r.billable?"Fatt.":"Int."),3),t("button",{onClick:s=>ie(r),class:"text-red-400 hover:text-red-600 dark:hover:text-red-300",title:"Rimuovi dalla griglia"},e[13]||(e[13]=[t("svg",{class:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,Ne)])]),(g(!0),c(w,null,j(C.value,s=>(g(),c("td",{key:s,class:M(["px-1 py-2 text-center",{"bg-blue-50 dark:bg-blue-900/20":D(s),"bg-red-50 dark:bg-red-900/20":P(s)}])},[t("input",{type:"number",step:"0.5",min:"0",max:"24",value:E(r,s),onInput:i=>K(r,s,i.target.value),onBlur:i=>ee(r,s),class:M(["w-12 h-8 text-xs text-center border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:border-primary-500 focus:ring-1 focus:ring-primary-500",{"bg-yellow-50 dark:bg-yellow-900/20 border-yellow-300 dark:border-yellow-600":Z(r,s),"bg-green-50 dark:bg-green-900/20 border-green-300 dark:border-green-600":E(r,s)>0}])},null,42,Re)],2))),128)),t("td",Le,l(m(se(r))),1)]))),128)),t("tr",Oe,[e[14]||(e[14]=t("td",{class:"sticky left-0 z-10 bg-gray-50 dark:bg-gray-700 px-4 py-3 text-sm text-gray-900 dark:text-white border-r border-gray-200 dark:border-gray-600"}," Totale Giornaliero ",-1)),(g(!0),c(w,null,j(C.value,r=>(g(),c("td",{key:r,class:M(["px-2 py-3 text-center text-sm text-gray-900 dark:text-white",{"bg-blue-100 dark:bg-blue-800":D(r),"bg-red-100 dark:bg-red-800":P(r)}])},l(m(oe(r))),3))),128)),t("td",Ge,l(m(T.value)),1)])])])])]),t("div",He,[t("div",Ue,[t("div",Ve,[e[16]||(e[16]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",Xe,[t("dl",null,[e[15]||(e[15]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Ore Totali ",-1)),t("dd",Ye,l(m(T.value)),1)])])])]),t("div",Je,[t("div",We,[e[18]||(e[18]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",qe,[t("dl",null,[e[17]||(e[17]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Ore Fatturabili ",-1)),t("dd",Qe,l(m(O.value)),1)])])])]),t("div",Ke,[t("div",Ze,[e[20]||(e[20]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",et,[t("dl",null,[e[19]||(e[19]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," In Attesa ",-1)),t("dd",tt,l(m(U.value)),1)])])])]),t("div",rt,[t("div",at,[e[22]||(e[22]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])])],-1)),t("div",st,[t("dl",null,[e[21]||(e[21]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Progetti Attivi ",-1)),t("dd",ot,l(V.value),1)])])])])]),_.value?(g(),c("div",lt,[t("div",nt,[t("div",it,[t("div",dt,[e[24]||(e[24]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Aggiungi Progetto alla Griglia ",-1)),t("button",{onClick:e[2]||(e[2]=r=>_.value=!1),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},e[23]||(e[23]=[t("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),t("div",ut,[t("div",null,[e[26]||(e[26]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Progetto * ",-1)),N(t("select",{"onUpdate:modelValue":e[3]||(e[3]=r=>o.value.project_id=r),onChange:le,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"},[e[25]||(e[25]=t("option",{value:""},"Seleziona progetto",-1)),(g(!0),c(w,null,j(x.value,r=>(g(),c("option",{key:r.id,value:r.id},l(r.name),9,ct))),128))],544),[[H,o.value.project_id]])]),o.value.project_id?(g(),c("div",gt,[e[28]||(e[28]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Task (opzionale) ",-1)),N(t("select",{"onUpdate:modelValue":e[4]||(e[4]=r=>o.value.task_id=r),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"},[e[27]||(e[27]=t("option",{value:""},"Nessun task specifico",-1)),(g(!0),c(w,null,j(f.value,r=>(g(),c("option",{key:r.id,value:r.id},l(r.name),9,vt))),128))],512),[[H,o.value.task_id]])])):B("",!0),t("div",null,[t("label",yt,[N(t("input",{type:"checkbox","onUpdate:modelValue":e[5]||(e[5]=r=>o.value.billable=r),class:"rounded border-gray-300 dark:border-gray-600"},null,512),[[ge,o.value.billable]]),e[29]||(e[29]=t("span",{class:"ml-2 text-sm text-gray-700 dark:text-gray-300"},"Ore fatturabili",-1))])]),t("div",kt,[t("button",{onClick:ne,disabled:!o.value.project_id,class:"bg-primary-600 hover:bg-primary-700 disabled:bg-primary-400 text-white px-4 py-2 rounded-md text-sm font-medium"}," Aggiungi alla Griglia ",8,pt),t("button",{onClick:e[6]||(e[6]=r=>_.value=!1),class:"bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium"}," Annulla ")])])])])])):B("",!0)]))}};export{bt as default};

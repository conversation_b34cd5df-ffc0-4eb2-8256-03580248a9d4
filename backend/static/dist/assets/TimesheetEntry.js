import{r as c,f as $,A as se,w as oe,c as l,j as t,g as D,t as n,F as h,k as f,v as P,G as O,C as ne,o as i,n as S}from"./vendor.js";import{u as le}from"./app.js";const ie={class:"space-y-6"},de={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},ue={class:"flex justify-between items-center"},ge={class:"flex items-center space-x-4"},ce={class:"flex items-center space-x-2"},ve={class:"text-center"},xe={class:"text-lg font-semibold text-gray-900 dark:text-white"},ye={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},ke={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},me={class:"flex items-center justify-between"},pe={class:"text-lg font-medium text-gray-900 dark:text-white"},be={class:"text-sm text-gray-500 dark:text-gray-400"},he={class:"overflow-x-auto"},fe={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},we={class:"bg-gray-50 dark:bg-gray-700"},_e={class:"text-xs text-gray-400"},je={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},Ce={key:0},$e=["colspan"],Se={class:"space-y-2"},Me={class:"sticky left-0 z-10 bg-white dark:bg-gray-800 px-4 py-3 border-r border-gray-200 dark:border-gray-600"},De={class:"text-sm font-medium text-gray-900 dark:text-white"},Ee={key:0,class:"text-xs text-gray-500 dark:text-gray-400"},Te={class:"flex items-center mt-1 space-x-2"},Fe=["onClick"],ze=["value","onInput","onBlur"],Pe={class:"px-4 py-3 text-center text-sm font-medium text-gray-900 dark:text-white border-l border-gray-200 dark:border-gray-600"},Ie={class:"bg-gray-50 dark:bg-gray-700 font-medium"},Ae={class:"px-4 py-3 text-center text-sm font-bold text-gray-900 dark:text-white border-l border-gray-200 dark:border-gray-600"},Be={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},Re={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Oe={class:"flex items-center"},Le={class:"ml-5 w-0 flex-1"},Ne={class:"text-lg font-medium text-gray-900 dark:text-white"},Ge={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},He={class:"flex items-center"},Ue={class:"ml-5 w-0 flex-1"},Ve={class:"text-lg font-medium text-gray-900 dark:text-white"},Xe={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Ye={class:"flex items-center"},Je={class:"ml-5 w-0 flex-1"},We={class:"text-lg font-medium text-gray-900 dark:text-white"},qe={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Qe={class:"flex items-center"},Ke={class:"ml-5 w-0 flex-1"},Ze={class:"text-lg font-medium text-gray-900 dark:text-white"},et={key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},tt={class:"relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 shadow-lg rounded-md bg-white dark:bg-gray-800"},rt={class:"mt-3"},at={class:"flex items-center justify-between mb-4"},st={class:"space-y-4"},ot=["value"],nt={key:0},lt=["value"],it={class:"flex items-center"},dt={class:"flex space-x-3"},ut=["disabled"],xt={__name:"TimesheetEntry",setup(gt){const k=le(),m=c([]),E=c([]),I=c([]),v=c([]),w=c([]),A=c(!1),p=c(!1),_=c(new Map),g=c(new Date().getFullYear()),d=c(new Date().getMonth()+1),o=c({project_id:"",task_id:"",billable:!0}),B=["Gennaio","Febbraio","Marzo","Aprile","Maggio","Giugno","Luglio","Agosto","Settembre","Ottobre","Novembre","Dicembre"],j=$(()=>{const a=g.value,e=d.value,r=new Date(a,e,0).getDate();return Array.from({length:r},(s,u)=>u+1)}),T=$(()=>m.value.reduce((a,e)=>a+e.hours,0)),R=$(()=>m.value.reduce((a,e)=>a+(e.billable?e.hours:0),0)),L=$(()=>m.value.reduce((a,e)=>a+(e.status==="pending"?e.hours:0),0)),N=$(()=>v.value.length),G=async()=>{try{const a=await fetch("/api/projects/",{headers:{"Content-Type":"application/json","X-CSRFToken":k.csrfToken}});if(a.ok){const e=await a.json();E.value=e.data||[]}}catch(a){console.error("Error loading projects:",a)}},H=async()=>{try{const a=await fetch("/api/tasks/",{headers:{"Content-Type":"application/json","X-CSRFToken":k.csrfToken}});if(a.ok){const e=await a.json();I.value=e.data||[]}}catch(a){console.error("Error loading tasks:",a)}},C=async()=>{A.value=!0;try{const a=g.value,e=d.value,r=new Date(a,e,0).getDate(),s=new URLSearchParams({start_date:`${a}-${String(e).padStart(2,"0")}-01`,end_date:`${a}-${String(e).padStart(2,"0")}-${String(r).padStart(2,"0")}`}),u=await fetch(`/api/timesheets/?${s}`,{headers:{"Content-Type":"application/json","X-CSRFToken":k.csrfToken}});if(u.ok){const y=await u.json();m.value=y.data||[]}}catch(a){console.error("Error loading timesheets:",a)}finally{A.value=!1}},U=()=>{d.value===1?(d.value=12,g.value--):d.value--,C()},V=()=>{d.value===12?(d.value=1,g.value++):d.value++,C()},F=a=>{const e=new Date;return e.getDate()===a&&e.getMonth()+1===d.value&&e.getFullYear()===g.value},z=a=>{const r=new Date(g.value,d.value-1,a).getDay();return r===0||r===6},X=a=>new Date(g.value,d.value-1,a).toLocaleDateString("it-IT",{weekday:"short"}).toUpperCase(),M=(a,e)=>{const r=`${g.value}-${String(d.value).padStart(2,"0")}-${String(e).padStart(2,"0")}`,s=m.value.find(u=>u.project_id===a.project_id&&u.task_id===a.task_id&&u.date===r);return s?s.hours:0},Y=(a,e,r)=>{const s=`${a.project_id}-${a.task_id||"no-task"}-${e}`;r===""||r==="0"?_.value.delete(s):_.value.set(s,{projectTask:a,day:e,hours:parseFloat(r)||0})},J=(a,e)=>{const r=`${a.project_id}-${a.task_id||"no-task"}-${e}`;return _.value.has(r)},W=async(a,e)=>{const r=`${a.project_id}-${a.task_id||"no-task"}-${e}`,s=_.value.get(r);if(!s)return;const u=`${g.value}-${String(d.value).padStart(2,"0")}-${String(e).padStart(2,"0")}`;try{const y=m.value.find(b=>b.project_id===a.project_id&&b.task_id===a.task_id&&b.date===u);if(s.hours===0)y&&await K(y.id);else{const b={project_id:a.project_id,task_id:a.task_id||null,date:u,hours:s.hours,billable:a.billable,description:""};y?await Q(y.id,b):await q(b)}_.value.delete(r),await C()}catch(y){console.error("Error saving entry:",y),alert("Errore nel salvare le ore. Riprova.")}},q=async a=>{const e=await fetch("/api/timesheets/",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":k.csrfToken},body:JSON.stringify(a)});if(!e.ok){const r=await e.json();throw new Error(r.message||"Errore nella creazione")}},Q=async(a,e)=>{const r=await fetch(`/api/timesheets/${a}`,{method:"PUT",headers:{"Content-Type":"application/json","X-CSRFToken":k.csrfToken},body:JSON.stringify(e)});if(!r.ok){const s=await r.json();throw new Error(s.message||"Errore nell'aggiornamento")}},K=async a=>{const e=await fetch(`/api/timesheets/${a}`,{method:"DELETE",headers:{"Content-Type":"application/json","X-CSRFToken":k.csrfToken}});if(!e.ok){const r=await e.json();throw new Error(r.message||"Errore nell'eliminazione")}},Z=a=>j.value.reduce((e,r)=>e+M(a,r),0),ee=a=>v.value.reduce((e,r)=>e+M(r,a),0),te=async()=>{if(!o.value.project_id){w.value=[];return}w.value=I.value.filter(a=>a.project_id===parseInt(o.value.project_id))},re=()=>{const a=E.value.find(r=>r.id===parseInt(o.value.project_id)),e=o.value.task_id?w.value.find(r=>r.id===parseInt(o.value.task_id)):null;if(`${o.value.project_id}${o.value.task_id||"no-task"}`,v.value.some(r=>r.project_id===parseInt(o.value.project_id)&&r.task_id===(o.value.task_id?parseInt(o.value.task_id):null))){alert("Questo progetto/task è già presente nella griglia");return}v.value.push({project_id:parseInt(o.value.project_id),task_id:o.value.task_id?parseInt(o.value.task_id):null,project_name:a.name,task_name:(e==null?void 0:e.name)||null,billable:o.value.billable}),o.value={project_id:"",task_id:"",billable:!0},w.value=[],p.value=!1},ae=a=>{if(confirm("Rimuovere questo progetto/task dalla griglia? Le ore registrate non verranno eliminate.")){const e=v.value.findIndex(r=>r.project_id===a.project_id&&r.task_id===a.task_id);e!==-1&&v.value.splice(e,1)}},x=a=>!a||a===0?"0h":a%1===0?`${a}h`:`${a.toFixed(1)}h`;return se(()=>{G(),H(),C()}),oe([d,g],()=>{C()}),(a,e)=>(i(),l("div",ie,[t("div",de,[t("div",ue,[e[9]||(e[9]=t("div",null,[t("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Le Mie Ore"),t("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Registra le tue ore di lavoro con la griglia mensile ")],-1)),t("div",ge,[t("div",ce,[t("button",{onClick:U,class:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},e[7]||(e[7]=[t("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)])),t("div",ve,[t("h2",xe,n(B[d.value-1])+" "+n(g.value),1)]),t("button",{onClick:V,class:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},e[8]||(e[8]=[t("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)]))]),t("button",{onClick:e[0]||(e[0]=r=>p.value=!0),class:"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium"}," Aggiungi Progetto ")])])]),t("div",ye,[t("div",ke,[t("div",me,[t("h3",pe," Griglia Ore - "+n(B[d.value-1])+" "+n(g.value),1),t("div",be," Totale: "+n(x(T.value))+" | Fatturabili: "+n(x(R.value)),1)])]),t("div",he,[t("table",fe,[t("thead",we,[t("tr",null,[e[10]||(e[10]=t("th",{class:"sticky left-0 z-10 bg-gray-50 dark:bg-gray-700 px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-r border-gray-200 dark:border-gray-600"}," Progetto/Task ",-1)),(i(!0),l(h,null,f(j.value,r=>(i(),l("th",{key:r,class:S(["px-2 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider min-w-[50px]",{"bg-blue-50 dark:bg-blue-900/20":F(r),"bg-red-50 dark:bg-red-900/20":z(r)}])},[t("div",null,n(r),1),t("div",_e,n(X(r)),1)],2))),128)),e[11]||(e[11]=t("th",{class:"px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-l border-gray-200 dark:border-gray-600"}," Totale ",-1))])]),t("tbody",je,[v.value.length===0?(i(),l("tr",Ce,[t("td",{colspan:j.value.length+2,class:"px-6 py-8 text-center text-gray-500 dark:text-gray-400"},[t("div",Se,[e[12]||(e[12]=t("p",null,"Nessun progetto configurato",-1)),t("button",{onClick:e[1]||(e[1]=r=>p.value=!0),class:"text-primary-600 hover:text-primary-700 dark:text-primary-400"}," Aggiungi il tuo primo progetto ")])],8,$e)])):D("",!0),(i(!0),l(h,null,f(v.value,r=>(i(),l("tr",{key:`${r.project_id}-${r.task_id||"no-task"}`},[t("td",Me,[t("div",De,n(r.project_name),1),r.task_name?(i(),l("div",Ee,n(r.task_name),1)):D("",!0),t("div",Te,[t("span",{class:S(["inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium",r.billable?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"])},n(r.billable?"Fatt.":"Int."),3),t("button",{onClick:s=>ae(r),class:"text-red-400 hover:text-red-600 dark:hover:text-red-300",title:"Rimuovi dalla griglia"},e[13]||(e[13]=[t("svg",{class:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,Fe)])]),(i(!0),l(h,null,f(j.value,s=>(i(),l("td",{key:s,class:S(["px-1 py-2 text-center",{"bg-blue-50 dark:bg-blue-900/20":F(s),"bg-red-50 dark:bg-red-900/20":z(s)}])},[t("input",{type:"number",step:"0.5",min:"0",max:"24",value:M(r,s),onInput:u=>Y(r,s,u.target.value),onBlur:u=>W(r,s),class:S(["w-12 h-8 text-xs text-center border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:border-primary-500 focus:ring-1 focus:ring-primary-500",{"bg-yellow-50 dark:bg-yellow-900/20 border-yellow-300 dark:border-yellow-600":J(r,s),"bg-green-50 dark:bg-green-900/20 border-green-300 dark:border-green-600":M(r,s)>0}])},null,42,ze)],2))),128)),t("td",Pe,n(x(Z(r))),1)]))),128)),t("tr",Ie,[e[14]||(e[14]=t("td",{class:"sticky left-0 z-10 bg-gray-50 dark:bg-gray-700 px-4 py-3 text-sm text-gray-900 dark:text-white border-r border-gray-200 dark:border-gray-600"}," Totale Giornaliero ",-1)),(i(!0),l(h,null,f(j.value,r=>(i(),l("td",{key:r,class:S(["px-2 py-3 text-center text-sm text-gray-900 dark:text-white",{"bg-blue-100 dark:bg-blue-800":F(r),"bg-red-100 dark:bg-red-800":z(r)}])},n(x(ee(r))),3))),128)),t("td",Ae,n(x(T.value)),1)])])])])]),t("div",Be,[t("div",Re,[t("div",Oe,[e[16]||(e[16]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",Le,[t("dl",null,[e[15]||(e[15]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Ore Totali ",-1)),t("dd",Ne,n(x(T.value)),1)])])])]),t("div",Ge,[t("div",He,[e[18]||(e[18]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",Ue,[t("dl",null,[e[17]||(e[17]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Ore Fatturabili ",-1)),t("dd",Ve,n(x(R.value)),1)])])])]),t("div",Xe,[t("div",Ye,[e[20]||(e[20]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",Je,[t("dl",null,[e[19]||(e[19]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," In Attesa ",-1)),t("dd",We,n(x(L.value)),1)])])])]),t("div",qe,[t("div",Qe,[e[22]||(e[22]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])])],-1)),t("div",Ke,[t("dl",null,[e[21]||(e[21]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Progetti Attivi ",-1)),t("dd",Ze,n(N.value),1)])])])])]),p.value?(i(),l("div",et,[t("div",tt,[t("div",rt,[t("div",at,[e[24]||(e[24]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Aggiungi Progetto alla Griglia ",-1)),t("button",{onClick:e[2]||(e[2]=r=>p.value=!1),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},e[23]||(e[23]=[t("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),t("div",st,[t("div",null,[e[26]||(e[26]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Progetto * ",-1)),P(t("select",{"onUpdate:modelValue":e[3]||(e[3]=r=>o.value.project_id=r),onChange:te,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"},[e[25]||(e[25]=t("option",{value:""},"Seleziona progetto",-1)),(i(!0),l(h,null,f(E.value,r=>(i(),l("option",{key:r.id,value:r.id},n(r.name),9,ot))),128))],544),[[O,o.value.project_id]])]),o.value.project_id?(i(),l("div",nt,[e[28]||(e[28]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Task (opzionale) ",-1)),P(t("select",{"onUpdate:modelValue":e[4]||(e[4]=r=>o.value.task_id=r),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"},[e[27]||(e[27]=t("option",{value:""},"Nessun task specifico",-1)),(i(!0),l(h,null,f(w.value,r=>(i(),l("option",{key:r.id,value:r.id},n(r.name),9,lt))),128))],512),[[O,o.value.task_id]])])):D("",!0),t("div",null,[t("label",it,[P(t("input",{type:"checkbox","onUpdate:modelValue":e[5]||(e[5]=r=>o.value.billable=r),class:"rounded border-gray-300 dark:border-gray-600"},null,512),[[ne,o.value.billable]]),e[29]||(e[29]=t("span",{class:"ml-2 text-sm text-gray-700 dark:text-gray-300"},"Ore fatturabili",-1))])]),t("div",dt,[t("button",{onClick:re,disabled:!o.value.project_id,class:"bg-primary-600 hover:bg-primary-700 disabled:bg-primary-400 text-white px-4 py-2 rounded-md text-sm font-medium"}," Aggiungi alla Griglia ",8,ut),t("button",{onClick:e[6]||(e[6]=r=>p.value=!1),class:"bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium"}," Annulla ")])])])])])):D("",!0)]))}};export{xt as default};

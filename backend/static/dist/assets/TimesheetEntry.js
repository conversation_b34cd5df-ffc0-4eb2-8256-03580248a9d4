import{r as x,f as $,A as ne,w as le,c as i,j as t,g as E,t as n,F as h,k as f,v as z,G as O,C as ie,o as d,n as S}from"./vendor.js";import{u as de}from"./app.js";const ue={class:"space-y-6"},ce={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},ge={class:"flex justify-between items-center"},ve={class:"flex items-center space-x-4"},ke={class:"flex items-center space-x-2"},xe={class:"text-center"},pe={class:"text-lg font-semibold text-gray-900 dark:text-white"},me={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},ye={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},be={class:"flex items-center justify-between"},he={class:"text-lg font-medium text-gray-900 dark:text-white"},fe={class:"text-sm text-gray-500 dark:text-gray-400"},_e={class:"overflow-x-auto"},we={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},je={class:"bg-gray-50 dark:bg-gray-700"},Ce={class:"text-xs text-gray-400"},$e={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},Se={key:0},Me=["colspan"],Ee={class:"space-y-2"},De={class:"sticky left-0 z-10 bg-white dark:bg-gray-800 px-4 py-3 border-r border-gray-200 dark:border-gray-600"},Te={class:"text-sm font-medium text-gray-900 dark:text-white"},Pe={key:0,class:"text-xs text-gray-500 dark:text-gray-400"},Fe={class:"flex items-center mt-1 space-x-2"},ze=["onClick"],Ae=["value","onInput","onBlur"],Ie={class:"px-4 py-3 text-center text-sm font-medium text-gray-900 dark:text-white border-l border-gray-200 dark:border-gray-600"},Be={class:"bg-gray-50 dark:bg-gray-700 font-medium"},Re={class:"px-4 py-3 text-center text-sm font-bold text-gray-900 dark:text-white border-l border-gray-200 dark:border-gray-600"},Oe={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},Le={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Ne={class:"flex items-center"},Ge={class:"ml-5 w-0 flex-1"},He={class:"text-lg font-medium text-gray-900 dark:text-white"},Ue={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Ve={class:"flex items-center"},Xe={class:"ml-5 w-0 flex-1"},Ye={class:"text-lg font-medium text-gray-900 dark:text-white"},Je={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},We={class:"flex items-center"},qe={class:"ml-5 w-0 flex-1"},Qe={class:"text-lg font-medium text-gray-900 dark:text-white"},Ke={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Ze={class:"flex items-center"},et={class:"ml-5 w-0 flex-1"},tt={class:"text-lg font-medium text-gray-900 dark:text-white"},rt={key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},at={class:"relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 shadow-lg rounded-md bg-white dark:bg-gray-800"},st={class:"mt-3"},ot={class:"flex items-center justify-between mb-4"},nt={class:"space-y-4"},lt=["value"],it={key:0},dt=["value"],ut={class:"flex items-center"},ct={class:"flex space-x-3"},gt=["disabled"],pt={__name:"TimesheetEntry",setup(vt){const y=de(),p=x([]),D=x([]),A=x([]),v=x([]),_=x([]),I=x(!1),b=x(!1),w=x(new Map),g=x(new Date().getFullYear()),u=x(new Date().getMonth()+1),o=x({project_id:"",task_id:"",billable:!0}),B=["Gennaio","Febbraio","Marzo","Aprile","Maggio","Giugno","Luglio","Agosto","Settembre","Ottobre","Novembre","Dicembre"],j=$(()=>{const a=g.value,e=u.value,r=new Date(a,e,0).getDate();return Array.from({length:r},(s,l)=>l+1)}),T=$(()=>p.value.reduce((a,e)=>a+e.hours,0)),R=$(()=>p.value.reduce((a,e)=>a+(e.billable?e.hours:0),0)),L=$(()=>p.value.reduce((a,e)=>a+(e.status==="pending"?e.hours:0),0)),N=$(()=>v.value.length),G=async()=>{try{const a=await fetch("/api/projects/",{headers:{"Content-Type":"application/json","X-CSRFToken":y.csrfToken}});if(a.ok){const e=await a.json();D.value=e.data||[]}}catch(a){console.error("Error loading projects:",a)}},H=async()=>{try{const a=await fetch("/api/tasks/",{headers:{"Content-Type":"application/json","X-CSRFToken":y.csrfToken}});if(a.ok){const e=await a.json();A.value=e.data||[]}}catch(a){console.error("Error loading tasks:",a)}},C=async()=>{I.value=!0;try{const a=g.value,e=u.value,r=new Date(a,e,0).getDate(),s=new URLSearchParams({start_date:`${a}-${String(e).padStart(2,"0")}-01`,end_date:`${a}-${String(e).padStart(2,"0")}-${String(r).padStart(2,"0")}`}),l=await fetch(`/api/timesheets/?${s}`,{headers:{"Content-Type":"application/json","X-CSRFToken":y.csrfToken}});if(l.ok){const k=await l.json();p.value=k.data||[],X()}}catch(a){console.error("Error loading timesheets:",a)}finally{I.value=!1}},U=()=>{u.value===1?(u.value=12,g.value--):u.value--,C()},V=()=>{u.value===12?(u.value=1,g.value++):u.value++,C()},X=()=>{console.log("Auto-populating project tasks from",p.value.length,"timesheet entries");const a=new Set;p.value.forEach(e=>{var s,l;const r=`${e.project_id}-${e.task_id||"no-task"}`;if(!a.has(r)&&(a.add(r),!v.value.some(c=>c.project_id===e.project_id&&c.task_id===e.task_id))){const c={project_id:e.project_id,task_id:e.task_id,project_name:((s=e.project)==null?void 0:s.name)||`Progetto ${e.project_id}`,task_name:((l=e.task)==null?void 0:l.name)||null,billable:e.billable||!0};console.log("Adding project task to grid:",c),v.value.push(c)}}),console.log("Final projectTasks:",v.value)},P=a=>{const e=new Date;return e.getDate()===a&&e.getMonth()+1===u.value&&e.getFullYear()===g.value},F=a=>{const r=new Date(g.value,u.value-1,a).getDay();return r===0||r===6},Y=a=>new Date(g.value,u.value-1,a).toLocaleDateString("it-IT",{weekday:"short"}).toUpperCase(),M=(a,e)=>{const r=`${g.value}-${String(u.value).padStart(2,"0")}-${String(e).padStart(2,"0")}`,s=p.value.find(l=>{const k=l.project_id===a.project_id,c=l.task_id===a.task_id||l.task_id===null&&a.task_id===null||l.task_id===void 0&&a.task_id===null,oe=l.date===r;return k&&c&&oe});return s?s.hours:0},J=(a,e,r)=>{const s=`${a.project_id}-${a.task_id||"no-task"}-${e}`;r===""||r==="0"?w.value.delete(s):w.value.set(s,{projectTask:a,day:e,hours:parseFloat(r)||0})},W=(a,e)=>{const r=`${a.project_id}-${a.task_id||"no-task"}-${e}`;return w.value.has(r)},q=async(a,e)=>{const r=`${a.project_id}-${a.task_id||"no-task"}-${e}`,s=w.value.get(r);if(!s)return;const l=`${g.value}-${String(u.value).padStart(2,"0")}-${String(e).padStart(2,"0")}`;try{const k=p.value.find(c=>c.project_id===a.project_id&&c.task_id===a.task_id&&c.date===l);if(s.hours===0)k&&await Z(k.id);else{const c={project_id:a.project_id,task_id:a.task_id||null,date:l,hours:s.hours,billable:a.billable,description:""};k?await K(k.id,c):await Q(c)}w.value.delete(r),await C()}catch(k){console.error("Error saving entry:",k),alert("Errore nel salvare le ore. Riprova.")}},Q=async a=>{const e=await fetch("/api/timesheets/",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":y.csrfToken},body:JSON.stringify(a)});if(!e.ok){const r=await e.json();throw new Error(r.message||"Errore nella creazione")}},K=async(a,e)=>{const r=await fetch(`/api/timesheets/${a}`,{method:"PUT",headers:{"Content-Type":"application/json","X-CSRFToken":y.csrfToken},body:JSON.stringify(e)});if(!r.ok){const s=await r.json();throw new Error(s.message||"Errore nell'aggiornamento")}},Z=async a=>{const e=await fetch(`/api/timesheets/${a}`,{method:"DELETE",headers:{"Content-Type":"application/json","X-CSRFToken":y.csrfToken}});if(!e.ok){const r=await e.json();throw new Error(r.message||"Errore nell'eliminazione")}},ee=a=>j.value.reduce((e,r)=>e+M(a,r),0),te=a=>v.value.reduce((e,r)=>e+M(r,a),0),re=async()=>{if(!o.value.project_id){_.value=[];return}_.value=A.value.filter(a=>a.project_id===parseInt(o.value.project_id))},ae=()=>{const a=D.value.find(r=>r.id===parseInt(o.value.project_id)),e=o.value.task_id?_.value.find(r=>r.id===parseInt(o.value.task_id)):null;if(`${o.value.project_id}${o.value.task_id||"no-task"}`,v.value.some(r=>r.project_id===parseInt(o.value.project_id)&&r.task_id===(o.value.task_id?parseInt(o.value.task_id):null))){alert("Questo progetto/task è già presente nella griglia");return}v.value.push({project_id:parseInt(o.value.project_id),task_id:o.value.task_id?parseInt(o.value.task_id):null,project_name:a.name,task_name:(e==null?void 0:e.name)||null,billable:o.value.billable}),o.value={project_id:"",task_id:"",billable:!0},_.value=[],b.value=!1},se=a=>{if(confirm("Rimuovere questo progetto/task dalla griglia? Le ore registrate non verranno eliminate.")){const e=v.value.findIndex(r=>r.project_id===a.project_id&&r.task_id===a.task_id);e!==-1&&v.value.splice(e,1)}},m=a=>!a||a===0?"0h":a%1===0?`${a}h`:`${a.toFixed(1)}h`;return ne(()=>{G(),H(),C()}),le([u,g],()=>{C()}),(a,e)=>(d(),i("div",ue,[t("div",ce,[t("div",ge,[e[9]||(e[9]=t("div",null,[t("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Le Mie Ore"),t("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Registra le tue ore di lavoro con la griglia mensile ")],-1)),t("div",ve,[t("div",ke,[t("button",{onClick:U,class:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},e[7]||(e[7]=[t("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)])),t("div",xe,[t("h2",pe,n(B[u.value-1])+" "+n(g.value),1)]),t("button",{onClick:V,class:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},e[8]||(e[8]=[t("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)]))]),t("button",{onClick:e[0]||(e[0]=r=>b.value=!0),class:"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium"}," Aggiungi Progetto ")])])]),t("div",me,[t("div",ye,[t("div",be,[t("h3",he," Griglia Ore - "+n(B[u.value-1])+" "+n(g.value),1),t("div",fe," Totale: "+n(m(T.value))+" | Fatturabili: "+n(m(R.value)),1)])]),t("div",_e,[t("table",we,[t("thead",je,[t("tr",null,[e[10]||(e[10]=t("th",{class:"sticky left-0 z-10 bg-gray-50 dark:bg-gray-700 px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-r border-gray-200 dark:border-gray-600"}," Progetto/Task ",-1)),(d(!0),i(h,null,f(j.value,r=>(d(),i("th",{key:r,class:S(["px-2 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider min-w-[50px]",{"bg-blue-50 dark:bg-blue-900/20":P(r),"bg-red-50 dark:bg-red-900/20":F(r)}])},[t("div",null,n(r),1),t("div",Ce,n(Y(r)),1)],2))),128)),e[11]||(e[11]=t("th",{class:"px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-l border-gray-200 dark:border-gray-600"}," Totale ",-1))])]),t("tbody",$e,[v.value.length===0?(d(),i("tr",Se,[t("td",{colspan:j.value.length+2,class:"px-6 py-8 text-center text-gray-500 dark:text-gray-400"},[t("div",Ee,[e[12]||(e[12]=t("p",null,"Nessun progetto configurato",-1)),t("button",{onClick:e[1]||(e[1]=r=>b.value=!0),class:"text-primary-600 hover:text-primary-700 dark:text-primary-400"}," Aggiungi il tuo primo progetto ")])],8,Me)])):E("",!0),(d(!0),i(h,null,f(v.value,r=>(d(),i("tr",{key:`${r.project_id}-${r.task_id||"no-task"}`},[t("td",De,[t("div",Te,n(r.project_name),1),r.task_name?(d(),i("div",Pe,n(r.task_name),1)):E("",!0),t("div",Fe,[t("span",{class:S(["inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium",r.billable?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"])},n(r.billable?"Fatt.":"Int."),3),t("button",{onClick:s=>se(r),class:"text-red-400 hover:text-red-600 dark:hover:text-red-300",title:"Rimuovi dalla griglia"},e[13]||(e[13]=[t("svg",{class:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,ze)])]),(d(!0),i(h,null,f(j.value,s=>(d(),i("td",{key:s,class:S(["px-1 py-2 text-center",{"bg-blue-50 dark:bg-blue-900/20":P(s),"bg-red-50 dark:bg-red-900/20":F(s)}])},[t("input",{type:"number",step:"0.5",min:"0",max:"24",value:M(r,s),onInput:l=>J(r,s,l.target.value),onBlur:l=>q(r,s),class:S(["w-12 h-8 text-xs text-center border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:border-primary-500 focus:ring-1 focus:ring-primary-500",{"bg-yellow-50 dark:bg-yellow-900/20 border-yellow-300 dark:border-yellow-600":W(r,s),"bg-green-50 dark:bg-green-900/20 border-green-300 dark:border-green-600":M(r,s)>0}])},null,42,Ae)],2))),128)),t("td",Ie,n(m(ee(r))),1)]))),128)),t("tr",Be,[e[14]||(e[14]=t("td",{class:"sticky left-0 z-10 bg-gray-50 dark:bg-gray-700 px-4 py-3 text-sm text-gray-900 dark:text-white border-r border-gray-200 dark:border-gray-600"}," Totale Giornaliero ",-1)),(d(!0),i(h,null,f(j.value,r=>(d(),i("td",{key:r,class:S(["px-2 py-3 text-center text-sm text-gray-900 dark:text-white",{"bg-blue-100 dark:bg-blue-800":P(r),"bg-red-100 dark:bg-red-800":F(r)}])},n(m(te(r))),3))),128)),t("td",Re,n(m(T.value)),1)])])])])]),t("div",Oe,[t("div",Le,[t("div",Ne,[e[16]||(e[16]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",Ge,[t("dl",null,[e[15]||(e[15]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Ore Totali ",-1)),t("dd",He,n(m(T.value)),1)])])])]),t("div",Ue,[t("div",Ve,[e[18]||(e[18]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",Xe,[t("dl",null,[e[17]||(e[17]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Ore Fatturabili ",-1)),t("dd",Ye,n(m(R.value)),1)])])])]),t("div",Je,[t("div",We,[e[20]||(e[20]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",qe,[t("dl",null,[e[19]||(e[19]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," In Attesa ",-1)),t("dd",Qe,n(m(L.value)),1)])])])]),t("div",Ke,[t("div",Ze,[e[22]||(e[22]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])])],-1)),t("div",et,[t("dl",null,[e[21]||(e[21]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Progetti Attivi ",-1)),t("dd",tt,n(N.value),1)])])])])]),b.value?(d(),i("div",rt,[t("div",at,[t("div",st,[t("div",ot,[e[24]||(e[24]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Aggiungi Progetto alla Griglia ",-1)),t("button",{onClick:e[2]||(e[2]=r=>b.value=!1),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},e[23]||(e[23]=[t("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),t("div",nt,[t("div",null,[e[26]||(e[26]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Progetto * ",-1)),z(t("select",{"onUpdate:modelValue":e[3]||(e[3]=r=>o.value.project_id=r),onChange:re,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"},[e[25]||(e[25]=t("option",{value:""},"Seleziona progetto",-1)),(d(!0),i(h,null,f(D.value,r=>(d(),i("option",{key:r.id,value:r.id},n(r.name),9,lt))),128))],544),[[O,o.value.project_id]])]),o.value.project_id?(d(),i("div",it,[e[28]||(e[28]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Task (opzionale) ",-1)),z(t("select",{"onUpdate:modelValue":e[4]||(e[4]=r=>o.value.task_id=r),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"},[e[27]||(e[27]=t("option",{value:""},"Nessun task specifico",-1)),(d(!0),i(h,null,f(_.value,r=>(d(),i("option",{key:r.id,value:r.id},n(r.name),9,dt))),128))],512),[[O,o.value.task_id]])])):E("",!0),t("div",null,[t("label",ut,[z(t("input",{type:"checkbox","onUpdate:modelValue":e[5]||(e[5]=r=>o.value.billable=r),class:"rounded border-gray-300 dark:border-gray-600"},null,512),[[ie,o.value.billable]]),e[29]||(e[29]=t("span",{class:"ml-2 text-sm text-gray-700 dark:text-gray-300"},"Ore fatturabili",-1))])]),t("div",ct,[t("button",{onClick:ae,disabled:!o.value.project_id,class:"bg-primary-600 hover:bg-primary-700 disabled:bg-primary-400 text-white px-4 py-2 rounded-md text-sm font-medium"}," Aggiungi alla Griglia ",8,gt),t("button",{onClick:e[6]||(e[6]=r=>b.value=!1),class:"bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium"}," Annulla ")])])])])])):E("",!0)]))}};export{pt as default};

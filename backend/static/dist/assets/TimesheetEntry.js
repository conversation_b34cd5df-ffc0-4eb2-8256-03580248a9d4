import{r as x,f as $,A as ue,w as ce,c as d,j as t,g as B,t as n,F as h,k as f,v as N,G as H,C as ge,o as u,n as S}from"./vendor.js";import{u as ve}from"./app.js";const ke={class:"space-y-6"},xe={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},pe={class:"flex justify-between items-center"},me={class:"flex items-center space-x-4"},ye={class:"flex items-center space-x-2"},be={class:"text-center"},he={class:"text-lg font-semibold text-gray-900 dark:text-white"},fe={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},_e={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},we={class:"flex items-center justify-between"},je={class:"text-lg font-medium text-gray-900 dark:text-white"},Ce={class:"text-sm text-gray-500 dark:text-gray-400"},$e={class:"overflow-x-auto"},Se={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},Me={class:"bg-gray-50 dark:bg-gray-700"},Ee={class:"text-xs text-gray-400"},Te={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},De={key:0},Pe=["colspan"],Fe={class:"space-y-2"},ze={class:"sticky left-0 z-10 bg-white dark:bg-gray-800 px-4 py-3 border-r border-gray-200 dark:border-gray-600"},Ae={class:"text-sm font-medium text-gray-900 dark:text-white"},Ie={class:"text-xs text-gray-500 dark:text-gray-400"},Be={class:"flex items-center mt-1 space-x-2"},Ne=["onClick"],Re=["value","onInput","onBlur"],Oe={class:"px-4 py-3 text-center text-sm font-medium text-gray-900 dark:text-white border-l border-gray-200 dark:border-gray-600"},Le={class:"bg-gray-50 dark:bg-gray-700 font-medium"},Ge={class:"px-4 py-3 text-center text-sm font-bold text-gray-900 dark:text-white border-l border-gray-200 dark:border-gray-600"},He={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},Ue={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Ve={class:"flex items-center"},Xe={class:"ml-5 w-0 flex-1"},Ye={class:"text-lg font-medium text-gray-900 dark:text-white"},Je={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},We={class:"flex items-center"},qe={class:"ml-5 w-0 flex-1"},Qe={class:"text-lg font-medium text-gray-900 dark:text-white"},Ke={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Ze={class:"flex items-center"},et={class:"ml-5 w-0 flex-1"},tt={class:"text-lg font-medium text-gray-900 dark:text-white"},rt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},at={class:"flex items-center"},st={class:"ml-5 w-0 flex-1"},ot={class:"text-lg font-medium text-gray-900 dark:text-white"},nt={key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},lt={class:"relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 shadow-lg rounded-md bg-white dark:bg-gray-800"},it={class:"mt-3"},dt={class:"flex items-center justify-between mb-4"},ut={class:"space-y-4"},ct=["value"],gt={key:0},vt=["value"],kt={class:"flex items-center"},xt={class:"flex space-x-3"},pt=["disabled"],ht={__name:"TimesheetEntry",setup(mt){const y=ve(),p=x([]),M=x([]),T=x([]),v=x([]),_=x([]),R=x(!1),b=x(!1),w=x(new Map),g=x(new Date().getFullYear()),i=x(new Date().getMonth()+1),o=x({project_id:"",task_id:"",billable:!0}),O=["Gennaio","Febbraio","Marzo","Aprile","Maggio","Giugno","Luglio","Agosto","Settembre","Ottobre","Novembre","Dicembre"],j=$(()=>{const a=g.value,e=i.value,r=new Date(a,e,0).getDate();return Array.from({length:r},(s,l)=>l+1)}),D=$(()=>p.value.reduce((a,e)=>a+e.hours,0)),L=$(()=>p.value.reduce((a,e)=>a+(e.billable?e.hours:0),0)),U=$(()=>p.value.reduce((a,e)=>a+(e.status==="pending"?e.hours:0),0)),V=$(()=>v.value.length),X=async()=>{try{const a=await fetch("/api/projects/",{headers:{"Content-Type":"application/json","X-CSRFToken":y.csrfToken}});if(a.ok){const e=await a.json();M.value=e.data||[]}}catch(a){console.error("Error loading projects:",a)}},Y=async()=>{try{const a=await fetch("/api/tasks/",{headers:{"Content-Type":"application/json","X-CSRFToken":y.csrfToken}});if(a.ok){const e=await a.json();T.value=e.data||[]}}catch(a){console.error("Error loading tasks:",a)}},C=async()=>{R.value=!0;try{const a=g.value,e=i.value,r=new Date(a,e,0).getDate(),s=new URLSearchParams({start_date:`${a}-${String(e).padStart(2,"0")}-01`,end_date:`${a}-${String(e).padStart(2,"0")}-${String(r).padStart(2,"0")}`}),l=await fetch(`/api/timesheets/?${s}`,{headers:{"Content-Type":"application/json","X-CSRFToken":y.csrfToken}});if(l.ok){const k=await l.json();p.value=k.data||[],q()}}catch(a){console.error("Error loading timesheets:",a)}finally{R.value=!1}},J=()=>{i.value===1?(i.value=12,g.value--):i.value--,C()},W=()=>{i.value===12?(i.value=1,g.value++):i.value++,C()},q=()=>{console.log("Auto-populating project tasks from",p.value.length,"timesheet entries");const a=new Set;p.value.forEach(e=>{var s,l;const r=`${e.project_id}-${e.task_id||"no-task"}`;if(!a.has(r)&&(a.add(r),!v.value.some(c=>c.project_id===e.project_id&&c.task_id===e.task_id))){const c=M.value.find(I=>I.id===e.project_id),z=(c==null?void 0:c.name)||((s=e.project)==null?void 0:s.name)||`Progetto ${e.project_id}`,A=T.value.find(I=>I.id===e.task_id),de=(A==null?void 0:A.name)||((l=e.task)==null?void 0:l.name)||null,G={project_id:e.project_id,task_id:e.task_id,project_name:z,task_name:de,billable:e.billable||!0};console.log("Adding project task to grid:",G),v.value.push(G)}}),console.log("Final projectTasks:",v.value)},P=a=>{const e=new Date;return e.getDate()===a&&e.getMonth()+1===i.value&&e.getFullYear()===g.value},F=a=>{const r=new Date(g.value,i.value-1,a).getDay();return r===0||r===6},Q=a=>new Date(g.value,i.value-1,a).toLocaleDateString("it-IT",{weekday:"short"}).toUpperCase(),E=(a,e)=>{const r=`${g.value}-${String(i.value).padStart(2,"0")}-${String(e).padStart(2,"0")}`,s=p.value.find(l=>{const k=l.project_id===a.project_id,c=l.task_id===a.task_id||l.task_id===null&&a.task_id===null||l.task_id===void 0&&a.task_id===null,z=l.date===r;return k&&c&&z});return s?s.hours:0},K=(a,e,r)=>{const s=`${a.project_id}-${a.task_id||"no-task"}-${e}`;r===""||r==="0"?w.value.delete(s):w.value.set(s,{projectTask:a,day:e,hours:parseFloat(r)||0})},Z=(a,e)=>{const r=`${a.project_id}-${a.task_id||"no-task"}-${e}`;return w.value.has(r)},ee=async(a,e)=>{const r=`${a.project_id}-${a.task_id||"no-task"}-${e}`,s=w.value.get(r);if(!s)return;const l=`${g.value}-${String(i.value).padStart(2,"0")}-${String(e).padStart(2,"0")}`;try{const k=p.value.find(c=>c.project_id===a.project_id&&c.task_id===a.task_id&&c.date===l);if(s.hours===0)k&&await ae(k.id);else{const c={project_id:a.project_id,task_id:a.task_id||null,date:l,hours:s.hours,billable:a.billable,description:""};k?await re(k.id,c):await te(c)}w.value.delete(r),await C()}catch(k){console.error("Error saving entry:",k),alert("Errore nel salvare le ore. Riprova.")}},te=async a=>{const e=await fetch("/api/timesheets/",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":y.csrfToken},body:JSON.stringify(a)});if(!e.ok){const r=await e.json();throw new Error(r.message||"Errore nella creazione")}},re=async(a,e)=>{const r=await fetch(`/api/timesheets/${a}`,{method:"PUT",headers:{"Content-Type":"application/json","X-CSRFToken":y.csrfToken},body:JSON.stringify(e)});if(!r.ok){const s=await r.json();throw new Error(s.message||"Errore nell'aggiornamento")}},ae=async a=>{const e=await fetch(`/api/timesheets/${a}`,{method:"DELETE",headers:{"Content-Type":"application/json","X-CSRFToken":y.csrfToken}});if(!e.ok){const r=await e.json();throw new Error(r.message||"Errore nell'eliminazione")}},se=a=>j.value.reduce((e,r)=>e+E(a,r),0),oe=a=>v.value.reduce((e,r)=>e+E(r,a),0),ne=async()=>{if(!o.value.project_id){_.value=[];return}_.value=T.value.filter(a=>a.project_id===parseInt(o.value.project_id))},le=()=>{const a=M.value.find(r=>r.id===parseInt(o.value.project_id)),e=o.value.task_id?_.value.find(r=>r.id===parseInt(o.value.task_id)):null;if(`${o.value.project_id}${o.value.task_id||"no-task"}`,v.value.some(r=>r.project_id===parseInt(o.value.project_id)&&r.task_id===(o.value.task_id?parseInt(o.value.task_id):null))){alert("Questo progetto/task è già presente nella griglia");return}v.value.push({project_id:parseInt(o.value.project_id),task_id:o.value.task_id?parseInt(o.value.task_id):null,project_name:a.name,task_name:(e==null?void 0:e.name)||null,billable:o.value.billable}),o.value={project_id:"",task_id:"",billable:!0},_.value=[],b.value=!1},ie=a=>{if(confirm("Rimuovere questo progetto/task dalla griglia? Le ore registrate non verranno eliminate.")){const e=v.value.findIndex(r=>r.project_id===a.project_id&&r.task_id===a.task_id);e!==-1&&v.value.splice(e,1)}},m=a=>!a||a===0?"0h":a%1===0?`${a}h`:`${a.toFixed(1)}h`;return ue(async()=>{await Promise.all([X(),Y()]),await C()}),ce([i,g],()=>{C()}),(a,e)=>(u(),d("div",ke,[t("div",xe,[t("div",pe,[e[9]||(e[9]=t("div",null,[t("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Le Mie Ore"),t("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Registra le tue ore di lavoro con la griglia mensile ")],-1)),t("div",me,[t("div",ye,[t("button",{onClick:J,class:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},e[7]||(e[7]=[t("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)])),t("div",be,[t("h2",he,n(O[i.value-1])+" "+n(g.value),1)]),t("button",{onClick:W,class:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},e[8]||(e[8]=[t("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)]))]),t("button",{onClick:e[0]||(e[0]=r=>b.value=!0),class:"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium"}," Aggiungi Progetto ")])])]),t("div",fe,[t("div",_e,[t("div",we,[t("h3",je," Griglia Ore - "+n(O[i.value-1])+" "+n(g.value),1),t("div",Ce," Totale: "+n(m(D.value))+" | Fatturabili: "+n(m(L.value)),1)])]),t("div",$e,[t("table",Se,[t("thead",Me,[t("tr",null,[e[10]||(e[10]=t("th",{class:"sticky left-0 z-10 bg-gray-50 dark:bg-gray-700 px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-r border-gray-200 dark:border-gray-600"}," Progetto/Task ",-1)),(u(!0),d(h,null,f(j.value,r=>(u(),d("th",{key:r,class:S(["px-2 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider min-w-[50px]",{"bg-blue-50 dark:bg-blue-900/20":P(r),"bg-red-50 dark:bg-red-900/20":F(r)}])},[t("div",null,n(r),1),t("div",Ee,n(Q(r)),1)],2))),128)),e[11]||(e[11]=t("th",{class:"px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-l border-gray-200 dark:border-gray-600"}," Totale ",-1))])]),t("tbody",Te,[v.value.length===0?(u(),d("tr",De,[t("td",{colspan:j.value.length+2,class:"px-6 py-8 text-center text-gray-500 dark:text-gray-400"},[t("div",Fe,[e[12]||(e[12]=t("p",null,"Nessun progetto configurato",-1)),t("button",{onClick:e[1]||(e[1]=r=>b.value=!0),class:"text-primary-600 hover:text-primary-700 dark:text-primary-400"}," Aggiungi il tuo primo progetto ")])],8,Pe)])):B("",!0),(u(!0),d(h,null,f(v.value,r=>(u(),d("tr",{key:`${r.project_id}-${r.task_id||"no-task"}`},[t("td",ze,[t("div",Ae,n(r.project_name),1),t("div",Ie,n(r.task_name||"Nessun task specifico"),1),t("div",Be,[t("span",{class:S(["inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium",r.billable?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"])},n(r.billable?"Fatt.":"Int."),3),t("button",{onClick:s=>ie(r),class:"text-red-400 hover:text-red-600 dark:hover:text-red-300",title:"Rimuovi dalla griglia"},e[13]||(e[13]=[t("svg",{class:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,Ne)])]),(u(!0),d(h,null,f(j.value,s=>(u(),d("td",{key:s,class:S(["px-1 py-2 text-center",{"bg-blue-50 dark:bg-blue-900/20":P(s),"bg-red-50 dark:bg-red-900/20":F(s)}])},[t("input",{type:"number",step:"0.5",min:"0",max:"24",value:E(r,s),onInput:l=>K(r,s,l.target.value),onBlur:l=>ee(r,s),class:S(["w-12 h-8 text-xs text-center border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:border-primary-500 focus:ring-1 focus:ring-primary-500",{"bg-yellow-50 dark:bg-yellow-900/20 border-yellow-300 dark:border-yellow-600":Z(r,s),"bg-green-50 dark:bg-green-900/20 border-green-300 dark:border-green-600":E(r,s)>0}])},null,42,Re)],2))),128)),t("td",Oe,n(m(se(r))),1)]))),128)),t("tr",Le,[e[14]||(e[14]=t("td",{class:"sticky left-0 z-10 bg-gray-50 dark:bg-gray-700 px-4 py-3 text-sm text-gray-900 dark:text-white border-r border-gray-200 dark:border-gray-600"}," Totale Giornaliero ",-1)),(u(!0),d(h,null,f(j.value,r=>(u(),d("td",{key:r,class:S(["px-2 py-3 text-center text-sm text-gray-900 dark:text-white",{"bg-blue-100 dark:bg-blue-800":P(r),"bg-red-100 dark:bg-red-800":F(r)}])},n(m(oe(r))),3))),128)),t("td",Ge,n(m(D.value)),1)])])])])]),t("div",He,[t("div",Ue,[t("div",Ve,[e[16]||(e[16]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",Xe,[t("dl",null,[e[15]||(e[15]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Ore Totali ",-1)),t("dd",Ye,n(m(D.value)),1)])])])]),t("div",Je,[t("div",We,[e[18]||(e[18]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",qe,[t("dl",null,[e[17]||(e[17]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Ore Fatturabili ",-1)),t("dd",Qe,n(m(L.value)),1)])])])]),t("div",Ke,[t("div",Ze,[e[20]||(e[20]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",et,[t("dl",null,[e[19]||(e[19]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," In Attesa ",-1)),t("dd",tt,n(m(U.value)),1)])])])]),t("div",rt,[t("div",at,[e[22]||(e[22]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])])],-1)),t("div",st,[t("dl",null,[e[21]||(e[21]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Progetti Attivi ",-1)),t("dd",ot,n(V.value),1)])])])])]),b.value?(u(),d("div",nt,[t("div",lt,[t("div",it,[t("div",dt,[e[24]||(e[24]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Aggiungi Progetto alla Griglia ",-1)),t("button",{onClick:e[2]||(e[2]=r=>b.value=!1),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},e[23]||(e[23]=[t("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),t("div",ut,[t("div",null,[e[26]||(e[26]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Progetto * ",-1)),N(t("select",{"onUpdate:modelValue":e[3]||(e[3]=r=>o.value.project_id=r),onChange:ne,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"},[e[25]||(e[25]=t("option",{value:""},"Seleziona progetto",-1)),(u(!0),d(h,null,f(M.value,r=>(u(),d("option",{key:r.id,value:r.id},n(r.name),9,ct))),128))],544),[[H,o.value.project_id]])]),o.value.project_id?(u(),d("div",gt,[e[28]||(e[28]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Task (opzionale) ",-1)),N(t("select",{"onUpdate:modelValue":e[4]||(e[4]=r=>o.value.task_id=r),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"},[e[27]||(e[27]=t("option",{value:""},"Nessun task specifico",-1)),(u(!0),d(h,null,f(_.value,r=>(u(),d("option",{key:r.id,value:r.id},n(r.name),9,vt))),128))],512),[[H,o.value.task_id]])])):B("",!0),t("div",null,[t("label",kt,[N(t("input",{type:"checkbox","onUpdate:modelValue":e[5]||(e[5]=r=>o.value.billable=r),class:"rounded border-gray-300 dark:border-gray-600"},null,512),[[ge,o.value.billable]]),e[29]||(e[29]=t("span",{class:"ml-2 text-sm text-gray-700 dark:text-gray-300"},"Ore fatturabili",-1))])]),t("div",xt,[t("button",{onClick:le,disabled:!o.value.project_id,class:"bg-primary-600 hover:bg-primary-700 disabled:bg-primary-400 text-white px-4 py-2 rounded-md text-sm font-medium"}," Aggiungi alla Griglia ",8,pt),t("button",{onClick:e[6]||(e[6]=r=>b.value=!1),class:"bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium"}," Annulla ")])])])])])):B("",!0)]))}};export{ht as default};

import{r as i,f as p,w as X,A as G,c as n,j as t,t as a,v as j,G as C,F as v,k,g as U,o as l,n as T}from"./vendor.js";import{u as Y}from"./app.js";const q={class:"space-y-6"},J={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},K={class:"flex justify-between items-center"},Q={class:"flex items-center space-x-4"},W={class:"flex items-center space-x-2"},Z={class:"text-center"},tt={class:"text-lg font-semibold text-gray-900 dark:text-white"},et={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-4"},st={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},rt=["value"],at=["disabled"],ot=["value"],it={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},dt={class:"overflow-x-auto"},nt={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},lt={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},ct={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},ut={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},gt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},xt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},pt={class:"px-6 py-4 whitespace-nowrap"},mt={class:"px-6 py-4 whitespace-nowrap"},ht={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},vt=["onClick"],kt=["onClick"],yt={key:0,class:"text-center py-8"},ft={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},wt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},bt={class:"flex items-center"},_t={class:"ml-5 w-0 flex-1"},jt={class:"text-lg font-medium text-gray-900 dark:text-white"},Ct={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Tt={class:"flex items-center"},St={class:"ml-5 w-0 flex-1"},Mt={class:"text-lg font-medium text-gray-900 dark:text-white"},At={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Et={class:"flex items-center"},Ft={class:"ml-5 w-0 flex-1"},zt={class:"text-lg font-medium text-gray-900 dark:text-white"},$t={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Dt={class:"flex items-center"},Bt={class:"ml-5 w-0 flex-1"},Rt={class:"text-lg font-medium text-gray-900 dark:text-white"},Pt={__name:"TimesheetEntry",setup(It){const m=Y(),c=i([]),y=i([]),f=i([]),w=i(!1),S=i(!1),d=i(""),g=i(""),x=i(new Date().getFullYear()),o=i(new Date().getMonth()+1),M=["Gennaio","Febbraio","Marzo","Aprile","Maggio","Giugno","Luglio","Agosto","Settembre","Ottobre","Novembre","Dicembre"],A=p(()=>d.value?f.value.filter(s=>s.project_id===parseInt(d.value)):[]),E=p(()=>c.value.reduce((s,e)=>s+e.hours,0)),F=p(()=>c.value.reduce((s,e)=>s+(e.billable?e.hours:0),0)),z=p(()=>c.value.reduce((s,e)=>s+(e.status==="pending"?e.hours:0),0)),$=p(()=>new Set(c.value.map(e=>e.project_id)).size),D=async()=>{try{const s=await fetch("/api/projects/",{headers:{"Content-Type":"application/json","X-CSRFToken":m.csrfToken}});if(s.ok){const e=await s.json();y.value=e.data||[]}}catch(s){console.error("Error loading projects:",s)}},B=async()=>{try{const s=await fetch("/api/tasks/",{headers:{"Content-Type":"application/json","X-CSRFToken":m.csrfToken}});if(s.ok){const e=await s.json();f.value=e.data||[]}}catch(s){console.error("Error loading tasks:",s)}},u=async()=>{w.value=!0;try{const s=new URLSearchParams({start_date:`${x.value}-${String(o.value).padStart(2,"0")}-01`,end_date:`${x.value}-${String(o.value).padStart(2,"0")}-31`});d.value&&s.append("project_id",d.value),g.value&&s.append("task_id",g.value);const e=await fetch(`/api/timesheets/?${s}`,{headers:{"Content-Type":"application/json","X-CSRFToken":m.csrfToken}});if(e.ok){const r=await e.json();c.value=r.data||[]}}catch(s){console.error("Error loading timesheets:",s)}finally{w.value=!1}},R=()=>{o.value===1?(o.value=12,x.value--):o.value--,u()},I=()=>{o.value===12?(o.value=1,x.value++):o.value++,u()},N=s=>new Date(s).toLocaleDateString("it-IT"),h=s=>!s||s===0?"0h":s%1===0?`${s}h`:`${s.toFixed(2)}h`,O=s=>{switch(s){case"approved":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";case"rejected":return"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";default:return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"}},P=s=>{switch(s){case"approved":return"Approvato";case"rejected":return"Rifiutato";default:return"In Attesa"}},L=s=>{console.log("Edit entry:",s)},V=async s=>{if(confirm("Sei sicuro di voler eliminare questa registrazione?"))try{(await fetch(`/api/timesheets/${s}`,{method:"DELETE",headers:{"Content-Type":"application/json","X-CSRFToken":m.csrfToken}})).ok&&await u()}catch(e){console.error("Error deleting entry:",e)}};return X([d,g],()=>{u()}),G(()=>{D(),B(),u()}),(s,e)=>(l(),n("div",q,[t("div",J,[t("div",K,[e[5]||(e[5]=t("div",null,[t("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Le Mie Ore"),t("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Registra le tue ore di lavoro giornaliere ")],-1)),t("div",Q,[t("div",W,[t("button",{onClick:R,class:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},e[3]||(e[3]=[t("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)])),t("div",Z,[t("h2",tt,a(M[o.value-1])+" "+a(x.value),1)]),t("button",{onClick:I,class:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},e[4]||(e[4]=[t("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)]))]),t("button",{onClick:e[0]||(e[0]=r=>S.value=!0),class:"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium"}," Aggiungi Ore ")])])]),t("div",et,[t("div",st,[t("div",null,[e[7]||(e[7]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Progetto ",-1)),j(t("select",{"onUpdate:modelValue":e[1]||(e[1]=r=>d.value=r),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[e[6]||(e[6]=t("option",{value:""},"Tutti i progetti",-1)),(l(!0),n(v,null,k(y.value,r=>(l(),n("option",{key:r.id,value:r.id},a(r.name),9,rt))),128))],512),[[C,d.value]])]),t("div",null,[e[9]||(e[9]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Task ",-1)),j(t("select",{"onUpdate:modelValue":e[2]||(e[2]=r=>g.value=r),disabled:!d.value,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500 disabled:opacity-50"},[e[8]||(e[8]=t("option",{value:""},"Tutti i task",-1)),(l(!0),n(v,null,k(A.value,r=>(l(),n("option",{key:r.id,value:r.id},a(r.name),9,ot))),128))],8,at),[[C,g.value]])]),t("div",{class:"flex items-end"},[t("button",{onClick:u,class:"w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium"}," Filtra ")])])]),t("div",it,[e[12]||(e[12]=t("div",{class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},[t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Ore Registrate ")],-1)),t("div",dt,[t("table",nt,[e[10]||(e[10]=t("thead",{class:"bg-gray-50 dark:bg-gray-700"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Data "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Progetto "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Task "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Ore "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Fatturabile "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Stato "),t("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Azioni ")])],-1)),t("tbody",lt,[(l(!0),n(v,null,k(c.value,r=>{var b,_;return l(),n("tr",{key:r.id},[t("td",ct,a(N(r.date)),1),t("td",ut,a(((b=r.project)==null?void 0:b.name)||"N/A"),1),t("td",gt,a(((_=r.task)==null?void 0:_.name)||"N/A"),1),t("td",xt,a(h(r.hours)),1),t("td",pt,[t("span",{class:T(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",r.billable?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"])},a(r.billable?"Fatturabile":"Interno"),3)]),t("td",mt,[t("span",{class:T(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",O(r.status)])},a(P(r.status)),3)]),t("td",ht,[t("button",{onClick:H=>L(r),class:"text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 mr-3"}," Modifica ",8,vt),t("button",{onClick:H=>V(r.id),class:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"}," Elimina ",8,kt)])])}),128))])]),c.value.length===0?(l(),n("div",yt,e[11]||(e[11]=[t("p",{class:"text-gray-500 dark:text-gray-400"},"Nessuna registrazione trovata per il periodo selezionato",-1)]))):U("",!0)])]),t("div",ft,[t("div",wt,[t("div",bt,[e[14]||(e[14]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",_t,[t("dl",null,[e[13]||(e[13]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Ore Totali ",-1)),t("dd",jt,a(h(E.value)),1)])])])]),t("div",Ct,[t("div",Tt,[e[16]||(e[16]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",St,[t("dl",null,[e[15]||(e[15]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Ore Fatturabili ",-1)),t("dd",Mt,a(h(F.value)),1)])])])]),t("div",At,[t("div",Et,[e[18]||(e[18]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",Ft,[t("dl",null,[e[17]||(e[17]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," In Attesa ",-1)),t("dd",zt,a(h(z.value)),1)])])])]),t("div",$t,[t("div",Dt,[e[20]||(e[20]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])])],-1)),t("div",Bt,[t("dl",null,[e[19]||(e[19]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Progetti Attivi ",-1)),t("dd",Rt,a($.value),1)])])])])])]))}};export{Pt as default};

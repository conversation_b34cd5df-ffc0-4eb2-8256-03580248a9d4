const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/ProjectCreate.js","assets/vendor.js","assets/ProjectView.js","assets/ProjectView.css","assets/ProjectEdit.js","assets/TimesheetEntry.js","assets/TimesheetRequests.js","assets/TimesheetDashboard.js","assets/TimesheetHistory.js","assets/TimesheetStatus.js","assets/TimesheetApprovals.js","assets/TimesheetAnalytics.js","assets/PersonnelDirectory.js","assets/personnel.js","assets/PersonnelOrgChart.js","assets/PersonnelOrgChart.css","assets/SkillsMatrix.js","assets/SkillsMatrix.css","assets/DepartmentList.js","assets/DepartmentCreate.js","assets/DepartmentView.js","assets/DepartmentEdit.js","assets/PersonnelAllocation.js","assets/PersonnelAdmin.js","assets/PersonnelAdmin.css","assets/PersonnelProfile.js","assets/PersonnelProfile.css","assets/Admin.js","assets/KPITemplates.js","assets/Profile.js","assets/Settings.js"])))=>i.map(i=>d[i]);
import{r as j,w as le,c as a,a as C,b as H,o as s,d as Ie,e as ge,f as x,g as _,n as S,h as R,i as A,t as c,u as ve,j as e,F as N,k as U,l as ee,m as I,p as O,q as Pe,s as he,v as K,x as Z,y as ne,z as fe,A as Q,T as Ve,B as Te,C as De,D as ue,E as Le,G as pe,H as qe,I as Re,J as Be,K as He,L as Oe}from"./vendor.js";(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))t(n);new MutationObserver(n=>{for(const m of n)if(m.type==="childList")for(const u of m.addedNodes)u.tagName==="LINK"&&u.rel==="modulepreload"&&t(u)}).observe(document,{childList:!0,subtree:!0});function l(n){const m={};return n.integrity&&(m.integrity=n.integrity),n.referrerPolicy&&(m.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?m.credentials="include":n.crossOrigin==="anonymous"?m.credentials="omit":m.credentials="same-origin",m}function t(n){if(n.ep)return;n.ep=!0;const m=l(n);fetch(n.href,m)}})();const W=j(!1);let $e=!1;const Se=o=>{o?(document.documentElement.classList.add("dark"),localStorage.setItem("darkMode","true")):(document.documentElement.classList.remove("dark"),localStorage.setItem("darkMode","false"))},Ne=()=>{$e||(le(W,o=>{Se(o)}),$e=!0)};function xe(){return Ne(),{isDarkMode:W,toggleDarkMode:()=>{W.value=!W.value},setDarkMode:t=>{W.value=t},initializeDarkMode:()=>{const t=localStorage.getItem("darkMode"),n=document.documentElement.classList.contains("dark");if(t==="true")W.value=!0;else if(t==="false")W.value=!1;else{const h=window.matchMedia("(prefers-color-scheme: dark)").matches;W.value=n||h}Se(W.value);const m=window.matchMedia("(prefers-color-scheme: dark)"),u=h=>{const g=localStorage.getItem("darkMode");(!g||g==="null")&&(W.value=h.matches)};m.addEventListener("change",u)}}}const Ue={id:"app"},Fe={__name:"App",setup(o){const{initializeDarkMode:r}=xe();return r(),(l,t)=>{const n=H("router-view");return s(),a("div",Ue,[C(n)])}}},Ke="modulepreload",We=function(o){return"/"+o},Ce={},D=function(r,l,t){let n=Promise.resolve();if(l&&l.length>0){document.getElementsByTagName("link");const u=document.querySelector("meta[property=csp-nonce]"),h=(u==null?void 0:u.nonce)||(u==null?void 0:u.getAttribute("nonce"));n=Promise.allSettled(l.map(g=>{if(g=We(g),g in Ce)return;Ce[g]=!0;const d=g.endsWith(".css"),i=d?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${g}"]${i}`))return;const f=document.createElement("link");if(f.rel=d?"stylesheet":Ke,d||(f.as="script"),f.crossOrigin="",f.href=g,h&&f.setAttribute("nonce",h),document.head.appendChild(f),d)return new Promise((k,w)=>{f.addEventListener("load",k),f.addEventListener("error",()=>w(new Error(`Unable to preload CSS for ${g}`)))})}))}function m(u){const h=new Event("vite:preloadError",{cancelable:!0});if(h.payload=u,window.dispatchEvent(h),!h.defaultPrevented)throw u}return n.then(u=>{for(const h of u||[])h.status==="rejected"&&m(h.reason);return r().catch(m)})},B=Ie.create({baseURL:"",timeout:6e4,withCredentials:!0,headers:{"Content-Type":"application/json"}});B.interceptors.request.use(o=>{var l,t;const r=(l=document.querySelector('meta[name="csrf-token"]'))==null?void 0:l.getAttribute("content");return r&&["post","put","patch","delete"].includes((t=o.method)==null?void 0:t.toLowerCase())&&(o.headers["X-CSRFToken"]=r),o},o=>Promise.reject(o));B.interceptors.response.use(o=>o,o=>{var r;return((r=o.response)==null?void 0:r.status)===401&&(localStorage.removeItem("user"),console.warn("Sessione scaduta, autenticazione richiesta")),Promise.reject(o)});const X=ge("auth",()=>{const o=localStorage.getItem("user"),r=j(o?JSON.parse(o):null),l=j(!1),t=j(null),n=j(!1),m=x(()=>!!r.value&&n.value),u={admin:["admin","manage_users","assign_roles","view_all_projects","create_project","edit_project","delete_project","assign_to_project","manage_project_tasks","manage_project_resources","approve_timesheets","view_personnel_data","edit_personnel_data","view_contracts","manage_contracts","view_crm","manage_clients","manage_proposals","view_reports","view_dashboard","submit_timesheet","view_own_timesheets","view_funding","manage_funding","view_products","manage_products","view_performance","manage_performance","view_communications","manage_communications","view_startup","manage_startup"],manager:["view_dashboard","view_all_projects","edit_project","assign_to_project","manage_project_tasks","manage_project_resources","approve_timesheets","view_personnel_data","view_crm","view_reports","submit_timesheet","view_own_timesheets","manage_clients","manage_proposals","view_funding","manage_funding","view_products","manage_products","view_performance","manage_performance","view_communications","manage_communications","view_startup","manage_startup"],employee:["view_dashboard","view_own_timesheets","submit_timesheet"],sales:["view_dashboard","view_crm","manage_clients","manage_proposals","submit_timesheet","view_own_timesheets","view_reports","view_funding","view_products","manage_products"],human_resources:["view_dashboard","manage_users","view_personnel_data","edit_personnel_data","view_contracts","manage_contracts","submit_timesheet","view_own_timesheets","view_reports","view_funding","manage_funding","view_performance","manage_performance","view_communications","manage_communications","view_startup","manage_startup"]},h=$=>!r.value||!r.value.role?!1:r.value.role==="admin"?!0:(u[r.value.role]||[]).includes($),g=()=>{var $,z;console.log("Current user:",r.value),console.log("User role:",($=r.value)==null?void 0:$.role),console.log("Has admin permission:",h("admin")),console.log("Available permissions for role:",u[(z=r.value)==null?void 0:z.role])};async function d($){var z,P;l.value=!0,t.value=null;try{const b=await B.post("/api/auth/login",$);return b.data.success?(r.value=b.data.data.user,localStorage.setItem("user",JSON.stringify(r.value)),n.value=!0,{success:!0}):(t.value=b.data.message||"Errore durante il login",{success:!1,error:t.value})}catch(b){return t.value=((P=(z=b.response)==null?void 0:z.data)==null?void 0:P.message)||"Errore di connessione",{success:!1,error:t.value}}finally{l.value=!1}}async function i($){var z,P;l.value=!0,t.value=null;try{const b=await B.post("/api/auth/register",$);return b.data.success?{success:!0,message:b.data.message}:(t.value=b.data.message||"Errore durante la registrazione",{success:!1,error:t.value})}catch(b){return t.value=((P=(z=b.response)==null?void 0:z.data)==null?void 0:P.message)||"Errore di connessione",{success:!1,error:t.value}}finally{l.value=!1}}async function f(){try{await B.post("/api/auth/logout")}catch($){console.warn("Errore durante il logout:",$)}finally{r.value=null,n.value=!1,localStorage.removeItem("user")}}async function k(){if(n.value)return m.value;try{const $=await B.get("/api/auth/me");return $.data.success?(r.value=$.data.data.user,localStorage.setItem("user",JSON.stringify(r.value)),n.value=!0,!0):(await f(),!1)}catch{return await f(),!1}}async function w(){return r.value?await k():(n.value=!0,!1)}return{user:r,loading:l,error:t,sessionChecked:n,isAuthenticated:m,hasPermission:h,debugPermissions:g,login:d,register:i,logout:f,checkAuth:k,initializeAuth:w}}),te=ge("tenant",()=>{const o=j(null),r=j(!1),l=j(null),t=x(()=>{var i;return((i=o.value)==null?void 0:i.company)||{}}),n=x(()=>{var i;return((i=o.value)==null?void 0:i.contact)||{}}),m=x(()=>{var i;return((i=o.value)==null?void 0:i.pages)||{}}),u=x(()=>{var i;return((i=o.value)==null?void 0:i.navigation)||{}}),h=x(()=>{var i;return((i=o.value)==null?void 0:i.footer)||{}});async function g(){try{if(r.value=!0,window.TENANT_CONFIG){o.value=window.TENANT_CONFIG;return}const i=await fetch("/api/config/tenant");o.value=await i.json()}catch(i){l.value="Errore nel caricamento della configurazione",console.error("Errore caricamento tenant config:",i)}finally{r.value=!1}}function d(i,f={}){if(!i||typeof i!="string")return i;let k=i;const w={"company.name":t.value.name||"DatVinci","company.tagline":t.value.tagline||"","company.description":t.value.description||"","company.mission":t.value.mission||"","company.vision":t.value.vision||"","company.founded":t.value.founded||"","company.team_size":t.value.team_size||"","contact.email":n.value.email||"","contact.phone":n.value.phone||"","contact.address":n.value.address||"",current_year:new Date().getFullYear().toString(),...f};for(const[$,z]of Object.entries(w)){const P=new RegExp(`\\{${$}\\}`,"g");k=k.replace(P,z||"")}return k}return{config:o,loading:r,error:l,company:t,contact:n,pages:m,navigation:u,footer:h,loadConfig:g,interpolateText:d}});function Ge(){const o=X(),r=x(()=>w=>o.hasPermission(w)),l=x(()=>{var w;return((w=o.user)==null?void 0:w.role)||null}),t=x(()=>l.value==="admin"),n=x(()=>l.value==="manager"),m=x(()=>l.value==="employee"),u=x(()=>l.value==="sales"),h=x(()=>l.value==="human_resources"),g=x(()=>r.value("create_project")||r.value("edit_project")||r.value("delete_project")),d=x(()=>r.value("manage_users")||r.value("assign_roles")),i=x(()=>r.value("view_all_projects")),f=x(()=>r.value("view_personnel_data")||r.value("edit_personnel_data")),k=x(()=>r.value("approve_timesheets"));return{hasPermission:r,userRole:l,isAdmin:t,isManager:n,isEmployee:m,isSales:u,isHR:h,canManageProjects:g,canManageUsers:d,canViewAllProjects:i,canManagePersonnel:f,canApproveTimesheets:k}}const Qe={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"},Je={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"},Ye={key:2,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"},Xe={key:3,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"},Ze={key:4,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"},et={key:5,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"},tt={key:6,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"},st={key:7,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"},rt={key:8,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"},ot={key:9,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"},at={key:10,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"},nt={key:11,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"},it={key:12,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"},lt={key:13,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"},dt={key:14,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"},ct={key:15,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"},ut={key:16,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"},mt={key:17,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"},pt={key:18,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"},gt={key:19,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"},vt={key:20,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"},de={__name:"SidebarIcon",props:{icon:{type:String,required:!0},className:{type:String,default:"h-5 w-5"}},setup(o){return(r,l)=>(s(),a("svg",{class:S(o.className),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o.icon==="dashboard"?(s(),a("path",Qe)):o.icon==="projects"?(s(),a("path",Je)):o.icon==="users"?(s(),a("path",Ye)):o.icon==="clients"?(s(),a("path",Xe)):o.icon==="products"?(s(),a("path",Ze)):o.icon==="reports"?(s(),a("path",et)):o.icon==="settings"?(s(),a("path",tt)):_("",!0),o.icon==="settings"?(s(),a("path",st)):o.icon==="user-management"?(s(),a("path",rt)):o.icon==="communications"?(s(),a("path",ot)):o.icon==="funding"?(s(),a("path",at)):o.icon==="reporting"?(s(),a("path",nt)):o.icon==="team"?(s(),a("path",it)):o.icon==="directory"?(s(),a("path",lt)):o.icon==="orgchart"?(s(),a("path",dt)):o.icon==="skills"?(s(),a("path",ct)):o.icon==="departments"?(s(),a("path",ut)):o.icon==="admin"?(s(),a("path",mt)):o.icon==="allocation"?(s(),a("path",pt)):o.icon==="user-profile"?(s(),a("path",gt)):(s(),a("path",vt))],2))}},ht={key:0,class:"truncate"},ft={key:0,class:"truncate"},Y={__name:"SidebarNavItem",props:{item:{type:Object,required:!0},isCollapsed:{type:Boolean,default:!1}},emits:["click"],setup(o){const r=x(()=>["text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400"]);return(l,t)=>{const n=H("router-link");return s(),a("div",null,[o.item.path!=="#"?(s(),R(n,{key:0,to:o.item.path,class:S(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",[r.value,{"justify-center":o.isCollapsed}]]),"active-class":"text-primary-600 bg-primary-50 border-r-2 border-primary-600",onClick:t[0]||(t[0]=m=>l.$emit("click"))},{default:A(()=>[C(de,{icon:o.item.icon,class:S(["flex-shrink-0 h-6 w-6",{"mr-0":o.isCollapsed,"mr-3":!o.isCollapsed}])},null,8,["icon","class"]),o.isCollapsed?_("",!0):(s(),a("span",ht,c(o.item.name),1))]),_:1},8,["to","class"])):(s(),a("div",{key:1,class:S(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150 cursor-not-allowed opacity-75",["text-gray-400 hover:text-gray-500",{"justify-center":o.isCollapsed}]])},[C(de,{icon:o.item.icon,class:S(["flex-shrink-0 h-6 w-6",{"mr-0":o.isCollapsed,"mr-3":!o.isCollapsed}])},null,8,["icon","class"]),o.isCollapsed?_("",!0):(s(),a("span",ft,c(o.item.name),1))],2))])}}},xt={key:0,class:"flex-1 text-left truncate"},yt={key:0,class:"ml-6 space-y-1 mt-1"},_t={class:"truncate"},me={__name:"SidebarNavItemCollapsible",props:{item:{type:Object,required:!0},isCollapsed:{type:Boolean,default:!1}},emits:["click"],setup(o){const r=o,l=ve(),t=X(),n=j(!1),m=x(()=>["text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400",{"text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900":u.value}]),u=x(()=>r.item.children?r.item.children.some(i=>i.path!=="#"&&l.path.startsWith(i.path)):!1),h=x(()=>r.item.children?r.item.children.filter(i=>{var f;return i.admin?((f=t.user)==null?void 0:f.role)==="admin":!0}):[]);u.value&&(n.value=!0);function g(){r.isCollapsed||(n.value=!n.value)}function d(i){if(i.path==="#")return!1}return(i,f)=>{const k=H("router-link");return s(),a("div",null,[e("button",{onClick:g,class:S(["group w-full flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",[m.value,{"justify-center":o.isCollapsed}]])},[C(de,{icon:o.item.icon,class:S(["flex-shrink-0 h-6 w-6",{"mr-0":o.isCollapsed,"mr-3":!o.isCollapsed}])},null,8,["icon","class"]),o.isCollapsed?_("",!0):(s(),a("span",xt,c(o.item.name),1)),o.isCollapsed?_("",!0):(s(),a("svg",{key:1,class:S([{"rotate-90":n.value},"ml-2 h-4 w-4 transition-transform duration-150"]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},f[0]||(f[0]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"},null,-1)]),2))],2),n.value&&!o.isCollapsed?(s(),a("div",yt,[(s(!0),a(N,null,U(h.value,w=>(s(),R(k,{key:w.name,to:w.path,class:S(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",w.path==="#"?"text-gray-400 dark:text-gray-500 hover:text-gray-500 dark:hover:text-gray-400 cursor-not-allowed opacity-75":"text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400"]),"active-class":"text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900",onClick:$=>d(w)},{default:A(()=>[w.icon?(s(),R(de,{key:0,icon:w.icon,class:"flex-shrink-0 h-4 w-4 mr-2"},null,8,["icon"])):_("",!0),e("span",_t,c(w.name),1)]),_:2},1032,["to","class","onClick"]))),128))])):_("",!0)])}}},kt={class:"mt-5 flex-grow flex flex-col overflow-hidden"},bt={class:"flex-1 px-2 space-y-1"},Me={__name:"SidebarNavigation",props:{isCollapsed:{type:Boolean,default:!1}},emits:["item-click"],setup(o){const{hasPermission:r}=Ge(),l=x(()=>r.value("view_dashboard")),t=x(()=>r.value("view_personnel_data")),n=x(()=>r.value("view_all_projects")),m=x(()=>r.value("manage_timesheets")||r.value("view_all_projects")),u=x(()=>r.value("view_crm")),h=x(()=>r.value("view_products")),g=x(()=>r.value("view_performance")),d=x(()=>r.value("view_communications")),i=x(()=>r.value("view_funding")),f=x(()=>r.value("view_reports")),k=x(()=>r.value("admin_access"));return(w,$)=>(s(),a("div",kt,[e("nav",bt,[l.value?(s(),R(Y,{key:0,item:{name:"Dashboard",path:"/app/dashboard",icon:"dashboard"},"is-collapsed":o.isCollapsed,onClick:$[0]||($[0]=z=>w.$emit("item-click"))},null,8,["is-collapsed"])):_("",!0),t.value?(s(),R(me,{key:1,item:{name:"Personale",icon:"users",children:[{name:"Directory",path:"/app/personnel",icon:"directory"},{name:"Organigramma",path:"/app/personnel/orgchart",icon:"orgchart"},{name:"Competenze",path:"/app/personnel/skills",icon:"skills"},{name:"Allocazione Risorse",path:"/app/personnel/allocation",icon:"allocation"},{name:"Dipartimenti",path:"/app/personnel/departments",icon:"departments",admin:!0},{name:"Amministrazione",path:"/app/personnel/admin",icon:"admin",admin:!0}]},"is-collapsed":o.isCollapsed,onClick:$[1]||($[1]=z=>w.$emit("item-click"))},null,8,["is-collapsed"])):_("",!0),n.value?(s(),R(Y,{key:2,item:{name:"Progetti",path:"/app/projects",icon:"projects"},"is-collapsed":o.isCollapsed,onClick:$[2]||($[2]=z=>w.$emit("item-click"))},null,8,["is-collapsed"])):_("",!0),m.value?(s(),R(me,{key:3,item:{name:"Timesheet",icon:"clock",children:[{name:"Le Mie Ore",path:"/app/timesheet/entry",icon:"clock"},{name:"Richieste",path:"/app/timesheet/requests",icon:"calendar-plus"},{name:"Dashboard",path:"/app/timesheet/dashboard",icon:"chart-bar"},{name:"Storico",path:"/app/timesheet/history",icon:"history"},{name:"Stato Approvazioni",path:"/app/timesheet/status",icon:"check-circle"},{name:"Approvazioni Team",path:"/app/timesheet/approvals",icon:"users-check",manager:!0},{name:"Report & Analytics",path:"/app/timesheet/analytics",icon:"analytics",admin:!0}]},"is-collapsed":o.isCollapsed,onClick:$[3]||($[3]=z=>w.$emit("item-click"))},null,8,["is-collapsed"])):_("",!0),u.value?(s(),R(Y,{key:4,item:{name:"CRM",path:"#",icon:"clients"},"is-collapsed":o.isCollapsed,onClick:$[4]||($[4]=z=>w.$emit("item-click"))},null,8,["is-collapsed"])):_("",!0),h.value?(s(),R(Y,{key:5,item:{name:"Prodotti",path:"#",icon:"products"},"is-collapsed":o.isCollapsed,onClick:$[5]||($[5]=z=>w.$emit("item-click"))},null,8,["is-collapsed"])):_("",!0),g.value?(s(),R(Y,{key:6,item:{name:"Performance",path:"#",icon:"reports"},"is-collapsed":o.isCollapsed,onClick:$[6]||($[6]=z=>w.$emit("item-click"))},null,8,["is-collapsed"])):_("",!0),d.value?(s(),R(Y,{key:7,item:{name:"Comunicazione",path:"#",icon:"communications"},"is-collapsed":o.isCollapsed,onClick:$[7]||($[7]=z=>w.$emit("item-click"))},null,8,["is-collapsed"])):_("",!0),i.value?(s(),R(Y,{key:8,item:{name:"Finanziamenti",path:"#",icon:"funding"},"is-collapsed":o.isCollapsed,onClick:$[8]||($[8]=z=>w.$emit("item-click"))},null,8,["is-collapsed"])):_("",!0),f.value?(s(),R(Y,{key:9,item:{name:"Rendicontazione",path:"#",icon:"reporting"},"is-collapsed":o.isCollapsed,onClick:$[9]||($[9]=z=>w.$emit("item-click"))},null,8,["is-collapsed"])):_("",!0),k.value?(s(),R(me,{key:10,item:{name:"Amministrazione",icon:"settings",children:[{name:"Gestione Utenti",path:"/app/admin/users",icon:"user-management"},{name:"Template KPI",path:"/app/admin/kpi-templates",icon:"reports"}]},"is-collapsed":o.isCollapsed,onClick:$[10]||($[10]=z=>w.$emit("item-click"))},null,8,["is-collapsed"])):_("",!0)])]))}},wt={class:"flex-shrink-0 border-t border-gray-200 p-4"},$t={class:"flex-shrink-0"},Ct={class:"h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center"},Mt={class:"text-sm font-medium text-primary-700"},jt={key:0,class:"ml-3 flex-1 min-w-0"},zt={class:"text-sm font-medium text-gray-900 truncate"},Pt={class:"text-xs text-gray-500 truncate"},St={class:"py-1"},At={key:0,class:"mt-3 text-xs text-gray-400 text-center"},je={__name:"SidebarFooter",props:{isCollapsed:{type:Boolean,default:!1}},setup(o){const r=ee(),l=X(),t=j(!1),n=x(()=>l.user&&(l.user.name||l.user.username)||"Utente"),m=x(()=>l.user?n.value.charAt(0).toUpperCase():"U"),u=x(()=>l.user?{admin:"Amministratore",manager:"Manager",employee:"Dipendente",client:"Cliente"}[l.user.role]||l.user.role:""),h=x(()=>"1.0.0");async function g(){t.value=!1,await l.logout(),r.push("/auth/login")}return(d,i)=>{const f=H("router-link");return s(),a("div",wt,[e("div",{class:S(["flex items-center",{"justify-center":o.isCollapsed}])},[e("div",$t,[e("div",Ct,[e("span",Mt,c(m.value),1)])]),o.isCollapsed?_("",!0):(s(),a("div",jt,[e("p",zt,c(n.value),1),e("p",Pt,c(u.value),1)])),e("div",{class:S(["relative",{"ml-3":!o.isCollapsed}])},[e("button",{onClick:i[0]||(i[0]=k=>t.value=!t.value),class:"p-1 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"},i[4]||(i[4]=[e("svg",{class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zM13 12a1 1 0 11-2 0 1 1 0 012 0zM20 12a1 1 0 11-2 0 1 1 0 012 0z"})],-1)])),t.value?(s(),a("div",{key:0,onClick:i[3]||(i[3]=k=>t.value=!1),class:"origin-bottom-left fixed bottom-16 left-4 w-48 rounded-md shadow-xl bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 border border-gray-200 dark:border-gray-600",style:{"z-index":"99999"}},[e("div",St,[C(f,{to:"/app/profile",class:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:i[1]||(i[1]=k=>t.value=!1)},{default:A(()=>i[5]||(i[5]=[I(" Il tuo profilo ")])),_:1,__:[5]}),C(f,{to:"/app/settings",class:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:i[2]||(i[2]=k=>t.value=!1)},{default:A(()=>i[6]||(i[6]=[I(" Impostazioni ")])),_:1,__:[6]}),i[7]||(i[7]=e("hr",{class:"my-1 border-gray-200 dark:border-gray-600"},null,-1)),e("button",{onClick:g,class:"block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"}," Esci ")])])):_("",!0)],2)],2),h.value&&!o.isCollapsed?(s(),a("div",At," v"+c(h.value),1)):_("",!0)])}}},Et={class:"flex"},It={class:"hidden lg:flex lg:flex-shrink-0 lg:fixed lg:inset-y-0 z-10"},Vt={class:"flex flex-col flex-grow pt-5 pb-4 overflow-y-auto bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 shadow-sm"},Tt={class:"flex items-center flex-shrink-0 px-4"},Dt={class:"w-10 h-10 bg-primary-600 rounded flex items-center justify-center mr-3"},Lt={class:"text-white font-bold text-lg"},qt={class:"text-xl font-semibold text-gray-900 dark:text-white"},Rt={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center"},Bt={class:"text-white font-bold text-sm"},Ht={class:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},Ot=["d"],Nt={class:"flex flex-col h-full pt-5 pb-4 overflow-y-auto bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 shadow-sm"},Ut={class:"flex items-center justify-between px-4 mb-4"},Ft={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center mr-3"},Kt={class:"text-white font-bold text-sm"},Wt={class:"text-xl font-semibold text-gray-900 dark:text-white"},Gt={__name:"AppSidebar",props:{isMobileOpen:{type:Boolean,default:!1}},emits:["close","toggle-collapsed"],setup(o,{emit:r}){const l=r,t=te(),n=j(!1),m=x(()=>t.config||{}),u=x(()=>{var i;return((i=m.value.company)==null?void 0:i.name)||"DatPortal"}),h=x(()=>u.value.split(" ").map(f=>f[0]).join("").toUpperCase().slice(0,2));function g(){n.value=!n.value,l("toggle-collapsed",n.value)}function d(){n.value&&(n.value=!1)}return(i,f)=>{const k=H("router-link");return s(),a("div",Et,[e("div",It,[e("div",{class:S(["flex flex-col transition-all duration-300",[n.value?"w-20":"w-64"]])},[e("div",Vt,[e("div",Tt,[e("div",{class:S(["flex items-center",{"justify-center":n.value}])},[C(k,{to:"/app/dashboard",class:S(["flex items-center",{hidden:n.value}])},{default:A(()=>[e("div",Dt,[e("span",Lt,c(h.value),1)]),e("h3",qt,c(u.value),1)]),_:1},8,["class"]),C(k,{to:"/app/dashboard",class:S(["flex items-center justify-center",{hidden:!n.value}])},{default:A(()=>[e("div",Rt,[e("span",Bt,c(h.value),1)])]),_:1},8,["class"])],2),e("button",{onClick:g,class:"ml-auto text-gray-600 dark:text-gray-400 focus:outline-none hover:bg-gray-100 dark:hover:bg-gray-700 p-1 rounded"},[(s(),a("svg",Ht,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:n.value?"M13 5l7 7-7 7M5 5l7 7-7 7":"M11 19l-7-7 7-7m8 14l-7-7 7-7"},null,8,Ot)]))])]),C(Me,{"is-collapsed":n.value,onItemClick:d},null,8,["is-collapsed"]),C(je,{"is-collapsed":n.value},null,8,["is-collapsed"])])],2)]),e("div",{class:S(["fixed inset-y-0 left-0 z-30 w-64 bg-primary-700 transform transition-transform duration-300 ease-in-out lg:hidden",o.isMobileOpen?"translate-x-0":"-translate-x-full"])},[e("div",Nt,[e("div",Ut,[C(k,{to:"/app/dashboard",class:"flex items-center"},{default:A(()=>[e("div",Ft,[e("span",Kt,c(h.value),1)]),e("h3",Wt,c(u.value),1)]),_:1}),e("button",{onClick:f[0]||(f[0]=w=>i.$emit("close")),class:"p-2 rounded-md text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700"},f[2]||(f[2]=[e("svg",{class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),C(Me,{"is-collapsed":!1,onItemClick:f[1]||(f[1]=w=>i.$emit("close"))}),C(je,{"is-collapsed":!1})])],2)])}}},Qt={class:"flex","aria-label":"Breadcrumb"},Jt={class:"flex items-center space-x-2 text-sm text-gray-500"},Yt={key:0,class:"mr-2"},Xt={class:"flex items-center"},Zt={key:0,class:"h-3 w-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},es=["d"],ts={key:2,class:"font-medium text-gray-900"},ss={__name:"HeaderBreadcrumbs",props:{breadcrumbs:{type:Array,required:!0}},setup(o){return(r,l)=>{const t=H("router-link");return s(),a("nav",Qt,[e("ol",Jt,[(s(!0),a(N,null,U(o.breadcrumbs,(n,m)=>(s(),a("li",{key:m,class:"flex items-center"},[m>0?(s(),a("div",Yt,l[0]||(l[0]=[e("svg",{class:"h-3 w-3 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})],-1)]))):_("",!0),n.to&&m<o.breadcrumbs.length-1?(s(),R(t,{key:1,to:n.to,class:"hover:text-gray-700 transition-colors duration-150"},{default:A(()=>[e("span",Xt,[n.icon?(s(),a("svg",Zt,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:n.icon},null,8,es)])):_("",!0),I(" "+c(n.label),1)])]),_:2},1032,["to"])):(s(),a("span",ts,c(n.label),1))]))),128))])])}}},rs={class:"flex items-center space-x-2"},os={key:0,class:"h-3 w-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},as={key:1,class:"h-3 w-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},ns={__name:"HeaderQuickActions",emits:["quick-create-project"],setup(o){const r=ve(),{isDarkMode:l,toggleDarkMode:t}=xe(),n=x(()=>{var m;return((m=r.name)==null?void 0:m.includes("projects"))||r.path.includes("/projects")});return(m,u)=>(s(),a("div",rs,[n.value?(s(),a("button",{key:0,onClick:u[0]||(u[0]=h=>m.$emit("quick-create-project")),class:"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},u[2]||(u[2]=[e("svg",{class:"h-3 w-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),I(" Nuovo Progetto ")]))):_("",!0),e("button",{onClick:u[1]||(u[1]=(...h)=>O(t)&&O(t)(...h)),class:"inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700",title:"Cambia tema"},[O(l)?(s(),a("svg",as,u[4]||(u[4]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"},null,-1)]))):(s(),a("svg",os,u[3]||(u[3]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"},null,-1)])))])]))}},is={class:"relative"},ls={class:"relative"},ds={key:0,class:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center"},cs={class:"py-1"},us={key:0,class:"px-4 py-8 text-center text-gray-500 text-sm"},ms={key:1,class:"max-h-64 overflow-y-auto"},ps=["onClick"],gs={class:"flex items-start"},vs={class:"flex-shrink-0"},hs={class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},fs=["d"],xs={class:"ml-3 flex-1"},ys={class:"text-sm font-medium text-gray-900"},_s={class:"text-xs text-gray-500 mt-1"},ks={class:"text-xs text-gray-400 mt-1"},bs={key:0,class:"flex-shrink-0"},ws={key:2,class:"px-4 py-2 border-t border-gray-100"},$s={__name:"HeaderNotifications",setup(o){const r=j(!1),l=j([{id:1,type:"task",title:"Nuovo task assegnato",message:'Ti è stato assegnato un nuovo task nel progetto "Website Redesign"',created_at:new Date().toISOString(),read:!1},{id:2,type:"project",title:"Progetto completato",message:'Il progetto "Mobile App" è stato completato con successo',created_at:new Date(Date.now()-36e5).toISOString(),read:!0}]),t=x(()=>l.value.filter(d=>!d.read).length);function n(d){const i={task:"h-6 w-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center",project:"h-6 w-6 rounded-full bg-green-100 text-green-600 flex items-center justify-center",user:"h-6 w-6 rounded-full bg-purple-100 text-purple-600 flex items-center justify-center",system:"h-6 w-6 rounded-full bg-gray-100 text-gray-600 flex items-center justify-center"};return i[d]||i.system}function m(d){const i={task:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2",project:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10",user:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z",system:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"};return i[d]||i.system}function u(d){const i=new Date(d),k=new Date-i;return k<6e4?"Adesso":k<36e5?`${Math.floor(k/6e4)}m fa`:k<864e5?`${Math.floor(k/36e5)}h fa`:i.toLocaleDateString("it-IT")}function h(d){d.read||(d.read=!0),r.value=!1}function g(){l.value.forEach(d=>d.read=!0)}return(d,i)=>(s(),a("div",is,[e("button",{onClick:i[0]||(i[0]=f=>r.value=!r.value),class:"p-2 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[i[3]||(i[3]=e("span",{class:"sr-only"},"Visualizza notifiche",-1)),e("div",ls,[i[2]||(i[2]=e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 17h5l-5 5v-5zM10 21a2 2 0 01-2-2V7a7 7 0 1114 0v12a2 2 0 01-2 2H10z"})],-1)),t.value>0?(s(),a("span",ds,c(t.value>9?"9+":t.value),1)):_("",!0)])]),r.value?(s(),a("div",{key:0,onClick:i[1]||(i[1]=f=>r.value=!1),class:"origin-top-right absolute right-0 mt-2 w-80 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50"},[e("div",cs,[i[5]||(i[5]=e("div",{class:"px-4 py-2 border-b border-gray-100"},[e("h3",{class:"text-sm font-medium text-gray-900"},"Notifiche")],-1)),l.value.length===0?(s(),a("div",us," Nessuna notifica ")):(s(),a("div",ms,[(s(!0),a(N,null,U(l.value,f=>(s(),a("div",{key:f.id,class:"px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-50 last:border-b-0",onClick:k=>h(f)},[e("div",gs,[e("div",vs,[e("div",{class:S(n(f.type))},[(s(),a("svg",hs,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:m(f.type)},null,8,fs)]))],2)]),e("div",xs,[e("p",ys,c(f.title),1),e("p",_s,c(f.message),1),e("p",ks,c(u(f.created_at)),1)]),f.read?_("",!0):(s(),a("div",bs,i[4]||(i[4]=[e("div",{class:"h-2 w-2 bg-primary-500 rounded-full"},null,-1)])))])],8,ps))),128))])),l.value.length>0?(s(),a("div",ws,[e("button",{onClick:g,class:"text-xs text-primary-600 hover:text-primary-800"}," Segna tutte come lette ")])):_("",!0)])])):_("",!0)]))}},Cs={class:"relative"},Ms={class:"flex items-start justify-center min-h-screen pt-16 px-4 pb-20 text-center sm:block sm:p-0"},js={class:"inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6"},zs={class:"flex items-center"},Ps={class:"flex-1"},Ss={key:0,class:"mt-4 max-h-64 overflow-y-auto"},As={class:"space-y-1"},Es=["onClick"],Is={class:"flex-shrink-0"},Vs={class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Ts=["d"],Ds={class:"ml-3 flex-1 min-w-0"},Ls={class:"text-sm font-medium text-gray-900 truncate"},qs={class:"text-xs text-gray-500 truncate"},Rs={class:"ml-2 text-xs text-gray-400"},Bs={key:1,class:"mt-4 text-center py-4"},Hs={key:2,class:"mt-4 text-center py-4"},Os={__name:"HeaderSearch",setup(o){const r=ee(),l=j(!1),t=j(""),n=j([]),m=j(-1),u=j(!1),h=j(null),g=[{id:1,type:"project",title:"Website Redesign",description:"Progetto di redesign del sito web aziendale",path:"/app/projects/1"},{id:2,type:"person",title:"Mario Rossi",description:"Senior Developer",path:"/app/personnel/directory/1"},{id:3,type:"document",title:"Specifiche Tecniche",description:"Documento delle specifiche del progetto mobile",path:"/app/documents/1"},{id:4,type:"task",title:"Implementazione API",description:"Task per l'implementazione delle API REST",path:"/app/projects/1/tasks/5"}];le(l,async P=>{var b;P?(await Pe(),(b=h.value)==null||b.focus()):(t.value="",n.value=[],m.value=-1)});function d(){if(!t.value.trim()){n.value=[];return}u.value=!0,setTimeout(()=>{n.value=g.filter(P=>P.title.toLowerCase().includes(t.value.toLowerCase())||P.description.toLowerCase().includes(t.value.toLowerCase())),m.value=-1,u.value=!1},200)}function i(P){if(n.value.length===0)return;const b=m.value+P;b>=0&&b<n.value.length&&(m.value=b)}function f(){m.value>=0&&n.value[m.value]&&k(n.value[m.value])}function k(P){l.value=!1,r.push(P.path)}function w(P){const b={project:"h-6 w-6 rounded bg-blue-100 text-blue-600 flex items-center justify-center",person:"h-6 w-6 rounded bg-green-100 text-green-600 flex items-center justify-center",document:"h-6 w-6 rounded bg-yellow-100 text-yellow-600 flex items-center justify-center",task:"h-6 w-6 rounded bg-purple-100 text-purple-600 flex items-center justify-center"};return b[P]||b.document}function $(P){const b={project:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10",person:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z",document:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z",task:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"};return b[P]||b.document}function z(P){return{project:"Progetto",person:"Persona",document:"Documento",task:"Task"}[P]||"Elemento"}return(P,b)=>(s(),a("div",Cs,[e("button",{onClick:b[0]||(b[0]=q=>l.value=!l.value),class:"p-2 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},b[7]||(b[7]=[e("span",{class:"sr-only"},"Cerca",-1),e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)])),l.value?(s(),a("div",{key:0,class:"fixed inset-0 z-50 overflow-y-auto",onClick:b[6]||(b[6]=he(q=>l.value=!1,["self"]))},[e("div",Ms,[b[11]||(b[11]=e("div",{class:"fixed inset-0 bg-black bg-opacity-25 transition-opacity"},null,-1)),e("div",js,[e("div",null,[e("div",zs,[e("div",Ps,[K(e("input",{ref_key:"searchInput",ref:h,"onUpdate:modelValue":b[1]||(b[1]=q=>t.value=q),onInput:d,onKeydown:[b[2]||(b[2]=ne(q=>l.value=!1,["escape"])),ne(f,["enter"]),b[3]||(b[3]=ne(q=>i(-1),["up"])),b[4]||(b[4]=ne(q=>i(1),["down"]))],type:"text",placeholder:"Cerca progetti, persone, documenti...",class:"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"},null,544),[[Z,t.value]])]),e("button",{onClick:b[5]||(b[5]=q=>l.value=!1),class:"ml-3 p-2 text-gray-400 hover:text-gray-600"},b[8]||(b[8]=[e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),n.value.length>0?(s(),a("div",Ss,[e("div",As,[(s(!0),a(N,null,U(n.value,(q,G)=>(s(),a("div",{key:q.id,onClick:se=>k(q),class:S(["flex items-center px-3 py-2 rounded-md cursor-pointer",G===m.value?"bg-primary-50":"hover:bg-gray-50"])},[e("div",Is,[e("div",{class:S(w(q.type))},[(s(),a("svg",Vs,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:$(q.type)},null,8,Ts)]))],2)]),e("div",Ds,[e("p",Ls,c(q.title),1),e("p",qs,c(q.description),1)]),e("div",Rs,c(z(q.type)),1)],10,Es))),128))])])):t.value&&!u.value?(s(),a("div",Bs,b[9]||(b[9]=[e("p",{class:"text-sm text-gray-500"},"Nessun risultato trovato",-1)]))):t.value?_("",!0):(s(),a("div",Hs,b[10]||(b[10]=[e("p",{class:"text-xs text-gray-400"},"Inizia a digitare per cercare...",-1)])))])])])])):_("",!0)]))}},Ns={class:"relative"},Us={class:"h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center"},Fs={class:"text-sm font-medium text-primary-700"},Ks={class:"py-1"},Ws={class:"px-4 py-2 border-b border-gray-100 dark:border-gray-700"},Gs={class:"text-sm font-medium text-gray-900 dark:text-white"},Qs={class:"text-xs text-gray-500 dark:text-gray-400"},Js={__name:"HeaderUserMenu",setup(o){const r=ee(),l=X(),t=j(!1),{isDarkMode:n,toggleDarkMode:m}=xe(),u=x(()=>l.user&&(l.user.name||l.user.username)||"Utente"),h=x(()=>{var i;return((i=l.user)==null?void 0:i.email)||""}),g=x(()=>l.user?u.value.charAt(0).toUpperCase():"U");async function d(){t.value=!1,await l.logout(),r.push("/auth/login")}return(i,f)=>{const k=H("router-link");return s(),a("div",Ns,[e("button",{onClick:f[0]||(f[0]=w=>t.value=!t.value),class:"flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[f[5]||(f[5]=e("span",{class:"sr-only"},"Apri menu utente",-1)),e("div",Us,[e("span",Fs,c(g.value),1)])]),t.value?(s(),a("div",{key:0,onClick:f[4]||(f[4]=w=>t.value=!1),class:"origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 z-50"},[e("div",Ks,[e("div",Ws,[e("p",Gs,c(u.value),1),e("p",Qs,c(h.value),1)]),C(k,{to:"/app/profile",class:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:f[1]||(f[1]=w=>t.value=!1)},{default:A(()=>f[6]||(f[6]=[I(" Il tuo profilo ")])),_:1,__:[6]}),C(k,{to:"/app/settings",class:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:f[2]||(f[2]=w=>t.value=!1)},{default:A(()=>f[7]||(f[7]=[I(" Impostazioni ")])),_:1,__:[7]}),f[8]||(f[8]=e("div",{class:"border-t border-gray-100 dark:border-gray-700 my-1"},null,-1)),e("button",{onClick:f[3]||(f[3]=(...w)=>O(m)&&O(m)(...w)),class:"flex items-center justify-between w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"},[e("span",null,c(O(n)?"Modalità chiara":"Modalità scura"),1),e("i",{class:S([O(n)?"fas fa-sun":"fas fa-moon","text-xs"])},null,2)]),e("button",{onClick:d,class:"block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"}," Esci ")])])):_("",!0)])}}},Ys={class:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700"},Xs={class:"flex justify-between items-center px-4 py-4 sm:px-6 lg:px-8"},Zs={class:"flex items-center space-x-4"},er={class:"flex flex-col"},tr={class:"text-lg font-semibold text-gray-900 dark:text-white"},sr={class:"flex items-center space-x-4"},rr={class:"hidden md:flex items-center space-x-2"},or={__name:"AppHeader",props:{pageTitle:{type:String,required:!0},breadcrumbs:{type:Array,default:()=>[]}},emits:["toggle-mobile-sidebar","quick-create-project"],setup(o){return(r,l)=>(s(),a("header",Ys,[e("div",Xs,[e("div",Zs,[e("button",{onClick:l[0]||(l[0]=t=>r.$emit("toggle-mobile-sidebar")),class:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:text-gray-500 dark:hover:text-gray-300 dark:hover:bg-gray-700"},l[2]||(l[2]=[e("svg",{class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"})],-1)])),e("div",er,[e("h2",tr,c(o.pageTitle),1),o.breadcrumbs.length>0?(s(),R(ss,{key:0,breadcrumbs:o.breadcrumbs},null,8,["breadcrumbs"])):_("",!0)])]),e("div",sr,[e("div",rr,[C(ns,{onQuickCreateProject:l[1]||(l[1]=t=>r.$emit("quick-create-project"))})]),C($s),C(Os),C(Js)])])]))}},ar={__name:"LoadingSpinner",props:{size:{type:String,default:"md",validator:o=>["sm","md","lg","xl"].includes(o)},message:{type:String,default:""},centered:{type:Boolean,default:!0}},setup(o){const r=o,l=x(()=>{const u={sm:"20px",md:"32px",lg:"48px",xl:"64px"};return`width: ${u[r.size]}; height: ${u[r.size]};`}),t=x(()=>["flex",r.centered?"items-center justify-center":"","space-y-2"]),n=x(()=>["flex items-center justify-center"]),m=x(()=>["text-sm text-gray-600 text-center"]);return(u,h)=>(s(),a("div",{class:S(t.value)},[e("div",{class:S(n.value)},[e("div",{class:"animate-spin rounded-full border-2 border-gray-300 border-t-primary-600",style:fe(l.value)},null,4)],2),o.message?(s(),a("p",{key:0,class:S(m.value)},c(o.message),3)):_("",!0)],2))}},Ae=(o,r)=>{const l=o.__vccOpts||o;for(const[t,n]of r)l[t]=n;return l},nr={class:"fixed bottom-0 right-0 z-50 p-6 space-y-4"},ir={class:"p-4"},lr={class:"flex items-start"},dr={class:"flex-shrink-0"},cr={class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},ur=["d"],mr={class:"ml-3 w-0 flex-1 pt-0.5"},pr={class:"text-sm font-medium text-gray-900"},gr={class:"mt-1 text-sm text-gray-500"},vr={class:"ml-4 flex-shrink-0 flex"},hr=["onClick"],fr={__name:"NotificationManager",setup(o){const r=j([]);function l(h){const g=Date.now(),d={id:g,type:h.type||"info",title:h.title,message:h.message,duration:h.duration||5e3};r.value.push(d),d.duration>0&&setTimeout(()=>{t(g)},d.duration)}function t(h){const g=r.value.findIndex(d=>d.id===h);g>-1&&r.value.splice(g,1)}function n(h){const g={success:"border-l-4 border-green-400",error:"border-l-4 border-red-400",warning:"border-l-4 border-yellow-400",info:"border-l-4 border-blue-400"};return g[h]||g.info}function m(h){const g={success:"h-8 w-8 rounded-full bg-green-100 text-green-600 flex items-center justify-center",error:"h-8 w-8 rounded-full bg-red-100 text-red-600 flex items-center justify-center",warning:"h-8 w-8 rounded-full bg-yellow-100 text-yellow-600 flex items-center justify-center",info:"h-8 w-8 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center"};return g[h]||g.info}function u(h){const g={success:"M5 13l4 4L19 7",error:"M6 18L18 6M6 6l12 12",warning:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-2.694-.833-3.464 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z",info:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"};return g[h]||g.info}return window.showNotification=l,Q(()=>{}),(h,g)=>(s(),a("div",nr,[C(Ve,{name:"notification",tag:"div",class:"space-y-4"},{default:A(()=>[(s(!0),a(N,null,U(r.value,d=>(s(),a("div",{key:d.id,class:S([n(d.type),"max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden"])},[e("div",ir,[e("div",lr,[e("div",dr,[e("div",{class:S(m(d.type))},[(s(),a("svg",cr,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:u(d.type)},null,8,ur)]))],2)]),e("div",mr,[e("p",pr,c(d.title),1),e("p",gr,c(d.message),1)]),e("div",vr,[e("button",{onClick:i=>t(d.id),class:"bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},g[0]||(g[0]=[e("span",{class:"sr-only"},"Chiudi",-1),e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,hr)])])])],2))),128))]),_:1})]))}},xr=Ae(fr,[["__scopeId","data-v-220f0827"]]),yr={class:"h-screen flex bg-gray-50 dark:bg-gray-900"},_r={class:"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900"},kr={class:"py-6"},br={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},wr={key:0,class:"mb-6"},$r={key:1,class:"flex items-center justify-center h-64"},Cr={__name:"AppLayout",setup(o){const r=ve(),l=ee(),t=te(),n=j(!1),m=j(!1),u=j(!1);x(()=>t.config||{});const h=x(()=>t.config!==null),g=x(()=>{var P;return(P=r.meta)!=null&&P.title?r.meta.title:{dashboard:"Dashboard",projects:"Progetti","projects-list":"Elenco Progetti","projects-view":"Dettaglio Progetto","projects-create":"Nuovo Progetto",personnel:"Personale","personnel-directory":"Rubrica Aziendale","personnel-orgchart":"Organigramma","personnel-skills":"Competenze"}[r.name]||"DatPortal"}),d=x(()=>{var z;return(z=r.meta)!=null&&z.breadcrumbs?r.meta.breadcrumbs.map(P=>({label:P.label,to:P.to,icon:P.icon})):[]}),i=x(()=>{var z;return((z=r.meta)==null?void 0:z.hasActions)||!1});function f(){n.value=!n.value}function k(){n.value=!1}function w(z){m.value=z}function $(){l.push("/app/projects/create")}return le(r,()=>{u.value=!0,setTimeout(()=>{u.value=!1},300)}),le(r,()=>{k()}),Q(()=>{h.value||t.loadConfig()}),(z,P)=>{const b=H("router-view");return s(),a("div",yr,[n.value?(s(),a("div",{key:0,onClick:k,class:"fixed inset-0 z-20 bg-black bg-opacity-50 lg:hidden"})):_("",!0),C(Gt,{"is-mobile-open":n.value,onClose:k,onToggleCollapsed:w},null,8,["is-mobile-open"]),e("div",{class:S(["flex flex-col flex-1 overflow-hidden transition-all duration-300",[m.value?"lg:ml-20":"lg:ml-64"]])},[C(or,{"page-title":g.value,breadcrumbs:d.value,onToggleMobileSidebar:f,onQuickCreateProject:$},null,8,["page-title","breadcrumbs"]),e("main",_r,[e("div",kr,[e("div",br,[i.value?(s(),a("div",wr,[Te(z.$slots,"page-actions")])):_("",!0),u.value?(s(),a("div",$r,[C(ar)])):(s(),R(b,{key:2}))])])])],2),C(xr)])}}},Mr={class:"min-h-screen bg-gray-50"},jr={class:"bg-white shadow-sm border-b"},zr={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},Pr={class:"flex justify-between h-16"},Sr={class:"flex items-center"},Ar={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center"},Er={class:"text-white font-bold text-sm"},Ir={class:"text-xl font-semibold text-gray-900"},Vr={class:"hidden md:flex items-center space-x-8"},Tr={class:"flex items-center space-x-4 ml-8 pl-8 border-l border-gray-200"},Dr={class:"md:hidden flex items-center"},Lr={key:0,class:"md:hidden"},qr={class:"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t"},Rr={class:"bg-gray-800 text-white"},Br={class:"max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8"},Hr={class:"grid grid-cols-1 md:grid-cols-4 gap-8"},Or={class:"col-span-1 md:col-span-2"},Nr={class:"flex items-center space-x-3 mb-4"},Ur={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center"},Fr={class:"text-white font-bold text-sm"},Kr={class:"text-xl font-semibold"},Wr={class:"text-gray-300 max-w-md"},Gr={class:"space-y-2"},Qr={class:"space-y-2 text-gray-300"},Jr={key:0},Yr={key:1},Xr={key:2},Zr={class:"mt-8 pt-8 border-t border-gray-700 text-center text-gray-400"},ze={__name:"PublicLayout",setup(o){const r=te(),l=j(!1),t=x(()=>r.config||{}),n=x(()=>{var g;return((g=t.value.company)==null?void 0:g.name)||"DatVinci"}),m=x(()=>n.value.split(" ").map(d=>d[0]).join("").toUpperCase().slice(0,2)),u=x(()=>r.config!==null),h=new Date().getFullYear();return Q(()=>{u.value||r.loadConfig()}),(g,d)=>{var k,w,$,z,P,b;const i=H("router-link"),f=H("router-view");return s(),a("div",Mr,[e("nav",jr,[e("div",zr,[e("div",Pr,[e("div",Sr,[C(i,{to:"/",class:"flex items-center space-x-3"},{default:A(()=>[e("div",Ar,[e("span",Er,c(m.value),1)]),e("span",Ir,c(n.value),1)]),_:1})]),e("div",Vr,[C(i,{to:"/",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:A(()=>d[1]||(d[1]=[I(" Home ")])),_:1,__:[1]}),C(i,{to:"/about",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:A(()=>d[2]||(d[2]=[I(" Chi Siamo ")])),_:1,__:[2]}),C(i,{to:"/services",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:A(()=>d[3]||(d[3]=[I(" Servizi ")])),_:1,__:[3]}),C(i,{to:"/contact",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:A(()=>d[4]||(d[4]=[I(" Contatti ")])),_:1,__:[4]}),e("div",Tr,[C(i,{to:"/auth/login",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:A(()=>d[5]||(d[5]=[I(" Accedi ")])),_:1,__:[5]}),C(i,{to:"/auth/register",class:"bg-primary-600 text-white hover:bg-primary-700 px-4 py-2 rounded-md text-sm font-medium"},{default:A(()=>d[6]||(d[6]=[I(" Registrati ")])),_:1,__:[6]})])]),e("div",Dr,[e("button",{onClick:d[0]||(d[0]=q=>l.value=!l.value),class:"text-gray-400 hover:text-gray-500"},d[7]||(d[7]=[e("svg",{class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"})],-1)]))])])]),l.value?(s(),a("div",Lr,[e("div",qr,[C(i,{to:"/",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:A(()=>d[8]||(d[8]=[I(" Home ")])),_:1,__:[8]}),C(i,{to:"/about",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:A(()=>d[9]||(d[9]=[I(" Chi Siamo ")])),_:1,__:[9]}),C(i,{to:"/services",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:A(()=>d[10]||(d[10]=[I(" Servizi ")])),_:1,__:[10]}),C(i,{to:"/contact",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:A(()=>d[11]||(d[11]=[I(" Contatti ")])),_:1,__:[11]}),d[14]||(d[14]=e("hr",{class:"my-2"},null,-1)),C(i,{to:"/auth/login",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:A(()=>d[12]||(d[12]=[I(" Accedi ")])),_:1,__:[12]}),C(i,{to:"/auth/register",class:"block px-3 py-2 text-base font-medium bg-primary-600 text-white rounded-md"},{default:A(()=>d[13]||(d[13]=[I(" Registrati ")])),_:1,__:[13]})])])):_("",!0)]),e("main",null,[C(f)]),e("footer",Rr,[e("div",Br,[e("div",Hr,[e("div",Or,[e("div",Nr,[e("div",Ur,[e("span",Fr,c(m.value),1)]),e("span",Kr,c(n.value),1)]),e("p",Wr,c(O(r).interpolateText((k=t.value.footer)==null?void 0:k.description)||((w=t.value.company)==null?void 0:w.description)||"Innovazione e tecnologia per il futuro digitale della tua azienda."),1)]),e("div",null,[d[19]||(d[19]=e("h3",{class:"text-sm font-semibold uppercase tracking-wider mb-4"},"Link Rapidi",-1)),e("ul",Gr,[e("li",null,[C(i,{to:"/",class:"text-gray-300 hover:text-white"},{default:A(()=>d[15]||(d[15]=[I("Home")])),_:1,__:[15]})]),e("li",null,[C(i,{to:"/about",class:"text-gray-300 hover:text-white"},{default:A(()=>d[16]||(d[16]=[I("Chi Siamo")])),_:1,__:[16]})]),e("li",null,[C(i,{to:"/services",class:"text-gray-300 hover:text-white"},{default:A(()=>d[17]||(d[17]=[I("Servizi")])),_:1,__:[17]})]),e("li",null,[C(i,{to:"/contact",class:"text-gray-300 hover:text-white"},{default:A(()=>d[18]||(d[18]=[I("Contatti")])),_:1,__:[18]})])])]),e("div",null,[d[20]||(d[20]=e("h3",{class:"text-sm font-semibold uppercase tracking-wider mb-4"},"Contatti",-1)),e("ul",Qr,[($=t.value.contact)!=null&&$.email?(s(),a("li",Jr,c(t.value.contact.email),1)):_("",!0),(z=t.value.contact)!=null&&z.phone?(s(),a("li",Yr,c(t.value.contact.phone),1)):_("",!0),(P=t.value.contact)!=null&&P.address?(s(),a("li",Xr,c(t.value.contact.address),1)):_("",!0)])])]),e("div",Zr,[e("p",null,c(O(r).interpolateText((b=t.value.footer)==null?void 0:b.copyright)||`© ${O(h)} ${n.value}. Tutti i diritti riservati.`),1)])])])])}}},eo={class:"bg-white"},to={class:"relative overflow-hidden"},so={class:"max-w-7xl mx-auto"},ro={class:"relative z-10 pb-8 bg-white sm:pb-16 md:pb-20 lg:max-w-2xl lg:w-full lg:pb-28 xl:pb-32"},oo={class:"mt-10 mx-auto max-w-7xl px-4 sm:mt-12 sm:px-6 md:mt-16 lg:mt-20 lg:px-8 xl:mt-28"},ao={class:"sm:text-center lg:text-left"},no={class:"text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl"},io={class:"block xl:inline"},lo={class:"mt-3 text-base text-gray-500 sm:mt-5 sm:text-lg sm:max-w-xl sm:mx-auto md:mt-5 md:text-xl lg:mx-0"},co={class:"mt-5 sm:mt-8 sm:flex sm:justify-center lg:justify-start"},uo={class:"rounded-md shadow"},mo={class:"mt-3 sm:mt-0 sm:ml-3"},po={class:"py-12 bg-white"},go={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},vo={class:"lg:text-center"},ho={class:"text-base text-primary-600 font-semibold tracking-wide uppercase"},fo={class:"mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl"},xo={key:0,class:"mt-10"},yo={class:"space-y-10 md:space-y-0 md:grid md:grid-cols-2 md:gap-x-8 md:gap-y-10"},_o={class:"absolute flex items-center justify-center h-12 w-12 rounded-md bg-primary-500 text-white"},ko={class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},bo={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"},wo={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"},$o={key:2,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"},Co={key:3,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"},Mo={class:"ml-16 text-lg leading-6 font-medium text-gray-900"},jo={class:"mt-2 ml-16 text-base text-gray-500"},zo={__name:"Home",setup(o){const r=te(),l=x(()=>r.config||{}),t=x(()=>{var m;return((m=l.value.pages)==null?void 0:m.home)||{}}),n=x(()=>l.value.company||{});return Q(()=>{r.config||r.loadConfig()}),(m,u)=>{var g,d,i,f;const h=H("router-link");return s(),a("div",eo,[e("div",to,[e("div",so,[e("div",ro,[e("main",oo,[e("div",ao,[e("h1",no,[e("span",io,c(((g=t.value.hero)==null?void 0:g.title)||"Innovazione per il futuro"),1)]),e("p",lo,c(((d=t.value.hero)==null?void 0:d.subtitle)||O(r).interpolateText(n.value.description)||"Supportiamo le aziende nel loro percorso di crescita attraverso soluzioni tecnologiche all'avanguardia"),1),e("div",co,[e("div",uo,[C(h,{to:"/services",class:"w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 md:py-4 md:text-lg md:px-10"},{default:A(()=>{var k;return[I(c(((k=t.value.hero)==null?void 0:k.cta_primary)||"Scopri i nostri servizi"),1)]}),_:1})]),e("div",mo,[C(h,{to:"/contact",class:"w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 md:py-4 md:text-lg md:px-10"},{default:A(()=>{var k;return[I(c(((k=t.value.hero)==null?void 0:k.cta_secondary)||"Contattaci"),1)]}),_:1})])])])])])]),u[0]||(u[0]=e("div",{class:"lg:absolute lg:inset-y-0 lg:right-0 lg:w-1/2"},[e("div",{class:"h-56 w-full bg-gradient-to-r from-primary-400 to-primary-600 sm:h-72 md:h-96 lg:w-full lg:h-full flex items-center justify-center"},[e("svg",{class:"h-24 w-24 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})])])],-1))]),e("div",po,[e("div",go,[e("div",vo,[e("h2",ho,c(((i=t.value.services_section)==null?void 0:i.title)||"I nostri servizi"),1),e("p",fo,c(((f=t.value.services_section)==null?void 0:f.subtitle)||"Soluzioni innovative per ogni esigenza aziendale"),1)]),n.value.platform_features?(s(),a("div",xo,[e("div",yo,[(s(!0),a(N,null,U(n.value.platform_features,k=>(s(),a("div",{key:k.title,class:"relative"},[e("div",_o,[(s(),a("svg",ko,[k.icon==="briefcase"?(s(),a("path",bo)):k.icon==="users"?(s(),a("path",wo)):k.icon==="chart"?(s(),a("path",$o)):(s(),a("path",Co))]))]),e("p",Mo,c(k.title),1),e("p",jo,c(k.description),1)]))),128))])])):_("",!0)])])])}}},Po={class:"py-16 bg-white"},So={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},Ao={class:"text-center"},Eo={class:"text-4xl font-extrabold text-gray-900 sm:text-5xl"},Io={class:"mt-4 text-xl text-gray-600"},Vo={key:0,class:"mt-16"},To={class:"max-w-3xl mx-auto"},Do={class:"text-3xl font-bold text-gray-900 text-center mb-8"},Lo={class:"text-lg text-gray-700 leading-relaxed"},qo={class:"mt-16 grid grid-cols-1 md:grid-cols-2 gap-12"},Ro={key:0,class:"bg-gray-50 p-8 rounded-lg"},Bo={class:"text-2xl font-bold text-gray-900 mb-4"},Ho={class:"text-gray-700"},Oo={key:1,class:"bg-gray-50 p-8 rounded-lg"},No={class:"text-2xl font-bold text-gray-900 mb-4"},Uo={class:"text-gray-700"},Fo={key:1,class:"mt-16"},Ko={class:"text-center mb-12"},Wo={class:"text-3xl font-bold text-gray-900"},Go={class:"mt-4 text-xl text-gray-600"},Qo={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},Jo={class:"text-lg font-semibold text-gray-900"},Yo={key:2,class:"mt-16"},Xo={class:"text-center"},Zo={class:"text-3xl font-bold text-gray-900"},ea={class:"mt-4 text-xl text-gray-600"},ta={class:"mt-8 inline-flex items-center px-6 py-3 bg-primary-50 rounded-lg"},sa={class:"text-primary-900 font-medium"},ra={__name:"About",setup(o){const r=te(),l=x(()=>r.config||{}),t=x(()=>{var m;return((m=l.value.pages)==null?void 0:m.about)||{}}),n=x(()=>l.value.company||{});return Q(()=>{r.config||r.loadConfig()}),(m,u)=>{var h,g;return s(),a("div",Po,[e("div",So,[e("div",Ao,[e("h1",Eo,c(((h=t.value.hero)==null?void 0:h.title)||"Chi Siamo"),1),e("p",Io,c(((g=t.value.hero)==null?void 0:g.subtitle)||"La nostra storia e i nostri valori"),1)]),t.value.story_section?(s(),a("div",Vo,[e("div",To,[e("h2",Do,c(t.value.story_section.title),1),e("p",Lo,c(O(r).interpolateText(t.value.story_section.content)),1)])])):_("",!0),e("div",qo,[t.value.mission_section?(s(),a("div",Ro,[e("h3",Bo,c(t.value.mission_section.title),1),e("p",Ho,c(O(r).interpolateText(t.value.mission_section.content)),1)])):_("",!0),t.value.vision_section?(s(),a("div",Oo,[e("h3",No,c(t.value.vision_section.title),1),e("p",Uo,c(O(r).interpolateText(t.value.vision_section.content)),1)])):_("",!0)]),t.value.expertise_section&&n.value.expertise?(s(),a("div",Fo,[e("div",Ko,[e("h2",Wo,c(t.value.expertise_section.title),1),e("p",Go,c(t.value.expertise_section.subtitle),1)]),e("div",Qo,[(s(!0),a(N,null,U(n.value.expertise,d=>(s(),a("div",{key:d,class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200 text-center"},[u[0]||(u[0]=e("div",{class:"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4"},[e("svg",{class:"w-6 h-6 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1)),e("h3",Jo,c(d),1)]))),128))])])):_("",!0),t.value.team_section?(s(),a("div",Yo,[e("div",Xo,[e("h2",Zo,c(t.value.team_section.title),1),e("p",ea,c(t.value.team_section.subtitle),1),e("div",ta,[u[1]||(u[1]=e("svg",{class:"w-5 h-5 text-primary-600 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})],-1)),e("span",sa,c(n.value.team_size),1)])])])):_("",!0)])])}}},oa={class:"py-16 bg-white"},aa={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},na={class:"text-center"},ia={class:"text-4xl font-extrabold text-gray-900 sm:text-5xl"},la={class:"mt-4 text-xl text-gray-600"},da={key:0,class:"mt-8 text-center"},ca={class:"text-lg text-gray-700 max-w-3xl mx-auto"},ua={class:"mt-16 grid grid-cols-1 lg:grid-cols-2 gap-16"},ma={key:0},pa={class:"text-2xl font-bold text-gray-900 mb-8"},ga={class:"block text-sm font-medium text-gray-700 mb-2"},va={class:"block text-sm font-medium text-gray-700 mb-2"},ha={class:"block text-sm font-medium text-gray-700 mb-2"},fa=["disabled"],xa={key:1},ya={class:"text-2xl font-bold text-gray-900 mb-8"},_a={class:"space-y-6"},ka={key:0,class:"flex items-start"},ba={class:"font-medium text-gray-900"},wa={class:"text-gray-600"},$a={key:1,class:"flex items-start"},Ca={class:"font-medium text-gray-900"},Ma={class:"text-gray-600"},ja={key:2,class:"flex items-start"},za={class:"font-medium text-gray-900"},Pa={class:"text-gray-600"},Sa={key:3,class:"flex items-start"},Aa={class:"font-medium text-gray-900"},Ea={class:"text-gray-600"},Ia={__name:"Contact",setup(o){const r=te(),l=x(()=>r.config||{}),t=x(()=>{var d;return((d=l.value.pages)==null?void 0:d.contact)||{}}),n=x(()=>l.value.contact||{}),m=j({name:"",email:"",message:""}),u=j(!1),h=j({text:"",type:""}),g=async()=>{var d,i;if(!m.value.name||!m.value.email||!m.value.message){h.value={text:((d=t.value.form)==null?void 0:d.error_message)||"Tutti i campi sono obbligatori",type:"error"};return}u.value=!0,h.value={text:"",type:""};try{await new Promise(f=>setTimeout(f,1e3)),h.value={text:((i=t.value.form)==null?void 0:i.success_message)||"Messaggio inviato con successo!",type:"success"},m.value={name:"",email:"",message:""}}catch{h.value={text:"Errore durante l'invio. Riprova più tardi.",type:"error"}}finally{u.value=!1}};return Q(()=>{r.config||r.loadConfig()}),(d,i)=>{var f,k;return s(),a("div",oa,[e("div",aa,[e("div",na,[e("h1",ia,c(((f=t.value.hero)==null?void 0:f.title)||"Contattaci"),1),e("p",la,c(((k=t.value.hero)==null?void 0:k.subtitle)||"Siamo qui per aiutarti"),1)]),t.value.intro?(s(),a("div",da,[e("p",ca,c(t.value.intro.content),1)])):_("",!0),e("div",ua,[t.value.form?(s(),a("div",ma,[e("h2",pa,c(t.value.form.title),1),e("form",{onSubmit:he(g,["prevent"]),class:"space-y-6"},[e("div",null,[e("label",ga,c(t.value.form.name_label),1),K(e("input",{"onUpdate:modelValue":i[0]||(i[0]=w=>m.value.name=w),type:"text",required:"",class:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"},null,512),[[Z,m.value.name]])]),e("div",null,[e("label",va,c(t.value.form.email_label),1),K(e("input",{"onUpdate:modelValue":i[1]||(i[1]=w=>m.value.email=w),type:"email",required:"",class:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"},null,512),[[Z,m.value.email]])]),e("div",null,[e("label",ha,c(t.value.form.message_label),1),K(e("textarea",{"onUpdate:modelValue":i[2]||(i[2]=w=>m.value.message=w),rows:"6",required:"",class:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"},null,512),[[Z,m.value.message]])]),e("button",{type:"submit",disabled:u.value,class:"w-full bg-primary-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-primary-700 disabled:opacity-50"},c(u.value?"Invio in corso...":t.value.form.submit_button),9,fa),h.value.text?(s(),a("div",{key:0,class:S([h.value.type==="success"?"text-green-600":"text-red-600","text-sm mt-2"])},c(h.value.text),3)):_("",!0)],32)])):_("",!0),t.value.info?(s(),a("div",xa,[e("h2",ya,c(t.value.info.title),1),e("div",_a,[n.value.address?(s(),a("div",ka,[i[3]||(i[3]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})],-1)),e("div",null,[e("h3",ba,c(t.value.info.address_label),1),e("p",wa,c(n.value.address),1)])])):_("",!0),n.value.phone?(s(),a("div",$a,[i[4]||(i[4]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})],-1)),e("div",null,[e("h3",Ca,c(t.value.info.phone_label),1),e("p",Ma,c(n.value.phone),1)])])):_("",!0),n.value.email?(s(),a("div",ja,[i[5]||(i[5]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})],-1)),e("div",null,[e("h3",za,c(t.value.info.email_label),1),e("p",Pa,c(n.value.email),1)])])):_("",!0),n.value.hours?(s(),a("div",Sa,[i[6]||(i[6]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),e("div",null,[e("h3",Aa,c(t.value.info.hours_label),1),e("p",Ea,c(n.value.hours),1)])])):_("",!0)])])):_("",!0)])])])}}},Va={class:"py-16 bg-white"},Ta={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},Da={class:"text-center"},La={class:"text-4xl font-extrabold text-gray-900 sm:text-5xl"},qa={class:"mt-4 text-xl text-gray-600"},Ra={key:0,class:"mt-8 text-center"},Ba={class:"text-lg text-gray-700 max-w-3xl mx-auto"},Ha={key:1,class:"mt-16"},Oa={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},Na={class:"text-xl font-bold text-gray-900 text-center mb-4"},Ua={class:"text-gray-600 text-center"},Fa={key:2,class:"mt-20"},Ka={class:"bg-primary-50 rounded-2xl p-12 text-center"},Wa={class:"text-3xl font-bold text-gray-900 mb-4"},Ga={class:"text-xl text-gray-600 mb-8"},Qa={__name:"Services",setup(o){const r=te(),l=x(()=>r.config||{}),t=x(()=>{var u;return((u=l.value.pages)==null?void 0:u.services)||{}}),n=x(()=>l.value.company||{}),m=u=>({"Sviluppo Software":"Soluzioni software personalizzate per ottimizzare i processi aziendali e migliorare l'efficienza operativa.","Intelligenza Artificiale":"Implementazione di sistemi AI avanzati per automatizzare processi e analizzare dati complessi.","Consulenza IT":"Consulenza strategica per la trasformazione digitale e l'ottimizzazione dell'infrastruttura tecnologica.","Gestione Progetti Innovativi":"Coordinamento e gestione di progetti tecnologici complessi con metodologie agili.","Supporto su Bandi e Finanziamenti":"Assistenza nella ricerca e gestione di bandi pubblici e finanziamenti per l'innovazione."})[u]||"Servizio professionale di alta qualità per supportare la crescita della tua azienda.";return Q(()=>{r.config||r.loadConfig()}),(u,h)=>{var d,i;const g=H("router-link");return s(),a("div",Va,[e("div",Ta,[e("div",Da,[e("h1",La,c(((d=t.value.hero)==null?void 0:d.title)||"I nostri servizi"),1),e("p",qa,c(((i=t.value.hero)==null?void 0:i.subtitle)||"Soluzioni complete per la tua azienda"),1)]),t.value.intro?(s(),a("div",Ra,[e("p",Ba,c(t.value.intro.content),1)])):_("",!0),n.value.expertise?(s(),a("div",Ha,[e("div",Oa,[(s(!0),a(N,null,U(n.value.expertise,f=>(s(),a("div",{key:f,class:"bg-white p-8 rounded-lg shadow-lg border border-gray-200 hover:shadow-xl transition-shadow"},[h[0]||(h[0]=e("div",{class:"w-16 h-16 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-6"},[e("svg",{class:"w-8 h-8 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1)),e("h3",Na,c(f),1),e("p",Ua,c(m(f)),1)]))),128))])])):_("",!0),t.value.cta?(s(),a("div",Fa,[e("div",Ka,[e("h2",Wa,c(t.value.cta.title),1),e("p",Ga,c(t.value.cta.subtitle),1),C(g,{to:"/contact",class:"inline-flex items-center px-8 py-4 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 transition-colors"},{default:A(()=>[I(c(t.value.cta.button)+" ",1),h[1]||(h[1]=e("svg",{class:"ml-2 w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 8l4 4m0 0l-4 4m4-4H3"})],-1))]),_:1,__:[1]})])])):_("",!0)])])}}},Ja={class:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"},Ya={class:"max-w-md w-full space-y-8"},Xa={class:"mt-2 text-center text-sm text-gray-600"},Za={key:0,class:"rounded-md bg-red-50 p-4"},en={class:"text-sm text-red-700"},tn={class:"rounded-md shadow-sm -space-y-px"},sn={class:"flex items-center justify-between"},rn={class:"flex items-center"},on=["disabled"],an={key:0,class:"absolute left-0 inset-y-0 flex items-center pl-3"},nn={__name:"Login",setup(o){const r=ee(),l=X(),t=j({username:"",password:"",remember:!1}),n=x(()=>l.loading),m=x(()=>l.error);async function u(){(await l.login({username:t.value.username,password:t.value.password,remember:t.value.remember})).success&&r.push("/app/dashboard")}return(h,g)=>{const d=H("router-link");return s(),a("div",Ja,[e("div",Ya,[e("div",null,[g[5]||(g[5]=e("div",{class:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-100"},[e("svg",{class:"h-6 w-6 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})])],-1)),g[6]||(g[6]=e("h2",{class:"mt-6 text-center text-3xl font-extrabold text-gray-900"}," Accedi al tuo account ",-1)),e("p",Xa,[g[4]||(g[4]=I(" Oppure ")),C(d,{to:"/auth/register",class:"font-medium text-primary-600 hover:text-primary-500"},{default:A(()=>g[3]||(g[3]=[I(" registrati per un nuovo account ")])),_:1,__:[3]})])]),e("form",{onSubmit:he(u,["prevent"]),class:"mt-8 space-y-6"},[m.value?(s(),a("div",Za,[e("div",en,c(m.value),1)])):_("",!0),e("div",tn,[e("div",null,[g[7]||(g[7]=e("label",{for:"username",class:"sr-only"},"Username",-1)),K(e("input",{id:"username","onUpdate:modelValue":g[0]||(g[0]=i=>t.value.username=i),name:"username",type:"text",autocomplete:"username",required:"",class:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm",placeholder:"Username"},null,512),[[Z,t.value.username]])]),e("div",null,[g[8]||(g[8]=e("label",{for:"password",class:"sr-only"},"Password",-1)),K(e("input",{id:"password","onUpdate:modelValue":g[1]||(g[1]=i=>t.value.password=i),name:"password",type:"password",autocomplete:"current-password",required:"",class:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm",placeholder:"Password"},null,512),[[Z,t.value.password]])])]),e("div",sn,[e("div",rn,[K(e("input",{id:"remember-me","onUpdate:modelValue":g[2]||(g[2]=i=>t.value.remember=i),name:"remember-me",type:"checkbox",class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,512),[[De,t.value.remember]]),g[9]||(g[9]=e("label",{for:"remember-me",class:"ml-2 block text-sm text-gray-900"}," Ricordami ",-1))]),g[10]||(g[10]=e("div",{class:"text-sm"},[e("a",{href:"#",class:"font-medium text-primary-600 hover:text-primary-500"}," Password dimenticata? ")],-1))]),e("div",null,[e("button",{type:"submit",disabled:n.value,class:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"},[n.value?(s(),a("span",an,g[11]||(g[11]=[e("svg",{class:"h-5 w-5 text-primary-500 animate-spin",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1)]))):_("",!0),I(" "+c(n.value?"Accesso in corso...":"Accedi"),1)],8,on)])],32)])])}}},ln={},dn={class:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"};function cn(o,r){return s(),a("div",dn,r[0]||(r[0]=[e("div",{class:"max-w-md w-full space-y-8"},[e("div",null,[e("h2",{class:"mt-6 text-center text-3xl font-extrabold text-gray-900"}," Registra un nuovo account ")]),e("div",{class:"text-center text-gray-600"}," Registrazione in arrivo... ")],-1)]))}const un=Ae(ln,[["render",cn]]),mn={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},pn={class:"p-5"},gn={class:"flex items-center"},vn=["innerHTML"],hn={class:"ml-5 w-0 flex-1"},fn={class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"},xn={class:"text-lg font-medium text-gray-900 dark:text-white"},yn={key:0,class:"text-xs text-gray-500 dark:text-gray-400"},_n={key:0,class:"bg-gray-50 dark:bg-gray-700 px-5 py-3"},kn={class:"text-sm"},ie={__name:"StatsCard",props:{title:{type:String,required:!0},value:{type:[Number,String],required:!0},subtitle:{type:String,default:null},icon:{type:String,required:!0},color:{type:String,default:"primary"},link:{type:String,default:null}},setup(o){const r=t=>t==="project"?`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
      </svg>`:t==="users"?`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
      </svg>`:t==="clock"?`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`:t==="team"?`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
      </svg>`:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>`,l=t=>{const n={primary:"bg-primary-500",secondary:"bg-secondary-500",red:"bg-red-500",yellow:"bg-yellow-500",blue:"bg-blue-500",green:"bg-green-500"};return n[t]||n.primary};return(t,n)=>{const m=H("router-link");return s(),a("div",mn,[e("div",pn,[e("div",gn,[e("div",{class:S(["flex-shrink-0 rounded-md p-3",l(o.color)])},[e("div",{class:"h-6 w-6 text-white",innerHTML:r(o.icon)},null,8,vn)],2),e("div",hn,[e("dl",null,[e("dt",fn,c(o.title),1),e("dd",null,[e("div",xn,c(o.value),1),o.subtitle?(s(),a("div",yn,c(o.subtitle),1)):_("",!0)])])])])]),o.link?(s(),a("div",_n,[e("div",kn,[C(m,{to:o.link,class:"font-medium text-primary-600 dark:text-primary-400 hover:text-primary-500 dark:hover:text-primary-300"},{default:A(()=>n[0]||(n[0]=[I(" Vedi tutti ")])),_:1,__:[0]},8,["to"])])])):_("",!0)])}}},bn={class:"py-6"},wn={class:"flex flex-col md:flex-row md:items-center md:justify-between mb-8"},$n={class:"mt-4 md:mt-0 flex space-x-3"},Cn={class:"relative"},Mn=["disabled"],jn={class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"},zn={class:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8"},Pn={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Sn={class:"relative h-64"},An={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},En={class:"relative h-64"},In={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},Vn={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Tn={class:"p-6"},Dn={key:0,class:"text-center py-8 text-gray-500"},Ln={key:1,class:"space-y-4"},qn={class:"flex justify-between items-start"},Rn={class:"flex-1"},Bn={class:"text-sm font-medium text-gray-900 dark:text-white"},Hn={class:"text-xs text-gray-500 dark:text-gray-400"},On={class:"mt-2 flex justify-between items-center"},Nn={class:"text-xs text-gray-500 dark:text-gray-400"},Un={class:"bg-gray-50 dark:bg-gray-700 px-6 py-3"},Fn={class:"text-sm"},Kn={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Wn={class:"p-6"},Gn={key:0,class:"text-center py-8 text-gray-500"},Qn={key:1,class:"space-y-4"},Jn={class:"flex-shrink-0"},Yn=["innerHTML"],Xn={class:"flex-1 min-w-0"},Zn={class:"text-sm font-medium text-gray-900 dark:text-white"},ei={class:"text-xs text-gray-500 dark:text-gray-400"},ti={class:"text-xs text-gray-400 dark:text-gray-500"},si={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},ri={class:"p-6"},oi={key:0,class:"text-center py-8 text-gray-500"},ai={key:1,class:"space-y-4"},ni={class:"flex justify-between items-start"},ii={class:"flex-1"},li={class:"text-sm font-medium text-gray-900 dark:text-white"},di={class:"text-xs text-gray-500 dark:text-gray-400"},ci={class:"text-right"},ui={class:"text-sm font-bold text-gray-900 dark:text-white"},mi={class:"text-xs text-gray-500"},pi={class:"mt-2"},gi={class:"w-full bg-gray-200 rounded-full h-2"},vi={class:"text-xs text-gray-500 mt-1"},hi={__name:"Dashboard",setup(o){ue.register(...Le),ee(),X();const r=j(!1),l=j("7"),t=j({}),n=j([]),m=j([]),u=j([]),h=j(null),g=j(null);let d=null,i=null;const f=async()=>{try{const p=await B.get("/api/dashboard/stats");t.value=p.data.data}catch(p){console.error("Error fetching dashboard stats:",p),t.value={}}},k=async()=>{try{const p=await B.get(`/api/dashboard/upcoming-tasks?days=${l.value}&limit=5`);n.value=p.data.data.tasks}catch(p){console.error("Error fetching upcoming tasks:",p),n.value=[]}},w=async()=>{try{const p=await B.get("/api/dashboard/recent-activities?limit=5");m.value=p.data.data.activities}catch(p){console.error("Error fetching recent activities:",p),m.value=[]}},$=async()=>{try{const p=await B.get("/api/dashboard/kpis?limit=3");u.value=p.data.data.kpis}catch(p){console.error("Error fetching KPIs:",p),u.value=[]}},z=async()=>{try{const p=await B.get("/api/dashboard/charts/project-status");b(p.data.data.chart)}catch(p){console.error("Error fetching project chart:",p)}},P=async()=>{try{const p=await B.get("/api/dashboard/charts/task-status");q(p.data.data.chart)}catch(p){console.error("Error fetching task chart:",p)}},b=p=>{if(!h.value)return;const v=h.value.getContext("2d");d&&d.destroy(),d=new ue(v,{type:"doughnut",data:{labels:p.labels,datasets:[{data:p.data,backgroundColor:["#3B82F6","#10B981","#F59E0B","#EF4444","#8B5CF6"],borderWidth:2,borderColor:"#ffffff"}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom",labels:{padding:20,usePointStyle:!0}}}}})},q=p=>{if(!g.value)return;const v=g.value.getContext("2d");i&&i.destroy(),i=new ue(v,{type:"bar",data:{labels:p.labels,datasets:[{label:"Tasks",data:p.data,backgroundColor:["#60A5FA","#34D399","#FBBF24","#F87171"],borderColor:["#3B82F6","#10B981","#F59E0B","#EF4444"],borderWidth:1}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1}},scales:{y:{beginAtZero:!0,ticks:{stepSize:1}}}}})},G=async()=>{r.value=!0;try{await Promise.all([f(),k(),w(),$(),z(),P()])}finally{r.value=!1}},se=p=>new Date(p).toLocaleDateString("it-IT"),re=p=>{const v=new Date(p),F=Math.floor((new Date-v)/(1e3*60));return F<60?`${F} minuti fa`:F<1440?`${Math.floor(F/60)} ore fa`:`${Math.floor(F/1440)} giorni fa`},M=p=>{const v={high:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",medium:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",low:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"};return v[p]||v.medium},E=p=>{const v={todo:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300","in-progress":"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",review:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",done:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"};return v[p]||v.todo},L=p=>{const v={task:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
      </svg>`,timesheet:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`,event:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>`};return v[p]||v.task},T=p=>{const v={task:"bg-blue-100 text-blue-600",timesheet:"bg-green-100 text-green-600",event:"bg-purple-100 text-purple-600"};return v[p]||v.task},y=p=>p>=90?"bg-green-500":p>=70?"bg-yellow-500":"bg-red-500";return Q(async()=>{await G(),await Pe(),h.value&&g.value&&(await z(),await P())}),(p,v)=>{var F,oe,ae,ye,_e,ke,be,we;const J=H("router-link");return s(),a("div",bn,[e("div",wn,[v[4]||(v[4]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Dashboard"),e("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Benvenuto! Ecco una panoramica delle attività della tua azienda. ")],-1)),e("div",$n,[e("div",Cn,[K(e("select",{"onUpdate:modelValue":v[0]||(v[0]=V=>l.value=V),onChange:G,class:"bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md py-2 pl-3 pr-10 text-sm font-medium text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500"},v[1]||(v[1]=[e("option",{value:"7"},"Ultimi 7 giorni",-1),e("option",{value:"30"},"Ultimo mese",-1),e("option",{value:"90"},"Ultimi 3 mesi",-1)]),544),[[pe,l.value]])]),e("button",{onClick:G,disabled:r.value,type:"button",class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},[(s(),a("svg",{xmlns:"http://www.w3.org/2000/svg",class:S(["h-4 w-4 mr-2",{"animate-spin":r.value}]),viewBox:"0 0 20 20",fill:"currentColor"},v[2]||(v[2]=[e("path",{"fill-rule":"evenodd",d:"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z","clip-rule":"evenodd"},null,-1)]),2)),v[3]||(v[3]=I(" Aggiorna "))],8,Mn)])]),e("div",jn,[C(ie,{title:"Progetti Attivi",value:((F=t.value.projects)==null?void 0:F.active)||0,subtitle:`di ${((oe=t.value.projects)==null?void 0:oe.total)||0} totali`,icon:"project",color:"primary",link:"/app/projects?status=active"},null,8,["value","subtitle"]),C(ie,{title:"Clienti",value:((ae=t.value.team)==null?void 0:ae.clients)||0,icon:"users",color:"secondary",link:"/app/crm/clients"},null,8,["value"]),C(ie,{title:"Task Pendenti",value:((ye=t.value.tasks)==null?void 0:ye.pending)||0,subtitle:`${((_e=t.value.tasks)==null?void 0:_e.overdue)||0} in ritardo`,icon:"clock",color:((ke=t.value.tasks)==null?void 0:ke.overdue)>0?"red":"yellow",link:"/app/tasks?status=pending"},null,8,["value","subtitle","color"]),C(ie,{title:"Team Members",value:((be=t.value.team)==null?void 0:be.users)||0,subtitle:`${((we=t.value.team)==null?void 0:we.departments)||0} dipartimenti`,icon:"team",color:"blue",link:"/app/personnel"},null,8,["value","subtitle"])]),e("div",zn,[e("div",Pn,[v[5]||(v[5]=e("div",{class:"flex items-center justify-between mb-4"},[e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Stato Progetti")],-1)),e("div",Sn,[e("canvas",{ref_key:"projectChart",ref:h},null,512)])]),e("div",An,[v[6]||(v[6]=e("div",{class:"flex items-center justify-between mb-4"},[e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Stato Attività")],-1)),e("div",En,[e("canvas",{ref_key:"taskChart",ref:g},null,512)])])]),e("div",In,[e("div",Vn,[e("div",Tn,[v[7]||(v[7]=e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Attività in Scadenza",-1)),n.value.length===0?(s(),a("div",Dn," Nessuna attività in scadenza ")):(s(),a("div",Ln,[(s(!0),a(N,null,U(n.value,V=>(s(),a("div",{key:V.id,class:"border-b border-gray-200 dark:border-gray-700 pb-4 last:border-b-0 last:pb-0"},[e("div",qn,[e("div",Rn,[e("h3",Bn,c(V.name),1),e("p",Hn,c(V.project_name),1)]),e("span",{class:S(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",M(V.priority)])},c(V.priority),3)]),e("div",On,[e("span",Nn," Scadenza: "+c(se(V.due_date)),1),e("span",{class:S(["inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",E(V.status)])},c(V.status),3)])]))),128))]))]),e("div",Un,[e("div",Fn,[C(J,{to:"/app/tasks",class:"font-medium text-primary-600 dark:text-primary-400 hover:text-primary-500"},{default:A(()=>v[8]||(v[8]=[I(" Vedi tutte le attività ")])),_:1,__:[8]})])])]),e("div",Kn,[e("div",Wn,[v[9]||(v[9]=e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Attività Recenti",-1)),m.value.length===0?(s(),a("div",Gn," Nessuna attività recente ")):(s(),a("div",Qn,[(s(!0),a(N,null,U(m.value,V=>(s(),a("div",{key:`${V.type}-${V.id}`,class:"flex items-start space-x-3"},[e("div",Jn,[e("div",{class:S(["w-8 h-8 rounded-full flex items-center justify-center",T(V.type)])},[e("div",{class:"w-4 h-4",innerHTML:L(V.type)},null,8,Yn)],2)]),e("div",Xn,[e("p",Zn,c(V.title),1),e("p",ei,c(V.description),1),e("p",ti,c(re(V.timestamp)),1)])]))),128))]))])]),e("div",si,[e("div",ri,[v[10]||(v[10]=e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"KPIs Principali",-1)),u.value.length===0?(s(),a("div",oi," Nessun KPI configurato ")):(s(),a("div",ai,[(s(!0),a(N,null,U(u.value,V=>(s(),a("div",{key:V.id,class:"border-b border-gray-200 dark:border-gray-700 pb-4 last:border-b-0 last:pb-0"},[e("div",ni,[e("div",ii,[e("h3",li,c(V.name),1),e("p",di,c(V.description),1)]),e("div",ci,[e("p",ui,c(V.current_value)+c(V.unit),1),e("p",mi," Target: "+c(V.target_value)+c(V.unit),1)])]),e("div",pi,[e("div",gi,[e("div",{class:S(["h-2 rounded-full",y(V.performance_percentage)]),style:fe({width:Math.min(V.performance_percentage,100)+"%"})},null,6)]),e("p",vi,c(Math.round(V.performance_percentage))+"% del target",1)])]))),128))]))])])])])}}},fi=ge("projects",()=>{const o=j([]),r=j(null),l=j(!1),t=j(null),n=j(new Map),m=j({page:1,perPage:20,total:0,totalPages:0}),u=j({search:"",status:"",client:"",type:""}),h=x(()=>{let M=o.value;if(u.value.search){const E=u.value.search.toLowerCase();M=M.filter(L=>{var T,y,p;return L.name.toLowerCase().includes(E)||((T=L.description)==null?void 0:T.toLowerCase().includes(E))||((p=(y=L.client)==null?void 0:y.name)==null?void 0:p.toLowerCase().includes(E))})}return u.value.status&&(M=M.filter(E=>E.status===u.value.status)),u.value.client&&(M=M.filter(E=>E.client_id===u.value.client)),u.value.type&&(M=M.filter(E=>E.project_type===u.value.type)),M}),g=x(()=>{const M={};return o.value.forEach(E=>{M[E.status]||(M[E.status]=[]),M[E.status].push(E)}),M}),d=async(M={})=>{var E,L;l.value=!0,t.value=null;try{const T=new URLSearchParams({page:M.page||m.value.page,per_page:M.perPage||m.value.perPage,search:M.search||u.value.search,status:M.status||u.value.status,client:M.client||u.value.client,type:M.type||u.value.type}),y=await B.get(`/api/projects?${T}`);y.data.success&&(o.value=y.data.data.projects,m.value=y.data.data.pagination)}catch(T){t.value=((L=(E=T.response)==null?void 0:E.data)==null?void 0:L.message)||"Errore nel caricamento progetti",console.error("Error fetching projects:",T)}finally{l.value=!1}},i=async(M,E=!1)=>{var L,T;if(!E&&n.value.has(M)){const y=n.value.get(M);return r.value=y,y}l.value=!0,t.value=null;try{const y=await B.get(`/api/projects/${M}`);if(y.data.success){const p=y.data.data.project;return r.value=p,n.value.set(M,p),p}}catch(y){throw t.value=((T=(L=y.response)==null?void 0:L.data)==null?void 0:T.message)||"Errore nel caricamento progetto",console.error("Error fetching project:",y),y}finally{l.value=!1}};return{projects:o,currentProject:r,loading:l,error:t,pagination:m,filters:u,filteredProjects:h,projectsByStatus:g,fetchProjects:d,fetchProject:i,createProject:async M=>{var E,L;l.value=!0,t.value=null;try{const T=await B.post("/api/projects",M);if(T.data.success){const y=T.data.data.project;return o.value.unshift(y),y}}catch(T){throw t.value=((L=(E=T.response)==null?void 0:E.data)==null?void 0:L.message)||"Errore nella creazione progetto",console.error("Error creating project:",T),T}finally{l.value=!1}},updateProject:async(M,E)=>{var L,T,y;l.value=!0,t.value=null;try{const p=await B.put(`/api/projects/${M}`,E);if(p.data.success){const v=p.data.data.project,J=o.value.findIndex(F=>F.id===M);return J!==-1&&(o.value[J]=v),((L=r.value)==null?void 0:L.id)===M&&(r.value=v),n.value.set(M,v),v}}catch(p){throw t.value=((y=(T=p.response)==null?void 0:T.data)==null?void 0:y.message)||"Errore nell'aggiornamento progetto",console.error("Error updating project:",p),p}finally{l.value=!1}},deleteProject:async M=>{var E,L,T;l.value=!0,t.value=null;try{(await B.delete(`/api/projects/${M}`)).data.success&&(o.value=o.value.filter(p=>p.id!==M),((E=r.value)==null?void 0:E.id)===M&&(r.value=null),n.value.delete(M))}catch(y){throw t.value=((T=(L=y.response)==null?void 0:L.data)==null?void 0:T.message)||"Errore nell'eliminazione progetto",console.error("Error deleting project:",y),y}finally{l.value=!1}},setFilters:M=>{u.value={...u.value,...M}},clearFilters:()=>{u.value={search:"",status:"",client:"",type:""}},setCurrentProject:M=>{r.value=M},clearCurrentProject:()=>{r.value=null},clearCache:()=>{n.value.clear()},refreshProject:async M=>await i(M,!0),getCachedProject:M=>n.value.get(M),$reset:()=>{o.value=[],r.value=null,l.value=!1,t.value=null,n.value.clear(),m.value={page:1,perPage:20,total:0,totalPages:0},u.value={search:"",status:"",client:"",type:""}}}}),xi={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6"},yi={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},_i=["value"],ki={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},bi={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},wi={class:"text-lg font-medium text-gray-900 dark:text-white"},$i={key:0,class:"p-6 text-center"},Ci={key:1,class:"p-6 text-center"},Mi={key:2,class:"divide-y divide-gray-200 dark:divide-gray-700"},ji=["onClick"],zi={class:"flex items-center justify-between"},Pi={class:"flex-1"},Si={class:"flex items-center"},Ai={class:"text-lg font-medium text-gray-900 dark:text-white"},Ei={class:"mt-1 text-sm text-gray-600 dark:text-gray-400"},Ii={class:"mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400"},Vi={key:0},Ti={key:1,class:"mx-2"},Di={key:2},Li={key:3,class:"mx-2"},qi={key:4},Ri={key:0,class:"mt-3 flex items-center space-x-4"},Bi={key:0,class:"flex items-center space-x-1"},Hi={class:"text-xs text-gray-600 dark:text-gray-400"},Oi={key:1,class:"flex items-center space-x-1"},Ni={class:"text-xs text-gray-600 dark:text-gray-400"},Ui={key:2,class:"flex items-center space-x-1"},Fi={class:"text-xs text-gray-600 dark:text-gray-400"},Ki={class:"ml-4 flex items-center space-x-4"},Wi={class:"text-right"},Gi={class:"text-sm font-medium text-gray-900 dark:text-white"},Qi={class:"w-20 bg-gray-200 dark:bg-gray-600 rounded-full h-2 mt-1"},Ji={key:0,class:"text-right"},Yi={class:"flex items-center space-x-1 mt-1"},Xi={__name:"Projects",setup(o){const r=ee(),l=fi(),t=j(!0),n=j(""),m=j({status:"",client:""}),u=x(()=>l.projects),h=j([]),g=x(()=>{let y=u.value;if(m.value.status&&(y=y.filter(p=>p.status===m.value.status)),m.value.client&&(y=y.filter(p=>p.client_id==m.value.client)),n.value){const p=n.value.toLowerCase();y=y.filter(v=>v.name.toLowerCase().includes(p)||v.description&&v.description.toLowerCase().includes(p)||v.client&&v.client.name&&v.client.name.toLowerCase().includes(p))}return y}),d=async()=>{t.value=!0;try{await l.fetchProjects(),h.value=[]}catch(y){console.error("Error loading projects:",y)}finally{t.value=!1}},i=()=>{},f=()=>{},k=()=>{m.value={status:"",client:""},n.value=""},w=()=>{r.push("/app/projects/create")},$=y=>{r.push(`/app/projects/${y}`)},z=y=>({planning:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",active:"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",completed:"bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400","on-hold":"bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400"})[y]||"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400",P=y=>({planning:"Pianificazione",active:"Attivo",completed:"Completato","on-hold":"In Pausa"})[y]||y,b=y=>new Date(y).toLocaleDateString("it-IT"),q=y=>new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(y),G=y=>({planning:10,active:50,completed:100,"on-hold":25})[y.status]||0,se=y=>y>=90?"bg-red-500":y>=75?"bg-yellow-500":"bg-green-500",re=y=>y>=90?"bg-red-500":y>=80?"bg-yellow-500":"bg-green-500",M=y=>y<10?"bg-red-500":y<20?"bg-yellow-500":"bg-green-500",E=y=>{const p=y.budget_usage>=90,v=y.time_usage>=90,J=y.margin<10;if(p||v||J)return"bg-red-500";const F=y.budget_usage>=75,oe=y.time_usage>=80,ae=y.margin<20;return F||oe||ae?"bg-yellow-500":"bg-green-500"},L=y=>{const p=E(y);return p.includes("red")?"text-red-600 dark:text-red-400":p.includes("yellow")?"text-yellow-600 dark:text-yellow-400":"text-green-600 dark:text-green-400"},T=y=>{const p=E(y);return p.includes("red")?"Critico":p.includes("yellow")?"Attenzione":"Buono"};return Q(()=>{d()}),(y,p)=>(s(),a("div",null,[e("div",{class:"mb-6"},[e("div",{class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},[p[4]||(p[4]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Progetti"),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Gestisci e monitora tutti i progetti aziendali ")],-1)),e("div",{class:"mt-4 sm:mt-0"},[e("button",{onClick:w,class:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},p[3]||(p[3]=[e("svg",{class:"h-4 w-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4v16m8-8H4"})],-1),I(" Nuovo Progetto ")]))])])]),e("div",xi,[e("div",yi,[e("div",null,[p[6]||(p[6]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Stato",-1)),K(e("select",{"onUpdate:modelValue":p[0]||(p[0]=v=>m.value.status=v),onChange:f,class:"w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"},p[5]||(p[5]=[qe('<option value="">Tutti gli stati</option><option value="planning">Pianificazione</option><option value="active">Attivo</option><option value="completed">Completato</option><option value="on-hold">In Pausa</option>',5)]),544),[[pe,m.value.status]])]),e("div",null,[p[8]||(p[8]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Cliente",-1)),K(e("select",{"onUpdate:modelValue":p[1]||(p[1]=v=>m.value.client=v),onChange:f,class:"w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"},[p[7]||(p[7]=e("option",{value:""},"Tutti i clienti",-1)),(s(!0),a(N,null,U(h.value,v=>(s(),a("option",{key:v.id,value:v.id},c(v.name),9,_i))),128))],544),[[pe,m.value.client]])]),e("div",null,[p[9]||(p[9]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Ricerca",-1)),K(e("input",{"onUpdate:modelValue":p[2]||(p[2]=v=>n.value=v),onInput:i,type:"text",placeholder:"Cerca progetti...",class:"w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"},null,544),[[Z,n.value]])]),e("div",{class:"flex items-end"},[e("button",{onClick:k,class:"w-full px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"}," Reset Filtri ")])])]),e("div",ki,[e("div",bi,[e("h3",wi," Progetti ("+c(g.value.length)+") ",1)]),t.value?(s(),a("div",$i,p[10]||(p[10]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto"},null,-1),e("p",{class:"mt-2 text-gray-600 dark:text-gray-400"},"Caricamento progetti...",-1)]))):g.value.length===0?(s(),a("div",Ci,p[11]||(p[11]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun progetto",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"Inizia creando il tuo primo progetto.",-1)]))):(s(),a("div",Mi,[(s(!0),a(N,null,U(g.value,v=>(s(),a("div",{key:v.id,class:"p-6 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer",onClick:J=>$(v.id)},[e("div",zi,[e("div",Pi,[e("div",Si,[e("h4",Ai,c(v.name),1),e("span",{class:S([z(v.status),"ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},c(P(v.status)),3)]),e("p",Ei,c(v.description),1),e("div",Ii,[v.client?(s(),a("span",Vi,"Cliente: "+c(v.client.name),1)):_("",!0),v.client?(s(),a("span",Ti,"•")):_("",!0),v.end_date?(s(),a("span",Di,"Scadenza: "+c(b(v.end_date)),1)):_("",!0),v.end_date&&v.budget?(s(),a("span",Li,"•")):_("",!0),v.budget?(s(),a("span",qi,"Budget: "+c(q(v.budget)),1)):_("",!0)]),v.kpis?(s(),a("div",Ri,[v.kpis.budget_usage!==void 0?(s(),a("div",Bi,[e("div",{class:S(["w-3 h-3 rounded-full",se(v.kpis.budget_usage)])},null,2),e("span",Hi," Budget: "+c(v.kpis.budget_usage)+"% ",1)])):_("",!0),v.kpis.time_usage!==void 0?(s(),a("div",Oi,[e("div",{class:S(["w-3 h-3 rounded-full",re(v.kpis.time_usage)])},null,2),e("span",Ni," Tempo: "+c(v.kpis.time_usage)+"% ",1)])):_("",!0),v.kpis.margin!==void 0?(s(),a("div",Ui,[e("div",{class:S(["w-3 h-3 rounded-full",M(v.kpis.margin)])},null,2),e("span",Fi," Margine: "+c(v.kpis.margin)+"% ",1)])):_("",!0)])):_("",!0)]),e("div",Ki,[e("div",Wi,[e("div",Gi,c(G(v))+"% ",1),e("div",Qi,[e("div",{class:"bg-primary-600 h-2 rounded-full",style:fe({width:G(v)+"%"})},null,4)])]),v.kpis?(s(),a("div",Ji,[p[12]||(p[12]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"KPI Status",-1)),e("div",Yi,[e("div",{class:S(["w-2 h-2 rounded-full",E(v.kpis)])},null,2),e("span",{class:S(["text-xs font-medium",L(v.kpis)])},c(T(v.kpis)),3)])])):_("",!0),p[13]||(p[13]=e("svg",{class:"h-5 w-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1))])])],8,ji))),128))]))])]))}},Zi=[{path:"/",component:ze,children:[{path:"",name:"home",component:zo},{path:"about",name:"about",component:ra},{path:"contact",name:"contact",component:Ia},{path:"services",name:"services",component:Qa}]},{path:"/auth",component:ze,children:[{path:"login",name:"login",component:nn},{path:"register",name:"register",component:un}]},{path:"/app",component:Cr,meta:{requiresAuth:!0},children:[{path:"",redirect:"/app/dashboard"},{path:"dashboard",name:"dashboard",component:hi,meta:{requiresAuth:!0,requiredPermission:"view_dashboard"}},{path:"projects",name:"projects",component:Xi,meta:{requiresAuth:!0,requiredPermission:"view_all_projects"}},{path:"projects/create",name:"projects-create",component:()=>D(()=>import("./ProjectCreate.js"),__vite__mapDeps([0,1])),meta:{requiresAuth:!0,requiredPermission:"edit_project"}},{path:"projects/:id",name:"project-view",component:()=>D(()=>import("./ProjectView.js"),__vite__mapDeps([2,1,3])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects"}},{path:"projects/:id/edit",name:"project-edit",component:()=>D(()=>import("./ProjectEdit.js"),__vite__mapDeps([4,1])),meta:{requiresAuth:!0,requiredPermission:"edit_project"}},{path:"timesheet",redirect:"/app/timesheet/entry"},{path:"timesheet/entry",name:"timesheet-entry",component:()=>D(()=>import("./TimesheetEntry.js"),__vite__mapDeps([5,1])),meta:{requiresAuth:!0,requiredPermission:"manage_timesheets"}},{path:"timesheet/requests",name:"timesheet-requests",component:()=>D(()=>import("./TimesheetRequests.js"),__vite__mapDeps([6,1])),meta:{requiresAuth:!0,requiredPermission:"manage_timesheets"}},{path:"timesheet/dashboard",name:"timesheet-dashboard",component:()=>D(()=>import("./TimesheetDashboard.js"),__vite__mapDeps([7,1])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects"}},{path:"timesheet/history",name:"timesheet-history",component:()=>D(()=>import("./TimesheetHistory.js"),__vite__mapDeps([8,1])),meta:{requiresAuth:!0,requiredPermission:"manage_timesheets"}},{path:"timesheet/status",name:"timesheet-status",component:()=>D(()=>import("./TimesheetStatus.js"),__vite__mapDeps([9,1])),meta:{requiresAuth:!0,requiredPermission:"manage_timesheets"}},{path:"timesheet/approvals",name:"timesheet-approvals",component:()=>D(()=>import("./TimesheetApprovals.js"),__vite__mapDeps([10,1])),meta:{requiresAuth:!0,requiredPermission:"manage_users"}},{path:"timesheet/analytics",name:"timesheet-analytics",component:()=>D(()=>import("./TimesheetAnalytics.js"),__vite__mapDeps([11,1])),meta:{requiresAuth:!0,requiredPermission:"admin_access"}},{path:"personnel",name:"personnel",component:()=>D(()=>import("./PersonnelDirectory.js"),__vite__mapDeps([12,1,13])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"personnel/orgchart",name:"personnel-orgchart",component:()=>D(()=>import("./PersonnelOrgChart.js"),__vite__mapDeps([14,1,15])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"personnel/skills",name:"personnel-skills",component:()=>D(()=>import("./SkillsMatrix.js"),__vite__mapDeps([16,1,17])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"personnel/departments",name:"personnel-departments",component:()=>D(()=>import("./DepartmentList.js"),__vite__mapDeps([18,1,13])),meta:{requiresAuth:!0,requiredPermission:"manage_users"}},{path:"personnel/departments/create",name:"department-create",component:()=>D(()=>import("./DepartmentCreate.js"),__vite__mapDeps([19,1])),meta:{requiresAuth:!0,requiredPermission:"manage_users"}},{path:"personnel/departments/:id",name:"department-view",component:()=>D(()=>import("./DepartmentView.js"),__vite__mapDeps([20,1,13])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"personnel/departments/:id/edit",name:"department-edit",component:()=>D(()=>import("./DepartmentEdit.js"),__vite__mapDeps([21,1])),meta:{requiresAuth:!0,requiredPermission:"manage_users"}},{path:"personnel/allocation",name:"personnel-allocation",component:()=>D(()=>import("./PersonnelAllocation.js"),__vite__mapDeps([22,1])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"personnel/admin",name:"personnel-admin",component:()=>D(()=>import("./PersonnelAdmin.js"),__vite__mapDeps([23,1,24])),meta:{requiresAuth:!0,requiredPermission:"admin_access"}},{path:"personnel/:id",name:"personnel-profile",component:()=>D(()=>import("./PersonnelProfile.js"),__vite__mapDeps([25,1,13,26])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"admin",redirect:"/app/admin/users"},{path:"admin/users",name:"admin-users",component:()=>D(()=>import("./Admin.js"),__vite__mapDeps([27,1])),meta:{requiresAuth:!0,requiredPermission:"admin_access"}},{path:"admin/kpi-templates",name:"admin-kpi-templates",component:()=>D(()=>import("./KPITemplates.js"),__vite__mapDeps([28,1])),meta:{requiresAuth:!0,requiredPermission:"admin_access"}},{path:"profile",name:"profile",component:()=>D(()=>import("./Profile.js"),__vite__mapDeps([29,1])),meta:{requiresAuth:!0}},{path:"settings",name:"settings",component:()=>D(()=>import("./Settings.js"),__vite__mapDeps([30,1])),meta:{requiresAuth:!0}}]}],Ee=Re({history:Be(),routes:Zi});Ee.beforeEach(async(o,r,l)=>{const t=X();if(o.meta.requiresAuth){if(t.sessionChecked||await t.initializeAuth(),!t.isAuthenticated){l("/auth/login");return}if(o.meta.requiredPermission&&!t.hasPermission(o.meta.requiredPermission)){console.warn(`Accesso negato a ${o.path}: permesso '${o.meta.requiredPermission}' richiesto`),l("/app/dashboard");return}}l()});const ce=He(Fe),el=Oe();ce.use(el);ce.use(Ee);const tl=X();tl.initializeAuth().then(()=>{console.log("Auth initialized successfully"),ce.mount("#app")}).catch(o=>{console.error("Auth initialization failed:",o),ce.mount("#app")});export{Ae as _,Ge as a,fi as b,B as c,xe as d,X as u};

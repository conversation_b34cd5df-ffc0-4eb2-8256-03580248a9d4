const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/ProjectCreate.js","assets/vendor.js","assets/ProjectView.js","assets/ProjectView.css","assets/ProjectEdit.js","assets/TimesheetEntry.js","assets/TimesheetRequests.js","assets/TimesheetDashboard.js","assets/TimesheetHistory.js","assets/TimesheetApprovals.js","assets/TimesheetAnalytics.js","assets/PersonnelDirectory.js","assets/personnel.js","assets/PersonnelOrgChart.js","assets/PersonnelOrgChart.css","assets/SkillsMatrix.js","assets/SkillsMatrix.css","assets/DepartmentList.js","assets/DepartmentCreate.js","assets/DepartmentView.js","assets/DepartmentEdit.js","assets/PersonnelAllocation.js","assets/PersonnelAdmin.js","assets/PersonnelAdmin.css","assets/PersonnelProfile.js","assets/PersonnelProfile.css","assets/Admin.js","assets/KPITemplates.js","assets/Profile.js","assets/Settings.js"])))=>i.map(i=>d[i]);
import{r as j,w as le,c as r,a as C,b as R,o as s,d as Ve,e as ve,f as y,g as _,n as S,h as q,i as A,t as c,u as ge,j as e,F as N,k as U,l as ee,m as V,p as O,q as Pe,s as he,v as K,x as Z,y as ne,z as fe,A as Q,T as Ie,B as Te,C as De,D as ue,E as Le,G as pe,H as He,I as qe,J as Be,K as Re,L as Oe}from"./vendor.js";(function(){const a=document.createElement("link").relList;if(a&&a.supports&&a.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))t(n);new MutationObserver(n=>{for(const u of n)if(u.type==="childList")for(const m of u.addedNodes)m.tagName==="LINK"&&m.rel==="modulepreload"&&t(m)}).observe(document,{childList:!0,subtree:!0});function l(n){const u={};return n.integrity&&(u.integrity=n.integrity),n.referrerPolicy&&(u.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?u.credentials="include":n.crossOrigin==="anonymous"?u.credentials="omit":u.credentials="same-origin",u}function t(n){if(n.ep)return;n.ep=!0;const u=l(n);fetch(n.href,u)}})();const W=j(!1);let $e=!1;const Se=o=>{o?(document.documentElement.classList.add("dark"),localStorage.setItem("darkMode","true")):(document.documentElement.classList.remove("dark"),localStorage.setItem("darkMode","false"))},Ne=()=>{$e||(le(W,o=>{Se(o)}),$e=!0)};function xe(){return Ne(),{isDarkMode:W,toggleDarkMode:()=>{W.value=!W.value},setDarkMode:t=>{W.value=t},initializeDarkMode:()=>{const t=localStorage.getItem("darkMode"),n=document.documentElement.classList.contains("dark");if(t==="true")W.value=!0;else if(t==="false")W.value=!1;else{const h=window.matchMedia("(prefers-color-scheme: dark)").matches;W.value=n||h}Se(W.value);const u=window.matchMedia("(prefers-color-scheme: dark)"),m=h=>{const v=localStorage.getItem("darkMode");(!v||v==="null")&&(W.value=h.matches)};u.addEventListener("change",m)}}}const Ue={id:"app"},Fe={__name:"App",setup(o){const{initializeDarkMode:a}=xe();return a(),(l,t)=>{const n=R("router-view");return s(),r("div",Ue,[C(n)])}}},Ke="modulepreload",We=function(o){return"/"+o},Ce={},D=function(a,l,t){let n=Promise.resolve();if(l&&l.length>0){document.getElementsByTagName("link");const m=document.querySelector("meta[property=csp-nonce]"),h=(m==null?void 0:m.nonce)||(m==null?void 0:m.getAttribute("nonce"));n=Promise.allSettled(l.map(v=>{if(v=We(v),v in Ce)return;Ce[v]=!0;const d=v.endsWith(".css"),i=d?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${v}"]${i}`))return;const f=document.createElement("link");if(f.rel=d?"stylesheet":Ke,d||(f.as="script"),f.crossOrigin="",f.href=v,h&&f.setAttribute("nonce",h),document.head.appendChild(f),d)return new Promise((k,w)=>{f.addEventListener("load",k),f.addEventListener("error",()=>w(new Error(`Unable to preload CSS for ${v}`)))})}))}function u(m){const h=new Event("vite:preloadError",{cancelable:!0});if(h.payload=m,window.dispatchEvent(h),!h.defaultPrevented)throw m}return n.then(m=>{for(const h of m||[])h.status==="rejected"&&u(h.reason);return a().catch(u)})},B=Ve.create({baseURL:"",timeout:6e4,withCredentials:!0,headers:{"Content-Type":"application/json"}});B.interceptors.request.use(o=>{var l,t;const a=(l=document.querySelector('meta[name="csrf-token"]'))==null?void 0:l.getAttribute("content");return a&&["post","put","patch","delete"].includes((t=o.method)==null?void 0:t.toLowerCase())&&(o.headers["X-CSRFToken"]=a),o},o=>Promise.reject(o));B.interceptors.response.use(o=>o,o=>{var a;return((a=o.response)==null?void 0:a.status)===401&&(localStorage.removeItem("user"),console.warn("Sessione scaduta, autenticazione richiesta")),Promise.reject(o)});const X=ve("auth",()=>{const o=localStorage.getItem("user"),a=j(o?JSON.parse(o):null),l=j(!1),t=j(null),n=j(!1),u=y(()=>!!a.value&&n.value),m={admin:["admin","manage_users","assign_roles","view_all_projects","create_project","edit_project","delete_project","assign_to_project","manage_project_tasks","manage_project_resources","approve_timesheets","view_personnel_data","edit_personnel_data","view_contracts","manage_contracts","view_crm","manage_clients","manage_proposals","view_reports","view_dashboard","submit_timesheet","view_own_timesheets","view_funding","manage_funding","view_products","manage_products","view_performance","manage_performance","view_communications","manage_communications","view_startup","manage_startup"],manager:["view_dashboard","view_all_projects","edit_project","assign_to_project","manage_project_tasks","manage_project_resources","approve_timesheets","view_personnel_data","view_crm","view_reports","submit_timesheet","view_own_timesheets","manage_clients","manage_proposals","view_funding","manage_funding","view_products","manage_products","view_performance","manage_performance","view_communications","manage_communications","view_startup","manage_startup"],employee:["view_dashboard","view_own_timesheets","submit_timesheet"],sales:["view_dashboard","view_crm","manage_clients","manage_proposals","submit_timesheet","view_own_timesheets","view_reports","view_funding","view_products","manage_products"],human_resources:["view_dashboard","manage_users","view_personnel_data","edit_personnel_data","view_contracts","manage_contracts","submit_timesheet","view_own_timesheets","view_reports","view_funding","manage_funding","view_performance","manage_performance","view_communications","manage_communications","view_startup","manage_startup"]},h=$=>!a.value||!a.value.role?!1:a.value.role==="admin"?!0:(m[a.value.role]||[]).includes($),v=()=>{var $,z;console.log("Current user:",a.value),console.log("User role:",($=a.value)==null?void 0:$.role),console.log("Has admin permission:",h("admin")),console.log("Available permissions for role:",m[(z=a.value)==null?void 0:z.role])};async function d($){var z,P;l.value=!0,t.value=null;try{const b=await B.post("/api/auth/login",$);return b.data.success?(a.value=b.data.data.user,localStorage.setItem("user",JSON.stringify(a.value)),n.value=!0,{success:!0}):(t.value=b.data.message||"Errore durante il login",{success:!1,error:t.value})}catch(b){return t.value=((P=(z=b.response)==null?void 0:z.data)==null?void 0:P.message)||"Errore di connessione",{success:!1,error:t.value}}finally{l.value=!1}}async function i($){var z,P;l.value=!0,t.value=null;try{const b=await B.post("/api/auth/register",$);return b.data.success?{success:!0,message:b.data.message}:(t.value=b.data.message||"Errore durante la registrazione",{success:!1,error:t.value})}catch(b){return t.value=((P=(z=b.response)==null?void 0:z.data)==null?void 0:P.message)||"Errore di connessione",{success:!1,error:t.value}}finally{l.value=!1}}async function f(){try{await B.post("/api/auth/logout")}catch($){console.warn("Errore durante il logout:",$)}finally{a.value=null,n.value=!1,localStorage.removeItem("user")}}async function k(){if(n.value)return u.value;try{const $=await B.get("/api/auth/me");return $.data.success?(a.value=$.data.data.user,localStorage.setItem("user",JSON.stringify(a.value)),n.value=!0,!0):(await f(),!1)}catch{return await f(),!1}}async function w(){return a.value?await k():(n.value=!0,!1)}return{user:a,loading:l,error:t,sessionChecked:n,isAuthenticated:u,hasPermission:h,debugPermissions:v,login:d,register:i,logout:f,checkAuth:k,initializeAuth:w}}),te=ve("tenant",()=>{const o=j(null),a=j(!1),l=j(null),t=y(()=>{var i;return((i=o.value)==null?void 0:i.company)||{}}),n=y(()=>{var i;return((i=o.value)==null?void 0:i.contact)||{}}),u=y(()=>{var i;return((i=o.value)==null?void 0:i.pages)||{}}),m=y(()=>{var i;return((i=o.value)==null?void 0:i.navigation)||{}}),h=y(()=>{var i;return((i=o.value)==null?void 0:i.footer)||{}});async function v(){try{if(a.value=!0,window.TENANT_CONFIG){o.value=window.TENANT_CONFIG;return}const i=await fetch("/api/config/tenant");o.value=await i.json()}catch(i){l.value="Errore nel caricamento della configurazione",console.error("Errore caricamento tenant config:",i)}finally{a.value=!1}}function d(i,f={}){if(!i||typeof i!="string")return i;let k=i;const w={"company.name":t.value.name||"DatVinci","company.tagline":t.value.tagline||"","company.description":t.value.description||"","company.mission":t.value.mission||"","company.vision":t.value.vision||"","company.founded":t.value.founded||"","company.team_size":t.value.team_size||"","contact.email":n.value.email||"","contact.phone":n.value.phone||"","contact.address":n.value.address||"",current_year:new Date().getFullYear().toString(),...f};for(const[$,z]of Object.entries(w)){const P=new RegExp(`\\{${$}\\}`,"g");k=k.replace(P,z||"")}return k}return{config:o,loading:a,error:l,company:t,contact:n,pages:u,navigation:m,footer:h,loadConfig:v,interpolateText:d}});function Ge(){const o=X(),a=y(()=>w=>o.hasPermission(w)),l=y(()=>{var w;return((w=o.user)==null?void 0:w.role)||null}),t=y(()=>l.value==="admin"),n=y(()=>l.value==="manager"),u=y(()=>l.value==="employee"),m=y(()=>l.value==="sales"),h=y(()=>l.value==="human_resources"),v=y(()=>a.value("create_project")||a.value("edit_project")||a.value("delete_project")),d=y(()=>a.value("manage_users")||a.value("assign_roles")),i=y(()=>a.value("view_all_projects")),f=y(()=>a.value("view_personnel_data")||a.value("edit_personnel_data")),k=y(()=>a.value("approve_timesheets"));return{hasPermission:a,userRole:l,isAdmin:t,isManager:n,isEmployee:u,isSales:m,isHR:h,canManageProjects:v,canManageUsers:d,canViewAllProjects:i,canManagePersonnel:f,canApproveTimesheets:k}}const Qe={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"},Je={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"},Ye={key:2,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"},Xe={key:3,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"},Ze={key:4,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"},et={key:5,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"},tt={key:6,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"},st={key:7,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"},ot={key:8,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"},rt={key:9,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"},at={key:10,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"},nt={key:11,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"},it={key:12,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"},lt={key:13,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"},dt={key:14,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"},ct={key:15,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"},ut={key:16,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"},mt={key:17,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"},pt={key:18,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"},vt={key:19,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"},gt={key:20,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"},ht={key:21,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"},ft={key:22,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8"},xt={key:23,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"},yt={key:24,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v6m3-3H9"},_t={key:25,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"},kt={key:26,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"},bt={key:27,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"},wt={key:28,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"},$t={key:29,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"},Ct={key:30,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"},Mt={key:31,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 12a8 8 0 018-8V2.5"},jt={key:32,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"},zt={key:33,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"},Pt={key:34,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4"},St={key:35,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"},At={key:36,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"},de={__name:"SidebarIcon",props:{icon:{type:String,required:!0},className:{type:String,default:"h-5 w-5"}},setup(o){return(a,l)=>(s(),r("svg",{class:S(o.className),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o.icon==="dashboard"?(s(),r("path",Qe)):o.icon==="projects"?(s(),r("path",Je)):o.icon==="users"?(s(),r("path",Ye)):o.icon==="clients"?(s(),r("path",Xe)):o.icon==="products"?(s(),r("path",Ze)):o.icon==="reports"?(s(),r("path",et)):o.icon==="settings"?(s(),r("path",tt)):_("",!0),o.icon==="settings"?(s(),r("path",st)):o.icon==="user-management"?(s(),r("path",ot)):o.icon==="communications"?(s(),r("path",rt)):o.icon==="funding"?(s(),r("path",at)):o.icon==="reporting"?(s(),r("path",nt)):o.icon==="team"?(s(),r("path",it)):o.icon==="directory"?(s(),r("path",lt)):o.icon==="orgchart"?(s(),r("path",dt)):o.icon==="skills"?(s(),r("path",ct)):o.icon==="departments"?(s(),r("path",ut)):o.icon==="admin"?(s(),r("path",mt)):o.icon==="allocation"?(s(),r("path",pt)):o.icon==="user-profile"?(s(),r("path",vt)):o.icon==="clock"?(s(),r("path",gt)):o.icon==="clock-play"?(s(),r("path",ht)):_("",!0),o.icon==="clock-play"?(s(),r("path",ft)):o.icon==="calendar-plus"?(s(),r("path",xt)):_("",!0),o.icon==="calendar-plus"?(s(),r("path",yt)):o.icon==="chart-bar"?(s(),r("path",_t)):o.icon==="archive"?(s(),r("path",kt)):o.icon==="clipboard-check"?(s(),r("path",bt)):o.icon==="user-group"?(s(),r("path",wt)):o.icon==="chart-line"?(s(),r("path",$t)):o.icon==="history"?(s(),r("path",Ct)):_("",!0),o.icon==="history"?(s(),r("path",Mt)):o.icon==="check-circle"?(s(),r("path",jt)):o.icon==="users-check"?(s(),r("path",zt)):_("",!0),o.icon==="users-check"?(s(),r("path",Pt)):o.icon==="analytics"?(s(),r("path",St)):(s(),r("path",At))],2))}},Et={key:0,class:"truncate"},Vt={key:0,class:"truncate"},Y={__name:"SidebarNavItem",props:{item:{type:Object,required:!0},isCollapsed:{type:Boolean,default:!1}},emits:["click"],setup(o){const a=y(()=>["text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400"]);return(l,t)=>{const n=R("router-link");return s(),r("div",null,[o.item.path!=="#"?(s(),q(n,{key:0,to:o.item.path,class:S(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",[a.value,{"justify-center":o.isCollapsed}]]),"active-class":"text-primary-600 bg-primary-50 border-r-2 border-primary-600",onClick:t[0]||(t[0]=u=>l.$emit("click"))},{default:A(()=>[C(de,{icon:o.item.icon,class:S(["flex-shrink-0 h-6 w-6",{"mr-0":o.isCollapsed,"mr-3":!o.isCollapsed}])},null,8,["icon","class"]),o.isCollapsed?_("",!0):(s(),r("span",Et,c(o.item.name),1))]),_:1},8,["to","class"])):(s(),r("div",{key:1,class:S(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150 cursor-not-allowed opacity-75",["text-gray-400 hover:text-gray-500",{"justify-center":o.isCollapsed}]])},[C(de,{icon:o.item.icon,class:S(["flex-shrink-0 h-6 w-6",{"mr-0":o.isCollapsed,"mr-3":!o.isCollapsed}])},null,8,["icon","class"]),o.isCollapsed?_("",!0):(s(),r("span",Vt,c(o.item.name),1))],2))])}}},It={key:0,class:"flex-1 text-left truncate"},Tt={key:0,class:"ml-6 space-y-1 mt-1"},Dt={class:"truncate"},me={__name:"SidebarNavItemCollapsible",props:{item:{type:Object,required:!0},isCollapsed:{type:Boolean,default:!1}},emits:["click"],setup(o){const a=o,l=ge(),t=X(),n=j(!1),u=y(()=>["text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400",{"text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900":m.value}]),m=y(()=>a.item.children?a.item.children.some(i=>i.path!=="#"&&l.path.startsWith(i.path)):!1),h=y(()=>a.item.children?a.item.children.filter(i=>{var f;return i.admin?((f=t.user)==null?void 0:f.role)==="admin":!0}):[]);m.value&&(n.value=!0);function v(){a.isCollapsed||(n.value=!n.value)}function d(i){if(i.path==="#")return!1}return(i,f)=>{const k=R("router-link");return s(),r("div",null,[e("button",{onClick:v,class:S(["group w-full flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",[u.value,{"justify-center":o.isCollapsed}]])},[C(de,{icon:o.item.icon,class:S(["flex-shrink-0 h-6 w-6",{"mr-0":o.isCollapsed,"mr-3":!o.isCollapsed}])},null,8,["icon","class"]),o.isCollapsed?_("",!0):(s(),r("span",It,c(o.item.name),1)),o.isCollapsed?_("",!0):(s(),r("svg",{key:1,class:S([{"rotate-90":n.value},"ml-2 h-4 w-4 transition-transform duration-150"]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},f[0]||(f[0]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"},null,-1)]),2))],2),n.value&&!o.isCollapsed?(s(),r("div",Tt,[(s(!0),r(N,null,U(h.value,w=>(s(),q(k,{key:w.name,to:w.path,class:S(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",w.path==="#"?"text-gray-400 dark:text-gray-500 hover:text-gray-500 dark:hover:text-gray-400 cursor-not-allowed opacity-75":"text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400"]),"active-class":"text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900",onClick:$=>d(w)},{default:A(()=>[w.icon?(s(),q(de,{key:0,icon:w.icon,class:"flex-shrink-0 h-4 w-4 mr-2"},null,8,["icon"])):_("",!0),e("span",Dt,c(w.name),1)]),_:2},1032,["to","class","onClick"]))),128))])):_("",!0)])}}},Lt={class:"mt-5 flex-grow flex flex-col overflow-hidden"},Ht={class:"flex-1 px-2 space-y-1"},Me={__name:"SidebarNavigation",props:{isCollapsed:{type:Boolean,default:!1}},emits:["item-click"],setup(o){const{hasPermission:a}=Ge(),l=y(()=>a.value("view_dashboard")),t=y(()=>a.value("view_personnel_data")),n=y(()=>a.value("view_all_projects")),u=y(()=>a.value("manage_timesheets")||a.value("view_all_projects")),m=y(()=>a.value("view_crm")),h=y(()=>a.value("view_products")),v=y(()=>a.value("view_performance")),d=y(()=>a.value("view_communications")),i=y(()=>a.value("view_funding")),f=y(()=>a.value("view_reports")),k=y(()=>a.value("admin_access"));return(w,$)=>(s(),r("div",Lt,[e("nav",Ht,[l.value?(s(),q(Y,{key:0,item:{name:"Dashboard",path:"/app/dashboard",icon:"dashboard"},"is-collapsed":o.isCollapsed,onClick:$[0]||($[0]=z=>w.$emit("item-click"))},null,8,["is-collapsed"])):_("",!0),t.value?(s(),q(me,{key:1,item:{name:"Personale",icon:"users",children:[{name:"Directory",path:"/app/personnel",icon:"directory"},{name:"Organigramma",path:"/app/personnel/orgchart",icon:"orgchart"},{name:"Competenze",path:"/app/personnel/skills",icon:"skills"},{name:"Allocazione Risorse",path:"/app/personnel/allocation",icon:"allocation"},{name:"Dipartimenti",path:"/app/personnel/departments",icon:"departments",admin:!0},{name:"Amministrazione",path:"/app/personnel/admin",icon:"admin",admin:!0}]},"is-collapsed":o.isCollapsed,onClick:$[1]||($[1]=z=>w.$emit("item-click"))},null,8,["is-collapsed"])):_("",!0),n.value?(s(),q(Y,{key:2,item:{name:"Progetti",path:"/app/projects",icon:"projects"},"is-collapsed":o.isCollapsed,onClick:$[2]||($[2]=z=>w.$emit("item-click"))},null,8,["is-collapsed"])):_("",!0),u.value?(s(),q(me,{key:3,item:{name:"Timesheet",icon:"clock",children:[{name:"Le Mie Ore",path:"/app/timesheet/entry",icon:"clock-play"},{name:"Richieste",path:"/app/timesheet/requests",icon:"calendar-plus"},{name:"Dashboard",path:"/app/timesheet/dashboard",icon:"chart-bar"},{name:"Storico",path:"/app/timesheet/history",icon:"archive"},{name:"Approvazioni Team",path:"/app/timesheet/approvals",icon:"user-group",manager:!0},{name:"Report & Analytics",path:"/app/timesheet/analytics",icon:"chart-line",admin:!0}]},"is-collapsed":o.isCollapsed,onClick:$[3]||($[3]=z=>w.$emit("item-click"))},null,8,["is-collapsed"])):_("",!0),m.value?(s(),q(Y,{key:4,item:{name:"CRM",path:"#",icon:"clients"},"is-collapsed":o.isCollapsed,onClick:$[4]||($[4]=z=>w.$emit("item-click"))},null,8,["is-collapsed"])):_("",!0),h.value?(s(),q(Y,{key:5,item:{name:"Prodotti",path:"#",icon:"products"},"is-collapsed":o.isCollapsed,onClick:$[5]||($[5]=z=>w.$emit("item-click"))},null,8,["is-collapsed"])):_("",!0),v.value?(s(),q(Y,{key:6,item:{name:"Performance",path:"#",icon:"reports"},"is-collapsed":o.isCollapsed,onClick:$[6]||($[6]=z=>w.$emit("item-click"))},null,8,["is-collapsed"])):_("",!0),d.value?(s(),q(Y,{key:7,item:{name:"Comunicazione",path:"#",icon:"communications"},"is-collapsed":o.isCollapsed,onClick:$[7]||($[7]=z=>w.$emit("item-click"))},null,8,["is-collapsed"])):_("",!0),i.value?(s(),q(Y,{key:8,item:{name:"Finanziamenti",path:"#",icon:"funding"},"is-collapsed":o.isCollapsed,onClick:$[8]||($[8]=z=>w.$emit("item-click"))},null,8,["is-collapsed"])):_("",!0),f.value?(s(),q(Y,{key:9,item:{name:"Rendicontazione",path:"#",icon:"reporting"},"is-collapsed":o.isCollapsed,onClick:$[9]||($[9]=z=>w.$emit("item-click"))},null,8,["is-collapsed"])):_("",!0),k.value?(s(),q(me,{key:10,item:{name:"Amministrazione",icon:"settings",children:[{name:"Gestione Utenti",path:"/app/admin/users",icon:"user-management"},{name:"Template KPI",path:"/app/admin/kpi-templates",icon:"reports"}]},"is-collapsed":o.isCollapsed,onClick:$[10]||($[10]=z=>w.$emit("item-click"))},null,8,["is-collapsed"])):_("",!0)])]))}},qt={class:"flex-shrink-0 border-t border-gray-200 p-4"},Bt={class:"flex-shrink-0"},Rt={class:"h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center"},Ot={class:"text-sm font-medium text-primary-700"},Nt={key:0,class:"ml-3 flex-1 min-w-0"},Ut={class:"text-sm font-medium text-gray-900 truncate"},Ft={class:"text-xs text-gray-500 truncate"},Kt={class:"py-1"},Wt={key:0,class:"mt-3 text-xs text-gray-400 text-center"},je={__name:"SidebarFooter",props:{isCollapsed:{type:Boolean,default:!1}},setup(o){const a=ee(),l=X(),t=j(!1),n=y(()=>l.user&&(l.user.name||l.user.username)||"Utente"),u=y(()=>l.user?n.value.charAt(0).toUpperCase():"U"),m=y(()=>l.user?{admin:"Amministratore",manager:"Manager",employee:"Dipendente",client:"Cliente"}[l.user.role]||l.user.role:""),h=y(()=>"1.0.0");async function v(){t.value=!1,await l.logout(),a.push("/auth/login")}return(d,i)=>{const f=R("router-link");return s(),r("div",qt,[e("div",{class:S(["flex items-center",{"justify-center":o.isCollapsed}])},[e("div",Bt,[e("div",Rt,[e("span",Ot,c(u.value),1)])]),o.isCollapsed?_("",!0):(s(),r("div",Nt,[e("p",Ut,c(n.value),1),e("p",Ft,c(m.value),1)])),e("div",{class:S(["relative",{"ml-3":!o.isCollapsed}])},[e("button",{onClick:i[0]||(i[0]=k=>t.value=!t.value),class:"p-1 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"},i[4]||(i[4]=[e("svg",{class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zM13 12a1 1 0 11-2 0 1 1 0 012 0zM20 12a1 1 0 11-2 0 1 1 0 012 0z"})],-1)])),t.value?(s(),r("div",{key:0,onClick:i[3]||(i[3]=k=>t.value=!1),class:"origin-bottom-left fixed bottom-16 left-4 w-48 rounded-md shadow-xl bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 border border-gray-200 dark:border-gray-600",style:{"z-index":"99999"}},[e("div",Kt,[C(f,{to:"/app/profile",class:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:i[1]||(i[1]=k=>t.value=!1)},{default:A(()=>i[5]||(i[5]=[V(" Il tuo profilo ")])),_:1,__:[5]}),C(f,{to:"/app/settings",class:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:i[2]||(i[2]=k=>t.value=!1)},{default:A(()=>i[6]||(i[6]=[V(" Impostazioni ")])),_:1,__:[6]}),i[7]||(i[7]=e("hr",{class:"my-1 border-gray-200 dark:border-gray-600"},null,-1)),e("button",{onClick:v,class:"block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"}," Esci ")])])):_("",!0)],2)],2),h.value&&!o.isCollapsed?(s(),r("div",Wt," v"+c(h.value),1)):_("",!0)])}}},Gt={class:"flex"},Qt={class:"hidden lg:flex lg:flex-shrink-0 lg:fixed lg:inset-y-0 z-10"},Jt={class:"flex flex-col flex-grow pt-5 pb-4 overflow-y-auto bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 shadow-sm"},Yt={class:"flex items-center flex-shrink-0 px-4"},Xt={class:"w-10 h-10 bg-primary-600 rounded flex items-center justify-center mr-3"},Zt={class:"text-white font-bold text-lg"},es={class:"text-xl font-semibold text-gray-900 dark:text-white"},ts={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center"},ss={class:"text-white font-bold text-sm"},os={class:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},rs=["d"],as={class:"flex flex-col h-full pt-5 pb-4 overflow-y-auto bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 shadow-sm"},ns={class:"flex items-center justify-between px-4 mb-4"},is={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center mr-3"},ls={class:"text-white font-bold text-sm"},ds={class:"text-xl font-semibold text-gray-900 dark:text-white"},cs={__name:"AppSidebar",props:{isMobileOpen:{type:Boolean,default:!1}},emits:["close","toggle-collapsed"],setup(o,{emit:a}){const l=a,t=te(),n=j(!1),u=y(()=>t.config||{}),m=y(()=>{var i;return((i=u.value.company)==null?void 0:i.name)||"DatPortal"}),h=y(()=>m.value.split(" ").map(f=>f[0]).join("").toUpperCase().slice(0,2));function v(){n.value=!n.value,l("toggle-collapsed",n.value)}function d(){n.value&&(n.value=!1)}return(i,f)=>{const k=R("router-link");return s(),r("div",Gt,[e("div",Qt,[e("div",{class:S(["flex flex-col transition-all duration-300",[n.value?"w-20":"w-64"]])},[e("div",Jt,[e("div",Yt,[e("div",{class:S(["flex items-center",{"justify-center":n.value}])},[C(k,{to:"/app/dashboard",class:S(["flex items-center",{hidden:n.value}])},{default:A(()=>[e("div",Xt,[e("span",Zt,c(h.value),1)]),e("h3",es,c(m.value),1)]),_:1},8,["class"]),C(k,{to:"/app/dashboard",class:S(["flex items-center justify-center",{hidden:!n.value}])},{default:A(()=>[e("div",ts,[e("span",ss,c(h.value),1)])]),_:1},8,["class"])],2),e("button",{onClick:v,class:"ml-auto text-gray-600 dark:text-gray-400 focus:outline-none hover:bg-gray-100 dark:hover:bg-gray-700 p-1 rounded"},[(s(),r("svg",os,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:n.value?"M13 5l7 7-7 7M5 5l7 7-7 7":"M11 19l-7-7 7-7m8 14l-7-7 7-7"},null,8,rs)]))])]),C(Me,{"is-collapsed":n.value,onItemClick:d},null,8,["is-collapsed"]),C(je,{"is-collapsed":n.value},null,8,["is-collapsed"])])],2)]),e("div",{class:S(["fixed inset-y-0 left-0 z-30 w-64 bg-primary-700 transform transition-transform duration-300 ease-in-out lg:hidden",o.isMobileOpen?"translate-x-0":"-translate-x-full"])},[e("div",as,[e("div",ns,[C(k,{to:"/app/dashboard",class:"flex items-center"},{default:A(()=>[e("div",is,[e("span",ls,c(h.value),1)]),e("h3",ds,c(m.value),1)]),_:1}),e("button",{onClick:f[0]||(f[0]=w=>i.$emit("close")),class:"p-2 rounded-md text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700"},f[2]||(f[2]=[e("svg",{class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),C(Me,{"is-collapsed":!1,onItemClick:f[1]||(f[1]=w=>i.$emit("close"))}),C(je,{"is-collapsed":!1})])],2)])}}},us={class:"flex","aria-label":"Breadcrumb"},ms={class:"flex items-center space-x-2 text-sm text-gray-500"},ps={key:0,class:"mr-2"},vs={class:"flex items-center"},gs={key:0,class:"h-3 w-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},hs=["d"],fs={key:2,class:"font-medium text-gray-900"},xs={__name:"HeaderBreadcrumbs",props:{breadcrumbs:{type:Array,required:!0}},setup(o){return(a,l)=>{const t=R("router-link");return s(),r("nav",us,[e("ol",ms,[(s(!0),r(N,null,U(o.breadcrumbs,(n,u)=>(s(),r("li",{key:u,class:"flex items-center"},[u>0?(s(),r("div",ps,l[0]||(l[0]=[e("svg",{class:"h-3 w-3 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})],-1)]))):_("",!0),n.to&&u<o.breadcrumbs.length-1?(s(),q(t,{key:1,to:n.to,class:"hover:text-gray-700 transition-colors duration-150"},{default:A(()=>[e("span",vs,[n.icon?(s(),r("svg",gs,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:n.icon},null,8,hs)])):_("",!0),V(" "+c(n.label),1)])]),_:2},1032,["to"])):(s(),r("span",fs,c(n.label),1))]))),128))])])}}},ys={class:"flex items-center space-x-2"},_s={key:0,class:"h-3 w-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},ks={key:1,class:"h-3 w-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},bs={__name:"HeaderQuickActions",emits:["quick-create-project"],setup(o){const a=ge(),{isDarkMode:l,toggleDarkMode:t}=xe(),n=y(()=>{var u;return((u=a.name)==null?void 0:u.includes("projects"))||a.path.includes("/projects")});return(u,m)=>(s(),r("div",ys,[n.value?(s(),r("button",{key:0,onClick:m[0]||(m[0]=h=>u.$emit("quick-create-project")),class:"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},m[2]||(m[2]=[e("svg",{class:"h-3 w-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),V(" Nuovo Progetto ")]))):_("",!0),e("button",{onClick:m[1]||(m[1]=(...h)=>O(t)&&O(t)(...h)),class:"inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700",title:"Cambia tema"},[O(l)?(s(),r("svg",ks,m[4]||(m[4]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"},null,-1)]))):(s(),r("svg",_s,m[3]||(m[3]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"},null,-1)])))])]))}},ws={class:"relative"},$s={class:"relative"},Cs={key:0,class:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center"},Ms={class:"py-1"},js={key:0,class:"px-4 py-8 text-center text-gray-500 text-sm"},zs={key:1,class:"max-h-64 overflow-y-auto"},Ps=["onClick"],Ss={class:"flex items-start"},As={class:"flex-shrink-0"},Es={class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Vs=["d"],Is={class:"ml-3 flex-1"},Ts={class:"text-sm font-medium text-gray-900"},Ds={class:"text-xs text-gray-500 mt-1"},Ls={class:"text-xs text-gray-400 mt-1"},Hs={key:0,class:"flex-shrink-0"},qs={key:2,class:"px-4 py-2 border-t border-gray-100"},Bs={__name:"HeaderNotifications",setup(o){const a=j(!1),l=j([{id:1,type:"task",title:"Nuovo task assegnato",message:'Ti è stato assegnato un nuovo task nel progetto "Website Redesign"',created_at:new Date().toISOString(),read:!1},{id:2,type:"project",title:"Progetto completato",message:'Il progetto "Mobile App" è stato completato con successo',created_at:new Date(Date.now()-36e5).toISOString(),read:!0}]),t=y(()=>l.value.filter(d=>!d.read).length);function n(d){const i={task:"h-6 w-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center",project:"h-6 w-6 rounded-full bg-green-100 text-green-600 flex items-center justify-center",user:"h-6 w-6 rounded-full bg-purple-100 text-purple-600 flex items-center justify-center",system:"h-6 w-6 rounded-full bg-gray-100 text-gray-600 flex items-center justify-center"};return i[d]||i.system}function u(d){const i={task:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2",project:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10",user:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z",system:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"};return i[d]||i.system}function m(d){const i=new Date(d),k=new Date-i;return k<6e4?"Adesso":k<36e5?`${Math.floor(k/6e4)}m fa`:k<864e5?`${Math.floor(k/36e5)}h fa`:i.toLocaleDateString("it-IT")}function h(d){d.read||(d.read=!0),a.value=!1}function v(){l.value.forEach(d=>d.read=!0)}return(d,i)=>(s(),r("div",ws,[e("button",{onClick:i[0]||(i[0]=f=>a.value=!a.value),class:"p-2 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[i[3]||(i[3]=e("span",{class:"sr-only"},"Visualizza notifiche",-1)),e("div",$s,[i[2]||(i[2]=e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 17h5l-5 5v-5zM10 21a2 2 0 01-2-2V7a7 7 0 1114 0v12a2 2 0 01-2 2H10z"})],-1)),t.value>0?(s(),r("span",Cs,c(t.value>9?"9+":t.value),1)):_("",!0)])]),a.value?(s(),r("div",{key:0,onClick:i[1]||(i[1]=f=>a.value=!1),class:"origin-top-right absolute right-0 mt-2 w-80 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50"},[e("div",Ms,[i[5]||(i[5]=e("div",{class:"px-4 py-2 border-b border-gray-100"},[e("h3",{class:"text-sm font-medium text-gray-900"},"Notifiche")],-1)),l.value.length===0?(s(),r("div",js," Nessuna notifica ")):(s(),r("div",zs,[(s(!0),r(N,null,U(l.value,f=>(s(),r("div",{key:f.id,class:"px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-50 last:border-b-0",onClick:k=>h(f)},[e("div",Ss,[e("div",As,[e("div",{class:S(n(f.type))},[(s(),r("svg",Es,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:u(f.type)},null,8,Vs)]))],2)]),e("div",Is,[e("p",Ts,c(f.title),1),e("p",Ds,c(f.message),1),e("p",Ls,c(m(f.created_at)),1)]),f.read?_("",!0):(s(),r("div",Hs,i[4]||(i[4]=[e("div",{class:"h-2 w-2 bg-primary-500 rounded-full"},null,-1)])))])],8,Ps))),128))])),l.value.length>0?(s(),r("div",qs,[e("button",{onClick:v,class:"text-xs text-primary-600 hover:text-primary-800"}," Segna tutte come lette ")])):_("",!0)])])):_("",!0)]))}},Rs={class:"relative"},Os={class:"flex items-start justify-center min-h-screen pt-16 px-4 pb-20 text-center sm:block sm:p-0"},Ns={class:"inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6"},Us={class:"flex items-center"},Fs={class:"flex-1"},Ks={key:0,class:"mt-4 max-h-64 overflow-y-auto"},Ws={class:"space-y-1"},Gs=["onClick"],Qs={class:"flex-shrink-0"},Js={class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Ys=["d"],Xs={class:"ml-3 flex-1 min-w-0"},Zs={class:"text-sm font-medium text-gray-900 truncate"},eo={class:"text-xs text-gray-500 truncate"},to={class:"ml-2 text-xs text-gray-400"},so={key:1,class:"mt-4 text-center py-4"},oo={key:2,class:"mt-4 text-center py-4"},ro={__name:"HeaderSearch",setup(o){const a=ee(),l=j(!1),t=j(""),n=j([]),u=j(-1),m=j(!1),h=j(null),v=[{id:1,type:"project",title:"Website Redesign",description:"Progetto di redesign del sito web aziendale",path:"/app/projects/1"},{id:2,type:"person",title:"Mario Rossi",description:"Senior Developer",path:"/app/personnel/directory/1"},{id:3,type:"document",title:"Specifiche Tecniche",description:"Documento delle specifiche del progetto mobile",path:"/app/documents/1"},{id:4,type:"task",title:"Implementazione API",description:"Task per l'implementazione delle API REST",path:"/app/projects/1/tasks/5"}];le(l,async P=>{var b;P?(await Pe(),(b=h.value)==null||b.focus()):(t.value="",n.value=[],u.value=-1)});function d(){if(!t.value.trim()){n.value=[];return}m.value=!0,setTimeout(()=>{n.value=v.filter(P=>P.title.toLowerCase().includes(t.value.toLowerCase())||P.description.toLowerCase().includes(t.value.toLowerCase())),u.value=-1,m.value=!1},200)}function i(P){if(n.value.length===0)return;const b=u.value+P;b>=0&&b<n.value.length&&(u.value=b)}function f(){u.value>=0&&n.value[u.value]&&k(n.value[u.value])}function k(P){l.value=!1,a.push(P.path)}function w(P){const b={project:"h-6 w-6 rounded bg-blue-100 text-blue-600 flex items-center justify-center",person:"h-6 w-6 rounded bg-green-100 text-green-600 flex items-center justify-center",document:"h-6 w-6 rounded bg-yellow-100 text-yellow-600 flex items-center justify-center",task:"h-6 w-6 rounded bg-purple-100 text-purple-600 flex items-center justify-center"};return b[P]||b.document}function $(P){const b={project:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10",person:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z",document:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z",task:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"};return b[P]||b.document}function z(P){return{project:"Progetto",person:"Persona",document:"Documento",task:"Task"}[P]||"Elemento"}return(P,b)=>(s(),r("div",Rs,[e("button",{onClick:b[0]||(b[0]=H=>l.value=!l.value),class:"p-2 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},b[7]||(b[7]=[e("span",{class:"sr-only"},"Cerca",-1),e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)])),l.value?(s(),r("div",{key:0,class:"fixed inset-0 z-50 overflow-y-auto",onClick:b[6]||(b[6]=he(H=>l.value=!1,["self"]))},[e("div",Os,[b[11]||(b[11]=e("div",{class:"fixed inset-0 bg-black bg-opacity-25 transition-opacity"},null,-1)),e("div",Ns,[e("div",null,[e("div",Us,[e("div",Fs,[K(e("input",{ref_key:"searchInput",ref:h,"onUpdate:modelValue":b[1]||(b[1]=H=>t.value=H),onInput:d,onKeydown:[b[2]||(b[2]=ne(H=>l.value=!1,["escape"])),ne(f,["enter"]),b[3]||(b[3]=ne(H=>i(-1),["up"])),b[4]||(b[4]=ne(H=>i(1),["down"]))],type:"text",placeholder:"Cerca progetti, persone, documenti...",class:"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"},null,544),[[Z,t.value]])]),e("button",{onClick:b[5]||(b[5]=H=>l.value=!1),class:"ml-3 p-2 text-gray-400 hover:text-gray-600"},b[8]||(b[8]=[e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),n.value.length>0?(s(),r("div",Ks,[e("div",Ws,[(s(!0),r(N,null,U(n.value,(H,G)=>(s(),r("div",{key:H.id,onClick:se=>k(H),class:S(["flex items-center px-3 py-2 rounded-md cursor-pointer",G===u.value?"bg-primary-50":"hover:bg-gray-50"])},[e("div",Qs,[e("div",{class:S(w(H.type))},[(s(),r("svg",Js,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:$(H.type)},null,8,Ys)]))],2)]),e("div",Xs,[e("p",Zs,c(H.title),1),e("p",eo,c(H.description),1)]),e("div",to,c(z(H.type)),1)],10,Gs))),128))])])):t.value&&!m.value?(s(),r("div",so,b[9]||(b[9]=[e("p",{class:"text-sm text-gray-500"},"Nessun risultato trovato",-1)]))):t.value?_("",!0):(s(),r("div",oo,b[10]||(b[10]=[e("p",{class:"text-xs text-gray-400"},"Inizia a digitare per cercare...",-1)])))])])])])):_("",!0)]))}},ao={class:"relative"},no={class:"h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center"},io={class:"text-sm font-medium text-primary-700"},lo={class:"py-1"},co={class:"px-4 py-2 border-b border-gray-100 dark:border-gray-700"},uo={class:"text-sm font-medium text-gray-900 dark:text-white"},mo={class:"text-xs text-gray-500 dark:text-gray-400"},po={__name:"HeaderUserMenu",setup(o){const a=ee(),l=X(),t=j(!1),{isDarkMode:n,toggleDarkMode:u}=xe(),m=y(()=>l.user&&(l.user.name||l.user.username)||"Utente"),h=y(()=>{var i;return((i=l.user)==null?void 0:i.email)||""}),v=y(()=>l.user?m.value.charAt(0).toUpperCase():"U");async function d(){t.value=!1,await l.logout(),a.push("/auth/login")}return(i,f)=>{const k=R("router-link");return s(),r("div",ao,[e("button",{onClick:f[0]||(f[0]=w=>t.value=!t.value),class:"flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[f[5]||(f[5]=e("span",{class:"sr-only"},"Apri menu utente",-1)),e("div",no,[e("span",io,c(v.value),1)])]),t.value?(s(),r("div",{key:0,onClick:f[4]||(f[4]=w=>t.value=!1),class:"origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 z-50"},[e("div",lo,[e("div",co,[e("p",uo,c(m.value),1),e("p",mo,c(h.value),1)]),C(k,{to:"/app/profile",class:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:f[1]||(f[1]=w=>t.value=!1)},{default:A(()=>f[6]||(f[6]=[V(" Il tuo profilo ")])),_:1,__:[6]}),C(k,{to:"/app/settings",class:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:f[2]||(f[2]=w=>t.value=!1)},{default:A(()=>f[7]||(f[7]=[V(" Impostazioni ")])),_:1,__:[7]}),f[8]||(f[8]=e("div",{class:"border-t border-gray-100 dark:border-gray-700 my-1"},null,-1)),e("button",{onClick:f[3]||(f[3]=(...w)=>O(u)&&O(u)(...w)),class:"flex items-center justify-between w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"},[e("span",null,c(O(n)?"Modalità chiara":"Modalità scura"),1),e("i",{class:S([O(n)?"fas fa-sun":"fas fa-moon","text-xs"])},null,2)]),e("button",{onClick:d,class:"block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"}," Esci ")])])):_("",!0)])}}},vo={class:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700"},go={class:"flex justify-between items-center px-4 py-4 sm:px-6 lg:px-8"},ho={class:"flex items-center space-x-4"},fo={class:"flex flex-col"},xo={class:"text-lg font-semibold text-gray-900 dark:text-white"},yo={class:"flex items-center space-x-4"},_o={class:"hidden md:flex items-center space-x-2"},ko={__name:"AppHeader",props:{pageTitle:{type:String,required:!0},breadcrumbs:{type:Array,default:()=>[]}},emits:["toggle-mobile-sidebar","quick-create-project"],setup(o){return(a,l)=>(s(),r("header",vo,[e("div",go,[e("div",ho,[e("button",{onClick:l[0]||(l[0]=t=>a.$emit("toggle-mobile-sidebar")),class:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:text-gray-500 dark:hover:text-gray-300 dark:hover:bg-gray-700"},l[2]||(l[2]=[e("svg",{class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"})],-1)])),e("div",fo,[e("h2",xo,c(o.pageTitle),1),o.breadcrumbs.length>0?(s(),q(xs,{key:0,breadcrumbs:o.breadcrumbs},null,8,["breadcrumbs"])):_("",!0)])]),e("div",yo,[e("div",_o,[C(bs,{onQuickCreateProject:l[1]||(l[1]=t=>a.$emit("quick-create-project"))})]),C(Bs),C(ro),C(po)])])]))}},bo={__name:"LoadingSpinner",props:{size:{type:String,default:"md",validator:o=>["sm","md","lg","xl"].includes(o)},message:{type:String,default:""},centered:{type:Boolean,default:!0}},setup(o){const a=o,l=y(()=>{const m={sm:"20px",md:"32px",lg:"48px",xl:"64px"};return`width: ${m[a.size]}; height: ${m[a.size]};`}),t=y(()=>["flex",a.centered?"items-center justify-center":"","space-y-2"]),n=y(()=>["flex items-center justify-center"]),u=y(()=>["text-sm text-gray-600 text-center"]);return(m,h)=>(s(),r("div",{class:S(t.value)},[e("div",{class:S(n.value)},[e("div",{class:"animate-spin rounded-full border-2 border-gray-300 border-t-primary-600",style:fe(l.value)},null,4)],2),o.message?(s(),r("p",{key:0,class:S(u.value)},c(o.message),3)):_("",!0)],2))}},Ae=(o,a)=>{const l=o.__vccOpts||o;for(const[t,n]of a)l[t]=n;return l},wo={class:"fixed bottom-0 right-0 z-50 p-6 space-y-4"},$o={class:"p-4"},Co={class:"flex items-start"},Mo={class:"flex-shrink-0"},jo={class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},zo=["d"],Po={class:"ml-3 w-0 flex-1 pt-0.5"},So={class:"text-sm font-medium text-gray-900"},Ao={class:"mt-1 text-sm text-gray-500"},Eo={class:"ml-4 flex-shrink-0 flex"},Vo=["onClick"],Io={__name:"NotificationManager",setup(o){const a=j([]);function l(h){const v=Date.now(),d={id:v,type:h.type||"info",title:h.title,message:h.message,duration:h.duration||5e3};a.value.push(d),d.duration>0&&setTimeout(()=>{t(v)},d.duration)}function t(h){const v=a.value.findIndex(d=>d.id===h);v>-1&&a.value.splice(v,1)}function n(h){const v={success:"border-l-4 border-green-400",error:"border-l-4 border-red-400",warning:"border-l-4 border-yellow-400",info:"border-l-4 border-blue-400"};return v[h]||v.info}function u(h){const v={success:"h-8 w-8 rounded-full bg-green-100 text-green-600 flex items-center justify-center",error:"h-8 w-8 rounded-full bg-red-100 text-red-600 flex items-center justify-center",warning:"h-8 w-8 rounded-full bg-yellow-100 text-yellow-600 flex items-center justify-center",info:"h-8 w-8 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center"};return v[h]||v.info}function m(h){const v={success:"M5 13l4 4L19 7",error:"M6 18L18 6M6 6l12 12",warning:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-2.694-.833-3.464 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z",info:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"};return v[h]||v.info}return window.showNotification=l,Q(()=>{}),(h,v)=>(s(),r("div",wo,[C(Ie,{name:"notification",tag:"div",class:"space-y-4"},{default:A(()=>[(s(!0),r(N,null,U(a.value,d=>(s(),r("div",{key:d.id,class:S([n(d.type),"max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden"])},[e("div",$o,[e("div",Co,[e("div",Mo,[e("div",{class:S(u(d.type))},[(s(),r("svg",jo,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:m(d.type)},null,8,zo)]))],2)]),e("div",Po,[e("p",So,c(d.title),1),e("p",Ao,c(d.message),1)]),e("div",Eo,[e("button",{onClick:i=>t(d.id),class:"bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},v[0]||(v[0]=[e("span",{class:"sr-only"},"Chiudi",-1),e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,Vo)])])])],2))),128))]),_:1})]))}},To=Ae(Io,[["__scopeId","data-v-220f0827"]]),Do={class:"h-screen flex bg-gray-50 dark:bg-gray-900"},Lo={class:"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900"},Ho={class:"py-6"},qo={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},Bo={key:0,class:"mb-6"},Ro={key:1,class:"flex items-center justify-center h-64"},Oo={__name:"AppLayout",setup(o){const a=ge(),l=ee(),t=te(),n=j(!1),u=j(!1),m=j(!1);y(()=>t.config||{});const h=y(()=>t.config!==null),v=y(()=>{var P;return(P=a.meta)!=null&&P.title?a.meta.title:{dashboard:"Dashboard",projects:"Progetti","projects-list":"Elenco Progetti","projects-view":"Dettaglio Progetto","projects-create":"Nuovo Progetto",personnel:"Personale","personnel-directory":"Rubrica Aziendale","personnel-orgchart":"Organigramma","personnel-skills":"Competenze"}[a.name]||"DatPortal"}),d=y(()=>{var z;return(z=a.meta)!=null&&z.breadcrumbs?a.meta.breadcrumbs.map(P=>({label:P.label,to:P.to,icon:P.icon})):[]}),i=y(()=>{var z;return((z=a.meta)==null?void 0:z.hasActions)||!1});function f(){n.value=!n.value}function k(){n.value=!1}function w(z){u.value=z}function $(){l.push("/app/projects/create")}return le(a,()=>{m.value=!0,setTimeout(()=>{m.value=!1},300)}),le(a,()=>{k()}),Q(()=>{h.value||t.loadConfig()}),(z,P)=>{const b=R("router-view");return s(),r("div",Do,[n.value?(s(),r("div",{key:0,onClick:k,class:"fixed inset-0 z-20 bg-black bg-opacity-50 lg:hidden"})):_("",!0),C(cs,{"is-mobile-open":n.value,onClose:k,onToggleCollapsed:w},null,8,["is-mobile-open"]),e("div",{class:S(["flex flex-col flex-1 overflow-hidden transition-all duration-300",[u.value?"lg:ml-20":"lg:ml-64"]])},[C(ko,{"page-title":v.value,breadcrumbs:d.value,onToggleMobileSidebar:f,onQuickCreateProject:$},null,8,["page-title","breadcrumbs"]),e("main",Lo,[e("div",Ho,[e("div",qo,[i.value?(s(),r("div",Bo,[Te(z.$slots,"page-actions")])):_("",!0),m.value?(s(),r("div",Ro,[C(bo)])):(s(),q(b,{key:2}))])])])],2),C(To)])}}},No={class:"min-h-screen bg-gray-50"},Uo={class:"bg-white shadow-sm border-b"},Fo={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},Ko={class:"flex justify-between h-16"},Wo={class:"flex items-center"},Go={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center"},Qo={class:"text-white font-bold text-sm"},Jo={class:"text-xl font-semibold text-gray-900"},Yo={class:"hidden md:flex items-center space-x-8"},Xo={class:"flex items-center space-x-4 ml-8 pl-8 border-l border-gray-200"},Zo={class:"md:hidden flex items-center"},er={key:0,class:"md:hidden"},tr={class:"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t"},sr={class:"bg-gray-800 text-white"},or={class:"max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8"},rr={class:"grid grid-cols-1 md:grid-cols-4 gap-8"},ar={class:"col-span-1 md:col-span-2"},nr={class:"flex items-center space-x-3 mb-4"},ir={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center"},lr={class:"text-white font-bold text-sm"},dr={class:"text-xl font-semibold"},cr={class:"text-gray-300 max-w-md"},ur={class:"space-y-2"},mr={class:"space-y-2 text-gray-300"},pr={key:0},vr={key:1},gr={key:2},hr={class:"mt-8 pt-8 border-t border-gray-700 text-center text-gray-400"},ze={__name:"PublicLayout",setup(o){const a=te(),l=j(!1),t=y(()=>a.config||{}),n=y(()=>{var v;return((v=t.value.company)==null?void 0:v.name)||"DatVinci"}),u=y(()=>n.value.split(" ").map(d=>d[0]).join("").toUpperCase().slice(0,2)),m=y(()=>a.config!==null),h=new Date().getFullYear();return Q(()=>{m.value||a.loadConfig()}),(v,d)=>{var k,w,$,z,P,b;const i=R("router-link"),f=R("router-view");return s(),r("div",No,[e("nav",Uo,[e("div",Fo,[e("div",Ko,[e("div",Wo,[C(i,{to:"/",class:"flex items-center space-x-3"},{default:A(()=>[e("div",Go,[e("span",Qo,c(u.value),1)]),e("span",Jo,c(n.value),1)]),_:1})]),e("div",Yo,[C(i,{to:"/",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:A(()=>d[1]||(d[1]=[V(" Home ")])),_:1,__:[1]}),C(i,{to:"/about",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:A(()=>d[2]||(d[2]=[V(" Chi Siamo ")])),_:1,__:[2]}),C(i,{to:"/services",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:A(()=>d[3]||(d[3]=[V(" Servizi ")])),_:1,__:[3]}),C(i,{to:"/contact",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:A(()=>d[4]||(d[4]=[V(" Contatti ")])),_:1,__:[4]}),e("div",Xo,[C(i,{to:"/auth/login",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:A(()=>d[5]||(d[5]=[V(" Accedi ")])),_:1,__:[5]}),C(i,{to:"/auth/register",class:"bg-primary-600 text-white hover:bg-primary-700 px-4 py-2 rounded-md text-sm font-medium"},{default:A(()=>d[6]||(d[6]=[V(" Registrati ")])),_:1,__:[6]})])]),e("div",Zo,[e("button",{onClick:d[0]||(d[0]=H=>l.value=!l.value),class:"text-gray-400 hover:text-gray-500"},d[7]||(d[7]=[e("svg",{class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"})],-1)]))])])]),l.value?(s(),r("div",er,[e("div",tr,[C(i,{to:"/",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:A(()=>d[8]||(d[8]=[V(" Home ")])),_:1,__:[8]}),C(i,{to:"/about",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:A(()=>d[9]||(d[9]=[V(" Chi Siamo ")])),_:1,__:[9]}),C(i,{to:"/services",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:A(()=>d[10]||(d[10]=[V(" Servizi ")])),_:1,__:[10]}),C(i,{to:"/contact",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:A(()=>d[11]||(d[11]=[V(" Contatti ")])),_:1,__:[11]}),d[14]||(d[14]=e("hr",{class:"my-2"},null,-1)),C(i,{to:"/auth/login",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:A(()=>d[12]||(d[12]=[V(" Accedi ")])),_:1,__:[12]}),C(i,{to:"/auth/register",class:"block px-3 py-2 text-base font-medium bg-primary-600 text-white rounded-md"},{default:A(()=>d[13]||(d[13]=[V(" Registrati ")])),_:1,__:[13]})])])):_("",!0)]),e("main",null,[C(f)]),e("footer",sr,[e("div",or,[e("div",rr,[e("div",ar,[e("div",nr,[e("div",ir,[e("span",lr,c(u.value),1)]),e("span",dr,c(n.value),1)]),e("p",cr,c(O(a).interpolateText((k=t.value.footer)==null?void 0:k.description)||((w=t.value.company)==null?void 0:w.description)||"Innovazione e tecnologia per il futuro digitale della tua azienda."),1)]),e("div",null,[d[19]||(d[19]=e("h3",{class:"text-sm font-semibold uppercase tracking-wider mb-4"},"Link Rapidi",-1)),e("ul",ur,[e("li",null,[C(i,{to:"/",class:"text-gray-300 hover:text-white"},{default:A(()=>d[15]||(d[15]=[V("Home")])),_:1,__:[15]})]),e("li",null,[C(i,{to:"/about",class:"text-gray-300 hover:text-white"},{default:A(()=>d[16]||(d[16]=[V("Chi Siamo")])),_:1,__:[16]})]),e("li",null,[C(i,{to:"/services",class:"text-gray-300 hover:text-white"},{default:A(()=>d[17]||(d[17]=[V("Servizi")])),_:1,__:[17]})]),e("li",null,[C(i,{to:"/contact",class:"text-gray-300 hover:text-white"},{default:A(()=>d[18]||(d[18]=[V("Contatti")])),_:1,__:[18]})])])]),e("div",null,[d[20]||(d[20]=e("h3",{class:"text-sm font-semibold uppercase tracking-wider mb-4"},"Contatti",-1)),e("ul",mr,[($=t.value.contact)!=null&&$.email?(s(),r("li",pr,c(t.value.contact.email),1)):_("",!0),(z=t.value.contact)!=null&&z.phone?(s(),r("li",vr,c(t.value.contact.phone),1)):_("",!0),(P=t.value.contact)!=null&&P.address?(s(),r("li",gr,c(t.value.contact.address),1)):_("",!0)])])]),e("div",hr,[e("p",null,c(O(a).interpolateText((b=t.value.footer)==null?void 0:b.copyright)||`© ${O(h)} ${n.value}. Tutti i diritti riservati.`),1)])])])])}}},fr={class:"bg-white"},xr={class:"relative overflow-hidden"},yr={class:"max-w-7xl mx-auto"},_r={class:"relative z-10 pb-8 bg-white sm:pb-16 md:pb-20 lg:max-w-2xl lg:w-full lg:pb-28 xl:pb-32"},kr={class:"mt-10 mx-auto max-w-7xl px-4 sm:mt-12 sm:px-6 md:mt-16 lg:mt-20 lg:px-8 xl:mt-28"},br={class:"sm:text-center lg:text-left"},wr={class:"text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl"},$r={class:"block xl:inline"},Cr={class:"mt-3 text-base text-gray-500 sm:mt-5 sm:text-lg sm:max-w-xl sm:mx-auto md:mt-5 md:text-xl lg:mx-0"},Mr={class:"mt-5 sm:mt-8 sm:flex sm:justify-center lg:justify-start"},jr={class:"rounded-md shadow"},zr={class:"mt-3 sm:mt-0 sm:ml-3"},Pr={class:"py-12 bg-white"},Sr={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},Ar={class:"lg:text-center"},Er={class:"text-base text-primary-600 font-semibold tracking-wide uppercase"},Vr={class:"mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl"},Ir={key:0,class:"mt-10"},Tr={class:"space-y-10 md:space-y-0 md:grid md:grid-cols-2 md:gap-x-8 md:gap-y-10"},Dr={class:"absolute flex items-center justify-center h-12 w-12 rounded-md bg-primary-500 text-white"},Lr={class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Hr={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"},qr={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"},Br={key:2,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"},Rr={key:3,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"},Or={class:"ml-16 text-lg leading-6 font-medium text-gray-900"},Nr={class:"mt-2 ml-16 text-base text-gray-500"},Ur={__name:"Home",setup(o){const a=te(),l=y(()=>a.config||{}),t=y(()=>{var u;return((u=l.value.pages)==null?void 0:u.home)||{}}),n=y(()=>l.value.company||{});return Q(()=>{a.config||a.loadConfig()}),(u,m)=>{var v,d,i,f;const h=R("router-link");return s(),r("div",fr,[e("div",xr,[e("div",yr,[e("div",_r,[e("main",kr,[e("div",br,[e("h1",wr,[e("span",$r,c(((v=t.value.hero)==null?void 0:v.title)||"Innovazione per il futuro"),1)]),e("p",Cr,c(((d=t.value.hero)==null?void 0:d.subtitle)||O(a).interpolateText(n.value.description)||"Supportiamo le aziende nel loro percorso di crescita attraverso soluzioni tecnologiche all'avanguardia"),1),e("div",Mr,[e("div",jr,[C(h,{to:"/services",class:"w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 md:py-4 md:text-lg md:px-10"},{default:A(()=>{var k;return[V(c(((k=t.value.hero)==null?void 0:k.cta_primary)||"Scopri i nostri servizi"),1)]}),_:1})]),e("div",zr,[C(h,{to:"/contact",class:"w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 md:py-4 md:text-lg md:px-10"},{default:A(()=>{var k;return[V(c(((k=t.value.hero)==null?void 0:k.cta_secondary)||"Contattaci"),1)]}),_:1})])])])])])]),m[0]||(m[0]=e("div",{class:"lg:absolute lg:inset-y-0 lg:right-0 lg:w-1/2"},[e("div",{class:"h-56 w-full bg-gradient-to-r from-primary-400 to-primary-600 sm:h-72 md:h-96 lg:w-full lg:h-full flex items-center justify-center"},[e("svg",{class:"h-24 w-24 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})])])],-1))]),e("div",Pr,[e("div",Sr,[e("div",Ar,[e("h2",Er,c(((i=t.value.services_section)==null?void 0:i.title)||"I nostri servizi"),1),e("p",Vr,c(((f=t.value.services_section)==null?void 0:f.subtitle)||"Soluzioni innovative per ogni esigenza aziendale"),1)]),n.value.platform_features?(s(),r("div",Ir,[e("div",Tr,[(s(!0),r(N,null,U(n.value.platform_features,k=>(s(),r("div",{key:k.title,class:"relative"},[e("div",Dr,[(s(),r("svg",Lr,[k.icon==="briefcase"?(s(),r("path",Hr)):k.icon==="users"?(s(),r("path",qr)):k.icon==="chart"?(s(),r("path",Br)):(s(),r("path",Rr))]))]),e("p",Or,c(k.title),1),e("p",Nr,c(k.description),1)]))),128))])])):_("",!0)])])])}}},Fr={class:"py-16 bg-white"},Kr={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},Wr={class:"text-center"},Gr={class:"text-4xl font-extrabold text-gray-900 sm:text-5xl"},Qr={class:"mt-4 text-xl text-gray-600"},Jr={key:0,class:"mt-16"},Yr={class:"max-w-3xl mx-auto"},Xr={class:"text-3xl font-bold text-gray-900 text-center mb-8"},Zr={class:"text-lg text-gray-700 leading-relaxed"},ea={class:"mt-16 grid grid-cols-1 md:grid-cols-2 gap-12"},ta={key:0,class:"bg-gray-50 p-8 rounded-lg"},sa={class:"text-2xl font-bold text-gray-900 mb-4"},oa={class:"text-gray-700"},ra={key:1,class:"bg-gray-50 p-8 rounded-lg"},aa={class:"text-2xl font-bold text-gray-900 mb-4"},na={class:"text-gray-700"},ia={key:1,class:"mt-16"},la={class:"text-center mb-12"},da={class:"text-3xl font-bold text-gray-900"},ca={class:"mt-4 text-xl text-gray-600"},ua={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},ma={class:"text-lg font-semibold text-gray-900"},pa={key:2,class:"mt-16"},va={class:"text-center"},ga={class:"text-3xl font-bold text-gray-900"},ha={class:"mt-4 text-xl text-gray-600"},fa={class:"mt-8 inline-flex items-center px-6 py-3 bg-primary-50 rounded-lg"},xa={class:"text-primary-900 font-medium"},ya={__name:"About",setup(o){const a=te(),l=y(()=>a.config||{}),t=y(()=>{var u;return((u=l.value.pages)==null?void 0:u.about)||{}}),n=y(()=>l.value.company||{});return Q(()=>{a.config||a.loadConfig()}),(u,m)=>{var h,v;return s(),r("div",Fr,[e("div",Kr,[e("div",Wr,[e("h1",Gr,c(((h=t.value.hero)==null?void 0:h.title)||"Chi Siamo"),1),e("p",Qr,c(((v=t.value.hero)==null?void 0:v.subtitle)||"La nostra storia e i nostri valori"),1)]),t.value.story_section?(s(),r("div",Jr,[e("div",Yr,[e("h2",Xr,c(t.value.story_section.title),1),e("p",Zr,c(O(a).interpolateText(t.value.story_section.content)),1)])])):_("",!0),e("div",ea,[t.value.mission_section?(s(),r("div",ta,[e("h3",sa,c(t.value.mission_section.title),1),e("p",oa,c(O(a).interpolateText(t.value.mission_section.content)),1)])):_("",!0),t.value.vision_section?(s(),r("div",ra,[e("h3",aa,c(t.value.vision_section.title),1),e("p",na,c(O(a).interpolateText(t.value.vision_section.content)),1)])):_("",!0)]),t.value.expertise_section&&n.value.expertise?(s(),r("div",ia,[e("div",la,[e("h2",da,c(t.value.expertise_section.title),1),e("p",ca,c(t.value.expertise_section.subtitle),1)]),e("div",ua,[(s(!0),r(N,null,U(n.value.expertise,d=>(s(),r("div",{key:d,class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200 text-center"},[m[0]||(m[0]=e("div",{class:"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4"},[e("svg",{class:"w-6 h-6 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1)),e("h3",ma,c(d),1)]))),128))])])):_("",!0),t.value.team_section?(s(),r("div",pa,[e("div",va,[e("h2",ga,c(t.value.team_section.title),1),e("p",ha,c(t.value.team_section.subtitle),1),e("div",fa,[m[1]||(m[1]=e("svg",{class:"w-5 h-5 text-primary-600 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})],-1)),e("span",xa,c(n.value.team_size),1)])])])):_("",!0)])])}}},_a={class:"py-16 bg-white"},ka={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},ba={class:"text-center"},wa={class:"text-4xl font-extrabold text-gray-900 sm:text-5xl"},$a={class:"mt-4 text-xl text-gray-600"},Ca={key:0,class:"mt-8 text-center"},Ma={class:"text-lg text-gray-700 max-w-3xl mx-auto"},ja={class:"mt-16 grid grid-cols-1 lg:grid-cols-2 gap-16"},za={key:0},Pa={class:"text-2xl font-bold text-gray-900 mb-8"},Sa={class:"block text-sm font-medium text-gray-700 mb-2"},Aa={class:"block text-sm font-medium text-gray-700 mb-2"},Ea={class:"block text-sm font-medium text-gray-700 mb-2"},Va=["disabled"],Ia={key:1},Ta={class:"text-2xl font-bold text-gray-900 mb-8"},Da={class:"space-y-6"},La={key:0,class:"flex items-start"},Ha={class:"font-medium text-gray-900"},qa={class:"text-gray-600"},Ba={key:1,class:"flex items-start"},Ra={class:"font-medium text-gray-900"},Oa={class:"text-gray-600"},Na={key:2,class:"flex items-start"},Ua={class:"font-medium text-gray-900"},Fa={class:"text-gray-600"},Ka={key:3,class:"flex items-start"},Wa={class:"font-medium text-gray-900"},Ga={class:"text-gray-600"},Qa={__name:"Contact",setup(o){const a=te(),l=y(()=>a.config||{}),t=y(()=>{var d;return((d=l.value.pages)==null?void 0:d.contact)||{}}),n=y(()=>l.value.contact||{}),u=j({name:"",email:"",message:""}),m=j(!1),h=j({text:"",type:""}),v=async()=>{var d,i;if(!u.value.name||!u.value.email||!u.value.message){h.value={text:((d=t.value.form)==null?void 0:d.error_message)||"Tutti i campi sono obbligatori",type:"error"};return}m.value=!0,h.value={text:"",type:""};try{await new Promise(f=>setTimeout(f,1e3)),h.value={text:((i=t.value.form)==null?void 0:i.success_message)||"Messaggio inviato con successo!",type:"success"},u.value={name:"",email:"",message:""}}catch{h.value={text:"Errore durante l'invio. Riprova più tardi.",type:"error"}}finally{m.value=!1}};return Q(()=>{a.config||a.loadConfig()}),(d,i)=>{var f,k;return s(),r("div",_a,[e("div",ka,[e("div",ba,[e("h1",wa,c(((f=t.value.hero)==null?void 0:f.title)||"Contattaci"),1),e("p",$a,c(((k=t.value.hero)==null?void 0:k.subtitle)||"Siamo qui per aiutarti"),1)]),t.value.intro?(s(),r("div",Ca,[e("p",Ma,c(t.value.intro.content),1)])):_("",!0),e("div",ja,[t.value.form?(s(),r("div",za,[e("h2",Pa,c(t.value.form.title),1),e("form",{onSubmit:he(v,["prevent"]),class:"space-y-6"},[e("div",null,[e("label",Sa,c(t.value.form.name_label),1),K(e("input",{"onUpdate:modelValue":i[0]||(i[0]=w=>u.value.name=w),type:"text",required:"",class:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"},null,512),[[Z,u.value.name]])]),e("div",null,[e("label",Aa,c(t.value.form.email_label),1),K(e("input",{"onUpdate:modelValue":i[1]||(i[1]=w=>u.value.email=w),type:"email",required:"",class:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"},null,512),[[Z,u.value.email]])]),e("div",null,[e("label",Ea,c(t.value.form.message_label),1),K(e("textarea",{"onUpdate:modelValue":i[2]||(i[2]=w=>u.value.message=w),rows:"6",required:"",class:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"},null,512),[[Z,u.value.message]])]),e("button",{type:"submit",disabled:m.value,class:"w-full bg-primary-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-primary-700 disabled:opacity-50"},c(m.value?"Invio in corso...":t.value.form.submit_button),9,Va),h.value.text?(s(),r("div",{key:0,class:S([h.value.type==="success"?"text-green-600":"text-red-600","text-sm mt-2"])},c(h.value.text),3)):_("",!0)],32)])):_("",!0),t.value.info?(s(),r("div",Ia,[e("h2",Ta,c(t.value.info.title),1),e("div",Da,[n.value.address?(s(),r("div",La,[i[3]||(i[3]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})],-1)),e("div",null,[e("h3",Ha,c(t.value.info.address_label),1),e("p",qa,c(n.value.address),1)])])):_("",!0),n.value.phone?(s(),r("div",Ba,[i[4]||(i[4]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})],-1)),e("div",null,[e("h3",Ra,c(t.value.info.phone_label),1),e("p",Oa,c(n.value.phone),1)])])):_("",!0),n.value.email?(s(),r("div",Na,[i[5]||(i[5]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})],-1)),e("div",null,[e("h3",Ua,c(t.value.info.email_label),1),e("p",Fa,c(n.value.email),1)])])):_("",!0),n.value.hours?(s(),r("div",Ka,[i[6]||(i[6]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),e("div",null,[e("h3",Wa,c(t.value.info.hours_label),1),e("p",Ga,c(n.value.hours),1)])])):_("",!0)])])):_("",!0)])])])}}},Ja={class:"py-16 bg-white"},Ya={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},Xa={class:"text-center"},Za={class:"text-4xl font-extrabold text-gray-900 sm:text-5xl"},en={class:"mt-4 text-xl text-gray-600"},tn={key:0,class:"mt-8 text-center"},sn={class:"text-lg text-gray-700 max-w-3xl mx-auto"},on={key:1,class:"mt-16"},rn={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},an={class:"text-xl font-bold text-gray-900 text-center mb-4"},nn={class:"text-gray-600 text-center"},ln={key:2,class:"mt-20"},dn={class:"bg-primary-50 rounded-2xl p-12 text-center"},cn={class:"text-3xl font-bold text-gray-900 mb-4"},un={class:"text-xl text-gray-600 mb-8"},mn={__name:"Services",setup(o){const a=te(),l=y(()=>a.config||{}),t=y(()=>{var m;return((m=l.value.pages)==null?void 0:m.services)||{}}),n=y(()=>l.value.company||{}),u=m=>({"Sviluppo Software":"Soluzioni software personalizzate per ottimizzare i processi aziendali e migliorare l'efficienza operativa.","Intelligenza Artificiale":"Implementazione di sistemi AI avanzati per automatizzare processi e analizzare dati complessi.","Consulenza IT":"Consulenza strategica per la trasformazione digitale e l'ottimizzazione dell'infrastruttura tecnologica.","Gestione Progetti Innovativi":"Coordinamento e gestione di progetti tecnologici complessi con metodologie agili.","Supporto su Bandi e Finanziamenti":"Assistenza nella ricerca e gestione di bandi pubblici e finanziamenti per l'innovazione."})[m]||"Servizio professionale di alta qualità per supportare la crescita della tua azienda.";return Q(()=>{a.config||a.loadConfig()}),(m,h)=>{var d,i;const v=R("router-link");return s(),r("div",Ja,[e("div",Ya,[e("div",Xa,[e("h1",Za,c(((d=t.value.hero)==null?void 0:d.title)||"I nostri servizi"),1),e("p",en,c(((i=t.value.hero)==null?void 0:i.subtitle)||"Soluzioni complete per la tua azienda"),1)]),t.value.intro?(s(),r("div",tn,[e("p",sn,c(t.value.intro.content),1)])):_("",!0),n.value.expertise?(s(),r("div",on,[e("div",rn,[(s(!0),r(N,null,U(n.value.expertise,f=>(s(),r("div",{key:f,class:"bg-white p-8 rounded-lg shadow-lg border border-gray-200 hover:shadow-xl transition-shadow"},[h[0]||(h[0]=e("div",{class:"w-16 h-16 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-6"},[e("svg",{class:"w-8 h-8 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1)),e("h3",an,c(f),1),e("p",nn,c(u(f)),1)]))),128))])])):_("",!0),t.value.cta?(s(),r("div",ln,[e("div",dn,[e("h2",cn,c(t.value.cta.title),1),e("p",un,c(t.value.cta.subtitle),1),C(v,{to:"/contact",class:"inline-flex items-center px-8 py-4 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 transition-colors"},{default:A(()=>[V(c(t.value.cta.button)+" ",1),h[1]||(h[1]=e("svg",{class:"ml-2 w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 8l4 4m0 0l-4 4m4-4H3"})],-1))]),_:1,__:[1]})])])):_("",!0)])])}}},pn={class:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"},vn={class:"max-w-md w-full space-y-8"},gn={class:"mt-2 text-center text-sm text-gray-600"},hn={key:0,class:"rounded-md bg-red-50 p-4"},fn={class:"text-sm text-red-700"},xn={class:"rounded-md shadow-sm -space-y-px"},yn={class:"flex items-center justify-between"},_n={class:"flex items-center"},kn=["disabled"],bn={key:0,class:"absolute left-0 inset-y-0 flex items-center pl-3"},wn={__name:"Login",setup(o){const a=ee(),l=X(),t=j({username:"",password:"",remember:!1}),n=y(()=>l.loading),u=y(()=>l.error);async function m(){(await l.login({username:t.value.username,password:t.value.password,remember:t.value.remember})).success&&a.push("/app/dashboard")}return(h,v)=>{const d=R("router-link");return s(),r("div",pn,[e("div",vn,[e("div",null,[v[5]||(v[5]=e("div",{class:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-100"},[e("svg",{class:"h-6 w-6 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})])],-1)),v[6]||(v[6]=e("h2",{class:"mt-6 text-center text-3xl font-extrabold text-gray-900"}," Accedi al tuo account ",-1)),e("p",gn,[v[4]||(v[4]=V(" Oppure ")),C(d,{to:"/auth/register",class:"font-medium text-primary-600 hover:text-primary-500"},{default:A(()=>v[3]||(v[3]=[V(" registrati per un nuovo account ")])),_:1,__:[3]})])]),e("form",{onSubmit:he(m,["prevent"]),class:"mt-8 space-y-6"},[u.value?(s(),r("div",hn,[e("div",fn,c(u.value),1)])):_("",!0),e("div",xn,[e("div",null,[v[7]||(v[7]=e("label",{for:"username",class:"sr-only"},"Username",-1)),K(e("input",{id:"username","onUpdate:modelValue":v[0]||(v[0]=i=>t.value.username=i),name:"username",type:"text",autocomplete:"username",required:"",class:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm",placeholder:"Username"},null,512),[[Z,t.value.username]])]),e("div",null,[v[8]||(v[8]=e("label",{for:"password",class:"sr-only"},"Password",-1)),K(e("input",{id:"password","onUpdate:modelValue":v[1]||(v[1]=i=>t.value.password=i),name:"password",type:"password",autocomplete:"current-password",required:"",class:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm",placeholder:"Password"},null,512),[[Z,t.value.password]])])]),e("div",yn,[e("div",_n,[K(e("input",{id:"remember-me","onUpdate:modelValue":v[2]||(v[2]=i=>t.value.remember=i),name:"remember-me",type:"checkbox",class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,512),[[De,t.value.remember]]),v[9]||(v[9]=e("label",{for:"remember-me",class:"ml-2 block text-sm text-gray-900"}," Ricordami ",-1))]),v[10]||(v[10]=e("div",{class:"text-sm"},[e("a",{href:"#",class:"font-medium text-primary-600 hover:text-primary-500"}," Password dimenticata? ")],-1))]),e("div",null,[e("button",{type:"submit",disabled:n.value,class:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"},[n.value?(s(),r("span",bn,v[11]||(v[11]=[e("svg",{class:"h-5 w-5 text-primary-500 animate-spin",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1)]))):_("",!0),V(" "+c(n.value?"Accesso in corso...":"Accedi"),1)],8,kn)])],32)])])}}},$n={},Cn={class:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"};function Mn(o,a){return s(),r("div",Cn,a[0]||(a[0]=[e("div",{class:"max-w-md w-full space-y-8"},[e("div",null,[e("h2",{class:"mt-6 text-center text-3xl font-extrabold text-gray-900"}," Registra un nuovo account ")]),e("div",{class:"text-center text-gray-600"}," Registrazione in arrivo... ")],-1)]))}const jn=Ae($n,[["render",Mn]]),zn={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},Pn={class:"p-5"},Sn={class:"flex items-center"},An=["innerHTML"],En={class:"ml-5 w-0 flex-1"},Vn={class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"},In={class:"text-lg font-medium text-gray-900 dark:text-white"},Tn={key:0,class:"text-xs text-gray-500 dark:text-gray-400"},Dn={key:0,class:"bg-gray-50 dark:bg-gray-700 px-5 py-3"},Ln={class:"text-sm"},ie={__name:"StatsCard",props:{title:{type:String,required:!0},value:{type:[Number,String],required:!0},subtitle:{type:String,default:null},icon:{type:String,required:!0},color:{type:String,default:"primary"},link:{type:String,default:null}},setup(o){const a=t=>t==="project"?`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
      </svg>`:t==="users"?`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
      </svg>`:t==="clock"?`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`:t==="team"?`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
      </svg>`:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>`,l=t=>{const n={primary:"bg-primary-500",secondary:"bg-secondary-500",red:"bg-red-500",yellow:"bg-yellow-500",blue:"bg-blue-500",green:"bg-green-500"};return n[t]||n.primary};return(t,n)=>{const u=R("router-link");return s(),r("div",zn,[e("div",Pn,[e("div",Sn,[e("div",{class:S(["flex-shrink-0 rounded-md p-3",l(o.color)])},[e("div",{class:"h-6 w-6 text-white",innerHTML:a(o.icon)},null,8,An)],2),e("div",En,[e("dl",null,[e("dt",Vn,c(o.title),1),e("dd",null,[e("div",In,c(o.value),1),o.subtitle?(s(),r("div",Tn,c(o.subtitle),1)):_("",!0)])])])])]),o.link?(s(),r("div",Dn,[e("div",Ln,[C(u,{to:o.link,class:"font-medium text-primary-600 dark:text-primary-400 hover:text-primary-500 dark:hover:text-primary-300"},{default:A(()=>n[0]||(n[0]=[V(" Vedi tutti ")])),_:1,__:[0]},8,["to"])])])):_("",!0)])}}},Hn={class:"py-6"},qn={class:"flex flex-col md:flex-row md:items-center md:justify-between mb-8"},Bn={class:"mt-4 md:mt-0 flex space-x-3"},Rn={class:"relative"},On=["disabled"],Nn={class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"},Un={class:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8"},Fn={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Kn={class:"relative h-64"},Wn={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Gn={class:"relative h-64"},Qn={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},Jn={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Yn={class:"p-6"},Xn={key:0,class:"text-center py-8 text-gray-500"},Zn={key:1,class:"space-y-4"},ei={class:"flex justify-between items-start"},ti={class:"flex-1"},si={class:"text-sm font-medium text-gray-900 dark:text-white"},oi={class:"text-xs text-gray-500 dark:text-gray-400"},ri={class:"mt-2 flex justify-between items-center"},ai={class:"text-xs text-gray-500 dark:text-gray-400"},ni={class:"bg-gray-50 dark:bg-gray-700 px-6 py-3"},ii={class:"text-sm"},li={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},di={class:"p-6"},ci={key:0,class:"text-center py-8 text-gray-500"},ui={key:1,class:"space-y-4"},mi={class:"flex-shrink-0"},pi=["innerHTML"],vi={class:"flex-1 min-w-0"},gi={class:"text-sm font-medium text-gray-900 dark:text-white"},hi={class:"text-xs text-gray-500 dark:text-gray-400"},fi={class:"text-xs text-gray-400 dark:text-gray-500"},xi={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},yi={class:"p-6"},_i={key:0,class:"text-center py-8 text-gray-500"},ki={key:1,class:"space-y-4"},bi={class:"flex justify-between items-start"},wi={class:"flex-1"},$i={class:"text-sm font-medium text-gray-900 dark:text-white"},Ci={class:"text-xs text-gray-500 dark:text-gray-400"},Mi={class:"text-right"},ji={class:"text-sm font-bold text-gray-900 dark:text-white"},zi={class:"text-xs text-gray-500"},Pi={class:"mt-2"},Si={class:"w-full bg-gray-200 rounded-full h-2"},Ai={class:"text-xs text-gray-500 mt-1"},Ei={__name:"Dashboard",setup(o){ue.register(...Le),ee(),X();const a=j(!1),l=j("7"),t=j({}),n=j([]),u=j([]),m=j([]),h=j(null),v=j(null);let d=null,i=null;const f=async()=>{try{const p=await B.get("/api/dashboard/stats");t.value=p.data.data}catch(p){console.error("Error fetching dashboard stats:",p),t.value={}}},k=async()=>{try{const p=await B.get(`/api/dashboard/upcoming-tasks?days=${l.value}&limit=5`);n.value=p.data.data.tasks}catch(p){console.error("Error fetching upcoming tasks:",p),n.value=[]}},w=async()=>{try{const p=await B.get("/api/dashboard/recent-activities?limit=5");u.value=p.data.data.activities}catch(p){console.error("Error fetching recent activities:",p),u.value=[]}},$=async()=>{try{const p=await B.get("/api/dashboard/kpis?limit=3");m.value=p.data.data.kpis}catch(p){console.error("Error fetching KPIs:",p),m.value=[]}},z=async()=>{try{const p=await B.get("/api/dashboard/charts/project-status");b(p.data.data.chart)}catch(p){console.error("Error fetching project chart:",p)}},P=async()=>{try{const p=await B.get("/api/dashboard/charts/task-status");H(p.data.data.chart)}catch(p){console.error("Error fetching task chart:",p)}},b=p=>{if(!h.value)return;const g=h.value.getContext("2d");d&&d.destroy(),d=new ue(g,{type:"doughnut",data:{labels:p.labels,datasets:[{data:p.data,backgroundColor:["#3B82F6","#10B981","#F59E0B","#EF4444","#8B5CF6"],borderWidth:2,borderColor:"#ffffff"}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom",labels:{padding:20,usePointStyle:!0}}}}})},H=p=>{if(!v.value)return;const g=v.value.getContext("2d");i&&i.destroy(),i=new ue(g,{type:"bar",data:{labels:p.labels,datasets:[{label:"Tasks",data:p.data,backgroundColor:["#60A5FA","#34D399","#FBBF24","#F87171"],borderColor:["#3B82F6","#10B981","#F59E0B","#EF4444"],borderWidth:1}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1}},scales:{y:{beginAtZero:!0,ticks:{stepSize:1}}}}})},G=async()=>{a.value=!0;try{await Promise.all([f(),k(),w(),$(),z(),P()])}finally{a.value=!1}},se=p=>new Date(p).toLocaleDateString("it-IT"),oe=p=>{const g=new Date(p),F=Math.floor((new Date-g)/(1e3*60));return F<60?`${F} minuti fa`:F<1440?`${Math.floor(F/60)} ore fa`:`${Math.floor(F/1440)} giorni fa`},M=p=>{const g={high:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",medium:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",low:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"};return g[p]||g.medium},E=p=>{const g={todo:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300","in-progress":"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",review:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",done:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"};return g[p]||g.todo},L=p=>{const g={task:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
      </svg>`,timesheet:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`,event:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>`};return g[p]||g.task},T=p=>{const g={task:"bg-blue-100 text-blue-600",timesheet:"bg-green-100 text-green-600",event:"bg-purple-100 text-purple-600"};return g[p]||g.task},x=p=>p>=90?"bg-green-500":p>=70?"bg-yellow-500":"bg-red-500";return Q(async()=>{await G(),await Pe(),h.value&&v.value&&(await z(),await P())}),(p,g)=>{var F,re,ae,ye,_e,ke,be,we;const J=R("router-link");return s(),r("div",Hn,[e("div",qn,[g[4]||(g[4]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Dashboard"),e("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Benvenuto! Ecco una panoramica delle attività della tua azienda. ")],-1)),e("div",Bn,[e("div",Rn,[K(e("select",{"onUpdate:modelValue":g[0]||(g[0]=I=>l.value=I),onChange:G,class:"bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md py-2 pl-3 pr-10 text-sm font-medium text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500"},g[1]||(g[1]=[e("option",{value:"7"},"Ultimi 7 giorni",-1),e("option",{value:"30"},"Ultimo mese",-1),e("option",{value:"90"},"Ultimi 3 mesi",-1)]),544),[[pe,l.value]])]),e("button",{onClick:G,disabled:a.value,type:"button",class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},[(s(),r("svg",{xmlns:"http://www.w3.org/2000/svg",class:S(["h-4 w-4 mr-2",{"animate-spin":a.value}]),viewBox:"0 0 20 20",fill:"currentColor"},g[2]||(g[2]=[e("path",{"fill-rule":"evenodd",d:"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z","clip-rule":"evenodd"},null,-1)]),2)),g[3]||(g[3]=V(" Aggiorna "))],8,On)])]),e("div",Nn,[C(ie,{title:"Progetti Attivi",value:((F=t.value.projects)==null?void 0:F.active)||0,subtitle:`di ${((re=t.value.projects)==null?void 0:re.total)||0} totali`,icon:"project",color:"primary",link:"/app/projects?status=active"},null,8,["value","subtitle"]),C(ie,{title:"Clienti",value:((ae=t.value.team)==null?void 0:ae.clients)||0,icon:"users",color:"secondary",link:"/app/crm/clients"},null,8,["value"]),C(ie,{title:"Task Pendenti",value:((ye=t.value.tasks)==null?void 0:ye.pending)||0,subtitle:`${((_e=t.value.tasks)==null?void 0:_e.overdue)||0} in ritardo`,icon:"clock",color:((ke=t.value.tasks)==null?void 0:ke.overdue)>0?"red":"yellow",link:"/app/tasks?status=pending"},null,8,["value","subtitle","color"]),C(ie,{title:"Team Members",value:((be=t.value.team)==null?void 0:be.users)||0,subtitle:`${((we=t.value.team)==null?void 0:we.departments)||0} dipartimenti`,icon:"team",color:"blue",link:"/app/personnel"},null,8,["value","subtitle"])]),e("div",Un,[e("div",Fn,[g[5]||(g[5]=e("div",{class:"flex items-center justify-between mb-4"},[e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Stato Progetti")],-1)),e("div",Kn,[e("canvas",{ref_key:"projectChart",ref:h},null,512)])]),e("div",Wn,[g[6]||(g[6]=e("div",{class:"flex items-center justify-between mb-4"},[e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Stato Attività")],-1)),e("div",Gn,[e("canvas",{ref_key:"taskChart",ref:v},null,512)])])]),e("div",Qn,[e("div",Jn,[e("div",Yn,[g[7]||(g[7]=e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Attività in Scadenza",-1)),n.value.length===0?(s(),r("div",Xn," Nessuna attività in scadenza ")):(s(),r("div",Zn,[(s(!0),r(N,null,U(n.value,I=>(s(),r("div",{key:I.id,class:"border-b border-gray-200 dark:border-gray-700 pb-4 last:border-b-0 last:pb-0"},[e("div",ei,[e("div",ti,[e("h3",si,c(I.name),1),e("p",oi,c(I.project_name),1)]),e("span",{class:S(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",M(I.priority)])},c(I.priority),3)]),e("div",ri,[e("span",ai," Scadenza: "+c(se(I.due_date)),1),e("span",{class:S(["inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",E(I.status)])},c(I.status),3)])]))),128))]))]),e("div",ni,[e("div",ii,[C(J,{to:"/app/tasks",class:"font-medium text-primary-600 dark:text-primary-400 hover:text-primary-500"},{default:A(()=>g[8]||(g[8]=[V(" Vedi tutte le attività ")])),_:1,__:[8]})])])]),e("div",li,[e("div",di,[g[9]||(g[9]=e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Attività Recenti",-1)),u.value.length===0?(s(),r("div",ci," Nessuna attività recente ")):(s(),r("div",ui,[(s(!0),r(N,null,U(u.value,I=>(s(),r("div",{key:`${I.type}-${I.id}`,class:"flex items-start space-x-3"},[e("div",mi,[e("div",{class:S(["w-8 h-8 rounded-full flex items-center justify-center",T(I.type)])},[e("div",{class:"w-4 h-4",innerHTML:L(I.type)},null,8,pi)],2)]),e("div",vi,[e("p",gi,c(I.title),1),e("p",hi,c(I.description),1),e("p",fi,c(oe(I.timestamp)),1)])]))),128))]))])]),e("div",xi,[e("div",yi,[g[10]||(g[10]=e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"KPIs Principali",-1)),m.value.length===0?(s(),r("div",_i," Nessun KPI configurato ")):(s(),r("div",ki,[(s(!0),r(N,null,U(m.value,I=>(s(),r("div",{key:I.id,class:"border-b border-gray-200 dark:border-gray-700 pb-4 last:border-b-0 last:pb-0"},[e("div",bi,[e("div",wi,[e("h3",$i,c(I.name),1),e("p",Ci,c(I.description),1)]),e("div",Mi,[e("p",ji,c(I.current_value)+c(I.unit),1),e("p",zi," Target: "+c(I.target_value)+c(I.unit),1)])]),e("div",Pi,[e("div",Si,[e("div",{class:S(["h-2 rounded-full",x(I.performance_percentage)]),style:fe({width:Math.min(I.performance_percentage,100)+"%"})},null,6)]),e("p",Ai,c(Math.round(I.performance_percentage))+"% del target",1)])]))),128))]))])])])])}}},Vi=ve("projects",()=>{const o=j([]),a=j(null),l=j(!1),t=j(null),n=j(new Map),u=j({page:1,perPage:20,total:0,totalPages:0}),m=j({search:"",status:"",client:"",type:""}),h=y(()=>{let M=o.value;if(m.value.search){const E=m.value.search.toLowerCase();M=M.filter(L=>{var T,x,p;return L.name.toLowerCase().includes(E)||((T=L.description)==null?void 0:T.toLowerCase().includes(E))||((p=(x=L.client)==null?void 0:x.name)==null?void 0:p.toLowerCase().includes(E))})}return m.value.status&&(M=M.filter(E=>E.status===m.value.status)),m.value.client&&(M=M.filter(E=>E.client_id===m.value.client)),m.value.type&&(M=M.filter(E=>E.project_type===m.value.type)),M}),v=y(()=>{const M={};return o.value.forEach(E=>{M[E.status]||(M[E.status]=[]),M[E.status].push(E)}),M}),d=async(M={})=>{var E,L;l.value=!0,t.value=null;try{const T=new URLSearchParams({page:M.page||u.value.page,per_page:M.perPage||u.value.perPage,search:M.search||m.value.search,status:M.status||m.value.status,client:M.client||m.value.client,type:M.type||m.value.type}),x=await B.get(`/api/projects?${T}`);x.data.success&&(x.data.data.items?(o.value=x.data.data.items,u.value=x.data.data.pagination):x.data.data.projects?(o.value=x.data.data.projects,u.value=x.data.data.pagination):Array.isArray(x.data.data)?(o.value=x.data.data,u.value={total:x.data.data.length,page:1,per_page:x.data.data.length}):(o.value=[],u.value={total:0,page:1,per_page:10}))}catch(T){t.value=((L=(E=T.response)==null?void 0:E.data)==null?void 0:L.message)||"Errore nel caricamento progetti",console.error("Error fetching projects:",T)}finally{l.value=!1}},i=async(M,E=!1)=>{var L,T;if(!E&&n.value.has(M)){const x=n.value.get(M);return a.value=x,x}l.value=!0,t.value=null;try{const x=await B.get(`/api/projects/${M}`);if(x.data.success){const p=x.data.data.project;return a.value=p,n.value.set(M,p),p}}catch(x){throw t.value=((T=(L=x.response)==null?void 0:L.data)==null?void 0:T.message)||"Errore nel caricamento progetto",console.error("Error fetching project:",x),x}finally{l.value=!1}};return{projects:o,currentProject:a,loading:l,error:t,pagination:u,filters:m,filteredProjects:h,projectsByStatus:v,fetchProjects:d,fetchProject:i,createProject:async M=>{var E,L;l.value=!0,t.value=null;try{const T=await B.post("/api/projects",M);if(T.data.success){const x=T.data.data.project;return o.value.unshift(x),x}}catch(T){throw t.value=((L=(E=T.response)==null?void 0:E.data)==null?void 0:L.message)||"Errore nella creazione progetto",console.error("Error creating project:",T),T}finally{l.value=!1}},updateProject:async(M,E)=>{var L,T,x;l.value=!0,t.value=null;try{const p=await B.put(`/api/projects/${M}`,E);if(p.data.success){const g=p.data.data.project,J=o.value.findIndex(F=>F.id===M);return J!==-1&&(o.value[J]=g),((L=a.value)==null?void 0:L.id)===M&&(a.value=g),n.value.set(M,g),g}}catch(p){throw t.value=((x=(T=p.response)==null?void 0:T.data)==null?void 0:x.message)||"Errore nell'aggiornamento progetto",console.error("Error updating project:",p),p}finally{l.value=!1}},deleteProject:async M=>{var E,L,T;l.value=!0,t.value=null;try{(await B.delete(`/api/projects/${M}`)).data.success&&(o.value=o.value.filter(p=>p.id!==M),((E=a.value)==null?void 0:E.id)===M&&(a.value=null),n.value.delete(M))}catch(x){throw t.value=((T=(L=x.response)==null?void 0:L.data)==null?void 0:T.message)||"Errore nell'eliminazione progetto",console.error("Error deleting project:",x),x}finally{l.value=!1}},setFilters:M=>{m.value={...m.value,...M}},clearFilters:()=>{m.value={search:"",status:"",client:"",type:""}},setCurrentProject:M=>{a.value=M},clearCurrentProject:()=>{a.value=null},clearCache:()=>{n.value.clear()},refreshProject:async M=>await i(M,!0),getCachedProject:M=>n.value.get(M),$reset:()=>{o.value=[],a.value=null,l.value=!1,t.value=null,n.value.clear(),u.value={page:1,perPage:20,total:0,totalPages:0},m.value={search:"",status:"",client:"",type:""}}}}),Ii={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6"},Ti={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},Di=["value"],Li={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},Hi={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},qi={class:"text-lg font-medium text-gray-900 dark:text-white"},Bi={key:0,class:"p-6 text-center"},Ri={key:1,class:"p-6 text-center"},Oi={key:2,class:"divide-y divide-gray-200 dark:divide-gray-700"},Ni=["onClick"],Ui={class:"flex items-center justify-between"},Fi={class:"flex-1"},Ki={class:"flex items-center"},Wi={class:"text-lg font-medium text-gray-900 dark:text-white"},Gi={class:"mt-1 text-sm text-gray-600 dark:text-gray-400"},Qi={class:"mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400"},Ji={key:0},Yi={key:1,class:"mx-2"},Xi={key:2},Zi={key:3,class:"mx-2"},el={key:4},tl={key:0,class:"mt-3 flex items-center space-x-4"},sl={key:0,class:"flex items-center space-x-1"},ol={class:"text-xs text-gray-600 dark:text-gray-400"},rl={key:1,class:"flex items-center space-x-1"},al={class:"text-xs text-gray-600 dark:text-gray-400"},nl={key:2,class:"flex items-center space-x-1"},il={class:"text-xs text-gray-600 dark:text-gray-400"},ll={class:"ml-4 flex items-center space-x-4"},dl={class:"text-right"},cl={class:"text-sm font-medium text-gray-900 dark:text-white"},ul={class:"w-20 bg-gray-200 dark:bg-gray-600 rounded-full h-2 mt-1"},ml={key:0,class:"text-right"},pl={class:"flex items-center space-x-1 mt-1"},vl={__name:"Projects",setup(o){const a=ee(),l=Vi(),t=j(!0),n=j(""),u=j({status:"",client:""}),m=y(()=>l.projects),h=j([]),v=y(()=>{let x=m.value;if(u.value.status&&(x=x.filter(p=>p.status===u.value.status)),u.value.client&&(x=x.filter(p=>p.client_id==u.value.client)),n.value){const p=n.value.toLowerCase();x=x.filter(g=>g.name.toLowerCase().includes(p)||g.description&&g.description.toLowerCase().includes(p)||g.client&&g.client.name&&g.client.name.toLowerCase().includes(p))}return x}),d=async()=>{t.value=!0;try{await l.fetchProjects(),h.value=[]}catch(x){console.error("Error loading projects:",x)}finally{t.value=!1}},i=()=>{},f=()=>{},k=()=>{u.value={status:"",client:""},n.value=""},w=()=>{a.push("/app/projects/create")},$=x=>{a.push(`/app/projects/${x}`)},z=x=>({planning:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",active:"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",completed:"bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400","on-hold":"bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400"})[x]||"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400",P=x=>({planning:"Pianificazione",active:"Attivo",completed:"Completato","on-hold":"In Pausa"})[x]||x,b=x=>new Date(x).toLocaleDateString("it-IT"),H=x=>new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(x),G=x=>({planning:10,active:50,completed:100,"on-hold":25})[x.status]||0,se=x=>x>=90?"bg-red-500":x>=75?"bg-yellow-500":"bg-green-500",oe=x=>x>=90?"bg-red-500":x>=80?"bg-yellow-500":"bg-green-500",M=x=>x<10?"bg-red-500":x<20?"bg-yellow-500":"bg-green-500",E=x=>{const p=x.budget_usage>=90,g=x.time_usage>=90,J=x.margin<10;if(p||g||J)return"bg-red-500";const F=x.budget_usage>=75,re=x.time_usage>=80,ae=x.margin<20;return F||re||ae?"bg-yellow-500":"bg-green-500"},L=x=>{const p=E(x);return p.includes("red")?"text-red-600 dark:text-red-400":p.includes("yellow")?"text-yellow-600 dark:text-yellow-400":"text-green-600 dark:text-green-400"},T=x=>{const p=E(x);return p.includes("red")?"Critico":p.includes("yellow")?"Attenzione":"Buono"};return Q(()=>{d()}),(x,p)=>(s(),r("div",null,[e("div",{class:"mb-6"},[e("div",{class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},[p[4]||(p[4]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Progetti"),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Gestisci e monitora tutti i progetti aziendali ")],-1)),e("div",{class:"mt-4 sm:mt-0"},[e("button",{onClick:w,class:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},p[3]||(p[3]=[e("svg",{class:"h-4 w-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4v16m8-8H4"})],-1),V(" Nuovo Progetto ")]))])])]),e("div",Ii,[e("div",Ti,[e("div",null,[p[6]||(p[6]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Stato",-1)),K(e("select",{"onUpdate:modelValue":p[0]||(p[0]=g=>u.value.status=g),onChange:f,class:"w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"},p[5]||(p[5]=[He('<option value="">Tutti gli stati</option><option value="planning">Pianificazione</option><option value="active">Attivo</option><option value="completed">Completato</option><option value="on-hold">In Pausa</option>',5)]),544),[[pe,u.value.status]])]),e("div",null,[p[8]||(p[8]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Cliente",-1)),K(e("select",{"onUpdate:modelValue":p[1]||(p[1]=g=>u.value.client=g),onChange:f,class:"w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"},[p[7]||(p[7]=e("option",{value:""},"Tutti i clienti",-1)),(s(!0),r(N,null,U(h.value,g=>(s(),r("option",{key:g.id,value:g.id},c(g.name),9,Di))),128))],544),[[pe,u.value.client]])]),e("div",null,[p[9]||(p[9]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Ricerca",-1)),K(e("input",{"onUpdate:modelValue":p[2]||(p[2]=g=>n.value=g),onInput:i,type:"text",placeholder:"Cerca progetti...",class:"w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"},null,544),[[Z,n.value]])]),e("div",{class:"flex items-end"},[e("button",{onClick:k,class:"w-full px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"}," Reset Filtri ")])])]),e("div",Li,[e("div",Hi,[e("h3",qi," Progetti ("+c(v.value.length)+") ",1)]),t.value?(s(),r("div",Bi,p[10]||(p[10]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto"},null,-1),e("p",{class:"mt-2 text-gray-600 dark:text-gray-400"},"Caricamento progetti...",-1)]))):v.value.length===0?(s(),r("div",Ri,p[11]||(p[11]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun progetto",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"Inizia creando il tuo primo progetto.",-1)]))):(s(),r("div",Oi,[(s(!0),r(N,null,U(v.value,g=>(s(),r("div",{key:g.id,class:"p-6 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer",onClick:J=>$(g.id)},[e("div",Ui,[e("div",Fi,[e("div",Ki,[e("h4",Wi,c(g.name),1),e("span",{class:S([z(g.status),"ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},c(P(g.status)),3)]),e("p",Gi,c(g.description),1),e("div",Qi,[g.client?(s(),r("span",Ji,"Cliente: "+c(g.client.name),1)):_("",!0),g.client?(s(),r("span",Yi,"•")):_("",!0),g.end_date?(s(),r("span",Xi,"Scadenza: "+c(b(g.end_date)),1)):_("",!0),g.end_date&&g.budget?(s(),r("span",Zi,"•")):_("",!0),g.budget?(s(),r("span",el,"Budget: "+c(H(g.budget)),1)):_("",!0)]),g.kpis?(s(),r("div",tl,[g.kpis.budget_usage!==void 0?(s(),r("div",sl,[e("div",{class:S(["w-3 h-3 rounded-full",se(g.kpis.budget_usage)])},null,2),e("span",ol," Budget: "+c(g.kpis.budget_usage)+"% ",1)])):_("",!0),g.kpis.time_usage!==void 0?(s(),r("div",rl,[e("div",{class:S(["w-3 h-3 rounded-full",oe(g.kpis.time_usage)])},null,2),e("span",al," Tempo: "+c(g.kpis.time_usage)+"% ",1)])):_("",!0),g.kpis.margin!==void 0?(s(),r("div",nl,[e("div",{class:S(["w-3 h-3 rounded-full",M(g.kpis.margin)])},null,2),e("span",il," Margine: "+c(g.kpis.margin)+"% ",1)])):_("",!0)])):_("",!0)]),e("div",ll,[e("div",dl,[e("div",cl,c(G(g))+"% ",1),e("div",ul,[e("div",{class:"bg-primary-600 h-2 rounded-full",style:fe({width:G(g)+"%"})},null,4)])]),g.kpis?(s(),r("div",ml,[p[12]||(p[12]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"KPI Status",-1)),e("div",pl,[e("div",{class:S(["w-2 h-2 rounded-full",E(g.kpis)])},null,2),e("span",{class:S(["text-xs font-medium",L(g.kpis)])},c(T(g.kpis)),3)])])):_("",!0),p[13]||(p[13]=e("svg",{class:"h-5 w-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1))])])],8,Ni))),128))]))])]))}},gl=[{path:"/",component:ze,children:[{path:"",name:"home",component:Ur},{path:"about",name:"about",component:ya},{path:"contact",name:"contact",component:Qa},{path:"services",name:"services",component:mn}]},{path:"/auth",component:ze,children:[{path:"login",name:"login",component:wn},{path:"register",name:"register",component:jn}]},{path:"/app",component:Oo,meta:{requiresAuth:!0},children:[{path:"",redirect:"/app/dashboard"},{path:"dashboard",name:"dashboard",component:Ei,meta:{requiresAuth:!0,requiredPermission:"view_dashboard"}},{path:"projects",name:"projects",component:vl,meta:{requiresAuth:!0,requiredPermission:"view_all_projects"}},{path:"projects/create",name:"projects-create",component:()=>D(()=>import("./ProjectCreate.js"),__vite__mapDeps([0,1])),meta:{requiresAuth:!0,requiredPermission:"edit_project"}},{path:"projects/:id",name:"project-view",component:()=>D(()=>import("./ProjectView.js"),__vite__mapDeps([2,1,3])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects"}},{path:"projects/:id/edit",name:"project-edit",component:()=>D(()=>import("./ProjectEdit.js"),__vite__mapDeps([4,1])),meta:{requiresAuth:!0,requiredPermission:"edit_project"}},{path:"timesheet",redirect:"/app/timesheet/entry"},{path:"timesheet/entry",name:"timesheet-entry",component:()=>D(()=>import("./TimesheetEntry.js"),__vite__mapDeps([5,1])),meta:{requiresAuth:!0,requiredPermission:"manage_timesheets"}},{path:"timesheet/requests",name:"timesheet-requests",component:()=>D(()=>import("./TimesheetRequests.js"),__vite__mapDeps([6,1])),meta:{requiresAuth:!0,requiredPermission:"manage_timesheets"}},{path:"timesheet/dashboard",name:"timesheet-dashboard",component:()=>D(()=>import("./TimesheetDashboard.js"),__vite__mapDeps([7,1])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects"}},{path:"timesheet/history",name:"timesheet-history",component:()=>D(()=>import("./TimesheetHistory.js"),__vite__mapDeps([8,1])),meta:{requiresAuth:!0,requiredPermission:"manage_timesheets"}},{path:"timesheet/approvals",name:"timesheet-approvals",component:()=>D(()=>import("./TimesheetApprovals.js"),__vite__mapDeps([9,1])),meta:{requiresAuth:!0,requiredPermission:"manage_users"}},{path:"timesheet/analytics",name:"timesheet-analytics",component:()=>D(()=>import("./TimesheetAnalytics.js"),__vite__mapDeps([10,1])),meta:{requiresAuth:!0,requiredPermission:"admin_access"}},{path:"personnel",name:"personnel",component:()=>D(()=>import("./PersonnelDirectory.js"),__vite__mapDeps([11,1,12])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"personnel/orgchart",name:"personnel-orgchart",component:()=>D(()=>import("./PersonnelOrgChart.js"),__vite__mapDeps([13,1,14])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"personnel/skills",name:"personnel-skills",component:()=>D(()=>import("./SkillsMatrix.js"),__vite__mapDeps([15,1,16])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"personnel/departments",name:"personnel-departments",component:()=>D(()=>import("./DepartmentList.js"),__vite__mapDeps([17,1,12])),meta:{requiresAuth:!0,requiredPermission:"manage_users"}},{path:"personnel/departments/create",name:"department-create",component:()=>D(()=>import("./DepartmentCreate.js"),__vite__mapDeps([18,1])),meta:{requiresAuth:!0,requiredPermission:"manage_users"}},{path:"personnel/departments/:id",name:"department-view",component:()=>D(()=>import("./DepartmentView.js"),__vite__mapDeps([19,1,12])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"personnel/departments/:id/edit",name:"department-edit",component:()=>D(()=>import("./DepartmentEdit.js"),__vite__mapDeps([20,1])),meta:{requiresAuth:!0,requiredPermission:"manage_users"}},{path:"personnel/allocation",name:"personnel-allocation",component:()=>D(()=>import("./PersonnelAllocation.js"),__vite__mapDeps([21,1])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"personnel/admin",name:"personnel-admin",component:()=>D(()=>import("./PersonnelAdmin.js"),__vite__mapDeps([22,1,23])),meta:{requiresAuth:!0,requiredPermission:"admin_access"}},{path:"personnel/:id",name:"personnel-profile",component:()=>D(()=>import("./PersonnelProfile.js"),__vite__mapDeps([24,1,12,25])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"admin",redirect:"/app/admin/users"},{path:"admin/users",name:"admin-users",component:()=>D(()=>import("./Admin.js"),__vite__mapDeps([26,1])),meta:{requiresAuth:!0,requiredPermission:"admin_access"}},{path:"admin/kpi-templates",name:"admin-kpi-templates",component:()=>D(()=>import("./KPITemplates.js"),__vite__mapDeps([27,1])),meta:{requiresAuth:!0,requiredPermission:"admin_access"}},{path:"profile",name:"profile",component:()=>D(()=>import("./Profile.js"),__vite__mapDeps([28,1])),meta:{requiresAuth:!0}},{path:"settings",name:"settings",component:()=>D(()=>import("./Settings.js"),__vite__mapDeps([29,1])),meta:{requiresAuth:!0}}]}],Ee=qe({history:Be(),routes:gl});Ee.beforeEach(async(o,a,l)=>{const t=X();if(o.meta.requiresAuth){if(t.sessionChecked||await t.initializeAuth(),!t.isAuthenticated){l("/auth/login");return}if(o.meta.requiredPermission&&!t.hasPermission(o.meta.requiredPermission)){console.warn(`Accesso negato a ${o.path}: permesso '${o.meta.requiredPermission}' richiesto`),l("/app/dashboard");return}}l()});const ce=Re(Fe),hl=Oe();ce.use(hl);ce.use(Ee);const fl=X();fl.initializeAuth().then(()=>{console.log("Auth initialized successfully"),ce.mount("#app")}).catch(o=>{console.error("Auth initialization failed:",o),ce.mount("#app")});export{Ae as _,Ge as a,Vi as b,B as c,xe as d,X as u};

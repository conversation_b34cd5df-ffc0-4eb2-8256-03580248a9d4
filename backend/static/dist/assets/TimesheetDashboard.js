import{u as $}from"./app.js";import{r as h,f as E,A as N,c as i,j as t,a as b,g as k,m as f,t as a,i as j,b as O,F as A,k as S,n as P,o as n}from"./vendor.js";const X={class:"space-y-6"},I={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},L={class:"flex justify-between items-center"},Q={class:"flex space-x-3"},q=["disabled"],J={key:0,class:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",fill:"none",viewBox:"0 0 24 24"},U={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},Y={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},G={class:"flex items-center"},K={class:"ml-5 w-0 flex-1"},W={class:"text-lg font-medium text-gray-900 dark:text-white"},Z={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},tt={class:"flex items-center"},et={class:"ml-5 w-0 flex-1"},st={class:"text-lg font-medium text-gray-900 dark:text-white"},rt={key:0,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},at={class:"flex items-center"},ot={class:"ml-5 w-0 flex-1"},it={class:"text-lg font-medium text-gray-900 dark:text-white"},nt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},dt={class:"flex items-center"},lt={class:"ml-5 w-0 flex-1"},ct={class:"text-lg font-medium text-gray-900 dark:text-white"},ut={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},mt={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},xt={class:"p-6"},gt={class:"space-y-4"},pt={class:"flex-1 min-w-0"},ht={class:"text-sm font-medium text-gray-900 dark:text-white"},yt={class:"text-sm text-gray-500 dark:text-gray-400"},vt={class:"flex-shrink-0"},kt={class:"text-sm font-medium text-primary-600 dark:text-primary-400"},ft={key:0,class:"text-center py-4"},wt={key:0,class:"bg-white dark:bg-gray-800 shadow rounded-lg"},_t={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},bt={class:"flex justify-between items-center"},jt={class:"p-6"},Ct={class:"space-y-4"},Tt={class:"flex items-center space-x-3"},At={class:"text-sm font-medium text-gray-900 dark:text-white"},St={class:"text-sm text-gray-500 dark:text-gray-400"},Ht={class:"flex space-x-2"},zt=["onClick"],Dt=["onClick"],Mt={key:0,class:"text-center py-4"},Bt={key:1,class:"bg-white dark:bg-gray-800 shadow rounded-lg"},Ft={class:"p-6"},Rt={class:"space-y-4"},Vt={class:"flex justify-between items-center"},$t={class:"flex justify-between items-center"},Et={class:"text-sm font-medium text-gray-900 dark:text-white"},Nt={class:"flex justify-between items-center"},Ot={class:"text-sm font-medium text-gray-900 dark:text-white"},Pt={class:"mt-4 pt-4 border-t border-gray-200 dark:border-gray-600"},Qt={__name:"TimesheetDashboard",setup(Xt){const c=$(),x=h(!1),g=h({weeklyHours:0,monthlyHours:0,pendingApprovals:0,efficiency:0}),y=h([]),w=h([]),p=h({status:"draft",totalHours:0,billableHours:0}),v=E(()=>{var s,e;return((s=c.user)==null?void 0:s.role)==="manager"||((e=c.user)==null?void 0:e.role)==="admin"}),H=async()=>{var s,e,o,r,l,d;try{const m=await fetch("/api/dashboard/stats",{headers:{"Content-Type":"application/json","X-CSRFToken":c.csrfToken}});if(m.ok){const _=await m.json();g.value={weeklyHours:((e=(s=_.data)==null?void 0:s.activities)==null?void 0:e.recent_timesheets)||0,monthlyHours:((r=(o=_.data)==null?void 0:o.activities)==null?void 0:r.recent_timesheets)||0,pendingApprovals:((d=(l=_.data)==null?void 0:l.activities)==null?void 0:d.unread_notifications)||0,efficiency:85}}}catch(m){console.error("Error loading dashboard stats:",m)}},z=async()=>{try{const s=await fetch("/api/timesheets/?per_page=5&page=1",{headers:{"Content-Type":"application/json","X-CSRFToken":c.csrfToken}});if(s.ok){const o=(await s.json()).data||[];y.value=o.map(r=>({id:r.id,description:`${r.project_name||"Progetto"} - ${r.task_name||"Task"}`,hours:r.hours,created_at:r.created_at,date:r.date}))}}catch(s){console.error("Error loading recent activities:",s),y.value=[]}},C=async()=>{if(v.value)try{const s=await fetch("/api/monthly-timesheets/?status=submitted",{headers:{"Content-Type":"application/json","X-CSRFToken":c.csrfToken}});if(s.ok){const e=await s.json();w.value=e.data||[]}}catch(s){console.error("Error loading pending approvals:",s)}},D=async()=>{var s,e,o;if(!v.value)try{const r=new Date,l=await fetch("/api/monthly-timesheets/generate",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":c.csrfToken},body:JSON.stringify({year:r.getFullYear(),month:r.getMonth()+1})});if(l.ok){const d=await l.json();p.value={status:((s=d.data)==null?void 0:s.status)||"draft",totalHours:((e=d.data)==null?void 0:e.total_hours)||0,billableHours:((o=d.data)==null?void 0:o.billable_hours)||0}}}catch(r){console.error("Error loading my status:",r)}},T=async()=>{x.value=!0;try{await Promise.all([H(),z(),C(),D()])}finally{x.value=!1}},M=async s=>{try{(await fetch(`/api/monthly-timesheets/${s}/approve`,{method:"PUT",headers:{"Content-Type":"application/json","X-CSRFToken":c.csrfToken}})).ok&&await C()}catch(e){console.error("Error approving timesheet:",e)}},B=s=>{window.location.href=`/app/timesheet/approvals?timesheet=${s.id}`},u=s=>!s||s===0?"0h":s%1===0?`${s}h`:`${s.toFixed(1)}h`,F=s=>new Date(s).toLocaleDateString("it-IT"),R=s=>{switch(s){case"approved":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";case"submitted":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";case"rejected":return"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"}},V=s=>{switch(s){case"approved":return"Approvato";case"submitted":return"In Attesa";case"rejected":return"Rifiutato";default:return"Bozza"}};return N(()=>{T()}),(s,e)=>{const o=O("router-link");return n(),i("div",X,[t("div",I,[t("div",L,[e[2]||(e[2]=t("div",null,[t("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Dashboard Timesheet"),t("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Panoramica ore e approvazioni del team ")],-1)),t("div",Q,[t("button",{onClick:T,disabled:x.value,class:"bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium disabled:opacity-50"},[x.value?(n(),i("svg",J,e[0]||(e[0]=[t("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),t("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):k("",!0),f(" "+a(x.value?"Aggiornamento...":"Aggiorna"),1)],8,q),b(o,{to:"/app/timesheet/entry",class:"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium"},{default:j(()=>e[1]||(e[1]=[f(" Registra Ore ")])),_:1,__:[1]})])])]),t("div",U,[t("div",Y,[t("div",G,[e[4]||(e[4]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",K,[t("dl",null,[e[3]||(e[3]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Ore Questa Settimana ",-1)),t("dd",W,a(u(g.value.weeklyHours)),1)])])])]),t("div",Z,[t("div",tt,[e[6]||(e[6]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])])],-1)),t("div",et,[t("dl",null,[e[5]||(e[5]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Ore Questo Mese ",-1)),t("dd",st,a(u(g.value.monthlyHours)),1)])])])]),v.value?(n(),i("div",rt,[t("div",at,[e[8]||(e[8]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",ot,[t("dl",null,[e[7]||(e[7]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Da Approvare ",-1)),t("dd",it,a(g.value.pendingApprovals),1)])])])])):k("",!0),t("div",nt,[t("div",dt,[e[10]||(e[10]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})])])],-1)),t("div",lt,[t("dl",null,[e[9]||(e[9]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Efficienza ",-1)),t("dd",ct,a(g.value.efficiency)+"% ",1)])])])])]),t("div",ut,[t("div",mt,[e[13]||(e[13]=t("div",{class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},[t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Attività Recenti ")],-1)),t("div",xt,[t("div",gt,[(n(!0),i(A,null,S(y.value,r=>(n(),i("div",{key:r.id,class:"flex items-center space-x-3"},[e[11]||(e[11]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center"},[t("svg",{class:"w-4 h-4 text-gray-600 dark:text-gray-300",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",pt,[t("p",ht,a(r.description),1),t("p",yt,a(F(r.created_at)),1)]),t("div",vt,[t("span",kt,a(u(r.hours)),1)])]))),128))]),y.value.length===0?(n(),i("div",ft,e[12]||(e[12]=[t("p",{class:"text-gray-500 dark:text-gray-400"},"Nessuna attività recente",-1)]))):k("",!0)])]),v.value?(n(),i("div",wt,[t("div",_t,[t("div",bt,[e[15]||(e[15]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Approvazioni in Attesa ",-1)),b(o,{to:"/app/timesheet/approvals",class:"text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 text-sm"},{default:j(()=>e[14]||(e[14]=[f(" Vedi Tutte → ")])),_:1,__:[14]})])]),t("div",jt,[t("div",Ct,[(n(!0),i(A,null,S(w.value,r=>{var l,d;return n(),i("div",{key:r.id,class:"flex items-center justify-between"},[t("div",Tt,[e[16]||(e[16]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-yellow-100 dark:bg-yellow-900 rounded-full flex items-center justify-center"},[t("svg",{class:"w-4 h-4 text-yellow-600 dark:text-yellow-300",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",null,[t("p",At,a((l=r.user)==null?void 0:l.first_name)+" "+a((d=r.user)==null?void 0:d.last_name),1),t("p",St,a(r.month)+"/"+a(r.year)+" - "+a(u(r.total_hours)),1)])]),t("div",Ht,[t("button",{onClick:m=>M(r.id),class:"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 text-sm"}," Approva ",8,zt),t("button",{onClick:m=>B(r),class:"text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 text-sm"}," Dettagli ",8,Dt)])])}),128))]),w.value.length===0?(n(),i("div",Mt,e[17]||(e[17]=[t("p",{class:"text-gray-500 dark:text-gray-400"},"Nessuna approvazione in attesa",-1)]))):k("",!0)])])):(n(),i("div",Bt,[e[22]||(e[22]=t("div",{class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},[t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Il Mio Stato ")],-1)),t("div",Ft,[t("div",Rt,[t("div",Vt,[e[18]||(e[18]=t("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Timesheet Corrente",-1)),t("span",{class:P(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",R(p.value.status)])},a(V(p.value.status)),3)]),t("div",$t,[e[19]||(e[19]=t("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Ore Registrate",-1)),t("span",Et,a(u(p.value.totalHours)),1)]),t("div",Nt,[e[20]||(e[20]=t("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Ore Fatturabili",-1)),t("span",Ot,a(u(p.value.billableHours)),1)])]),t("div",Pt,[b(o,{to:"/app/timesheet/status",class:"text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 text-sm"},{default:j(()=>e[21]||(e[21]=[f(" Vedi Stato Completo → ")])),_:1,__:[21]})])])]))])])}}};export{Qt as default};

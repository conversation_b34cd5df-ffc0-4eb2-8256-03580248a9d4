import{u as B}from"./app.js";import{r as l,A as E,c as i,j as t,g as d,a as j,t as a,i as D,b as I,n as k,F,k as z,H as L,o as n,m as R}from"./vendor.js";const P={class:"space-y-6"},X={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},H={class:"flex justify-between items-center"},Y={class:"flex space-x-3"},G=["disabled"],U={key:0,class:"bg-white dark:bg-gray-800 shadow rounded-lg"},J={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},W={class:"flex justify-between items-center"},q={class:"text-lg font-medium text-gray-900 dark:text-white"},K={class:"flex items-center space-x-3"},Q=["disabled"],Z={class:"p-6"},tt={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},et={class:"text-center"},st={class:"text-2xl font-bold text-gray-900 dark:text-white"},rt={class:"text-center"},at={class:"text-2xl font-bold text-green-600"},ot={class:"text-center"},it={class:"text-2xl font-bold text-blue-600"},nt={class:"text-center"},dt={class:"text-2xl font-bold text-purple-600"},lt={key:0,class:"mt-6 p-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-lg"},ct={class:"flex"},ut={class:"ml-3"},xt={class:"mt-2 text-sm text-red-700 dark:text-red-300"},pt={key:1,class:"mt-6 border-t border-gray-200 dark:border-gray-600 pt-4"},gt={class:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm"},mt={key:0},yt={class:"text-gray-900 dark:text-white font-medium"},ht={key:1},vt={class:"text-gray-900 dark:text-white font-medium"},kt={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},_t={class:"overflow-x-auto"},ft={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},bt={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},wt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},Tt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},Ct={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},St={class:"px-6 py-4 whitespace-nowrap"},jt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},Dt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},Ft={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},zt=["onClick"],Rt=["onClick"],Mt=["onClick"],At={key:0,class:"text-center py-8"},Ot={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},Vt={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},Nt={class:"flex justify-between items-center"},$t={class:"p-6"},Bt={class:"space-y-4"},Et={class:"flex items-center space-x-3"},It={class:"flex-shrink-0"},Lt={class:"text-sm font-medium text-gray-900 dark:text-white"},Pt={class:"text-sm text-gray-500 dark:text-gray-400"},Xt={class:"flex items-center space-x-3"},Ht={key:0,class:"text-center py-4"},Jt={__name:"TimesheetStatus",setup(Yt){const c=B(),x=l([]),o=l(null),_=l([]),w=l(!1),p=l(!1),g=l(!1),m=async()=>{w.value=!0;try{const s=await fetch("/api/monthly-timesheets/",{headers:{"Content-Type":"application/json","X-CSRFToken":c.csrfToken}});if(s.ok){const e=await s.json();x.value=e.data||[];const u=new Date,r=u.getFullYear(),v=u.getMonth()+1;o.value=x.value.find(S=>S.year===r&&S.month===v)}}catch(s){console.error("Error loading monthly timesheets:",s)}finally{w.value=!1}},M=async()=>{try{const s=await fetch("/api/time-off-requests/?limit=5",{headers:{"Content-Type":"application/json","X-CSRFToken":c.csrfToken}});if(s.ok){const e=await s.json();_.value=e.data||[]}}catch(s){console.error("Error loading time-off requests:",s)}},A=async()=>{p.value=!0;try{const s=new Date;(await fetch("/api/monthly-timesheets/generate",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":c.csrfToken},body:JSON.stringify({year:s.getFullYear(),month:s.getMonth()+1})})).ok&&await m()}catch(s){console.error("Error generating monthly timesheet:",s)}finally{p.value=!1}},T=async s=>{g.value=!0;try{(await fetch(`/api/monthly-timesheets/${s}/submit`,{method:"PUT",headers:{"Content-Type":"application/json","X-CSRFToken":c.csrfToken}})).ok&&await m()}catch(e){console.error("Error submitting timesheet:",e)}finally{g.value=!1}},O=async s=>{try{(await fetch(`/api/monthly-timesheets/${s}/reopen`,{method:"PUT",headers:{"Content-Type":"application/json","X-CSRFToken":c.csrfToken}})).ok&&await m()}catch(e){console.error("Error reopening timesheet:",e)}},V=s=>{console.log("View details:",s)},y=s=>!s||s===0?"0h":s%1===0?`${s}h`:`${s.toFixed(1)}h`,h=s=>new Date(s).toLocaleDateString("it-IT"),C=s=>{const e=new Date(s);return e.toLocaleDateString("it-IT")+" "+e.toLocaleTimeString("it-IT",{hour:"2-digit",minute:"2-digit"})},f=s=>{switch(s){case"approved":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";case"submitted":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";case"rejected":return"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"}},b=s=>{switch(s){case"approved":return"Approvato";case"submitted":return"In Attesa";case"rejected":return"Rifiutato";default:return"Bozza"}},N=s=>{switch(s){case"vacation":return"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";case"leave":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";case"smartworking":return"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"}},$=s=>{switch(s){case"vacation":return"Ferie";case"leave":return"Permesso";case"smartworking":return"Smart Working";default:return"Altro"}};return E(()=>{m(),M()}),(s,e)=>{const u=I("router-link");return n(),i("div",P,[t("div",X,[t("div",H,[e[2]||(e[2]=t("div",null,[t("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Stato Approvazioni"),t("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Monitora lo stato delle tue approvazioni timesheet mensili ")],-1)),t("div",Y,[t("button",{onClick:A,disabled:p.value,class:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium disabled:opacity-50"},a(p.value?"Generazione...":"Genera Mese Corrente"),9,G),j(u,{to:"/app/timesheet/entry",class:"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium"},{default:D(()=>e[1]||(e[1]=[R(" Registra Ore ")])),_:1,__:[1]})])])]),o.value?(n(),i("div",U,[t("div",J,[t("div",W,[t("h3",q," Timesheet "+a(o.value.month)+"/"+a(o.value.year),1),t("div",K,[t("span",{class:k(["inline-flex items-center px-3 py-1 rounded-full text-sm font-medium",f(o.value.status)])},a(b(o.value.status)),3),o.value.status==="draft"&&o.value.total_hours>0?(n(),i("button",{key:0,onClick:e[0]||(e[0]=r=>T(o.value.id)),disabled:g.value,class:"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium disabled:opacity-50"},a(g.value?"Invio...":"Sottometti per Approvazione"),9,Q)):d("",!0)])])]),t("div",Z,[t("div",tt,[t("div",et,[t("div",st,a(y(o.value.total_hours)),1),e[3]||(e[3]=t("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Ore Totali",-1))]),t("div",rt,[t("div",at,a(y(o.value.billable_hours)),1),e[4]||(e[4]=t("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Ore Fatturabili",-1))]),t("div",ot,[t("div",it,a(o.value.entries_count||0),1),e[5]||(e[5]=t("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Registrazioni",-1))]),t("div",nt,[t("div",dt,a(Math.round(o.value.billable_hours/Math.max(o.value.total_hours,1)*100))+"% ",1),e[6]||(e[6]=t("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"% Fatturabile",-1))])]),o.value.rejection_reason?(n(),i("div",lt,[t("div",ct,[e[8]||(e[8]=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-5 w-5 text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})])],-1)),t("div",ut,[e[7]||(e[7]=t("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"}," Timesheet Rifiutato ",-1)),t("div",xt,[t("p",null,a(o.value.rejection_reason),1)])])])])):d("",!0),o.value.submission_date||o.value.approval_date?(n(),i("div",pt,[t("dl",gt,[o.value.submission_date?(n(),i("div",mt,[e[9]||(e[9]=t("dt",{class:"text-gray-500 dark:text-gray-400"},"Data Sottomissione",-1)),t("dd",yt,a(C(o.value.submission_date)),1)])):d("",!0),o.value.approval_date?(n(),i("div",ht,[e[10]||(e[10]=t("dt",{class:"text-gray-500 dark:text-gray-400"},"Data Approvazione",-1)),t("dd",vt,a(C(o.value.approval_date)),1)])):d("",!0)])])):d("",!0)])])):d("",!0),t("div",kt,[e[13]||(e[13]=t("div",{class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},[t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Storico Approvazioni ")],-1)),t("div",_t,[t("table",ft,[e[11]||(e[11]=t("thead",{class:"bg-gray-50 dark:bg-gray-700"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Periodo "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Ore Totali "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Ore Fatturabili "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Stato "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Data Sottomissione "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Data Approvazione "),t("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Azioni ")])],-1)),t("tbody",bt,[(n(!0),i(F,null,z(x.value,r=>(n(),i("tr",{key:r.id},[t("td",wt,a(r.month)+"/"+a(r.year),1),t("td",Tt,a(y(r.total_hours)),1),t("td",Ct,a(y(r.billable_hours)),1),t("td",St,[t("span",{class:k(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",f(r.status)])},a(b(r.status)),3)]),t("td",jt,a(r.submission_date?h(r.submission_date):"-"),1),t("td",Dt,a(r.approval_date?h(r.approval_date):"-"),1),t("td",Ft,[t("button",{onClick:v=>V(r),class:"text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 mr-3"}," Dettagli ",8,zt),r.status==="draft"?(n(),i("button",{key:0,onClick:v=>T(r.id),class:"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"}," Sottometti ",8,Rt)):r.status==="rejected"?(n(),i("button",{key:1,onClick:v=>O(r.id),class:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"}," Riapri ",8,Mt)):d("",!0)])]))),128))])]),x.value.length===0?(n(),i("div",At,e[12]||(e[12]=[L('<div class="mx-auto h-12 w-12 text-gray-400"><svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg></div><h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Nessun timesheet mensile</h3><p class="mt-1 text-sm text-gray-500 dark:text-gray-400"> Inizia registrando le tue ore per generare il primo timesheet mensile. </p>',3)]))):d("",!0)])]),t("div",Ot,[t("div",Vt,[t("div",Nt,[e[15]||(e[15]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Richieste Ferie/Permessi ",-1)),j(u,{to:"/app/timesheet/requests",class:"text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 text-sm"},{default:D(()=>e[14]||(e[14]=[R(" Vedi Tutte → ")])),_:1,__:[14]})])]),t("div",$t,[t("div",Bt,[(n(!0),i(F,null,z(_.value,r=>(n(),i("div",{key:r.id,class:"flex items-center justify-between"},[t("div",Et,[t("div",It,[t("span",{class:k(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",N(r.request_type)])},a($(r.request_type)),3)]),t("div",null,[t("p",Lt,a(h(r.start_date))+" - "+a(h(r.end_date)),1),t("p",Pt,a(r.duration_days)+" giorni ",1)])]),t("div",Xt,[t("span",{class:k(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",f(r.status)])},a(b(r.status)),3)])]))),128))]),_.value.length===0?(n(),i("div",Ht,e[16]||(e[16]=[t("p",{class:"text-gray-500 dark:text-gray-400"},"Nessuna richiesta recente",-1)]))):d("",!0)])])])}}};export{Jt as default};

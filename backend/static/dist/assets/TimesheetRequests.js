import{r as o,f as y,w as H,A as X,c as d,j as t,g as C,v as g,G as T,x as k,F as G,k as O,H as J,t as s,s as S,o as n,n as j}from"./vendor.js";import{u as K}from"./app.js";const Q={class:"space-y-6"},Y={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Z={class:"flex justify-between items-center"},tt={class:"flex space-x-3"},et={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-4"},rt={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},at={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},st={class:"overflow-x-auto"},ot={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},it={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},dt={class:"px-6 py-4 whitespace-nowrap"},nt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},lt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},ut={class:"px-6 py-4 text-sm text-gray-900 dark:text-white max-w-xs truncate"},gt={class:"px-6 py-4 whitespace-nowrap"},ct={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},pt={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},mt=["onClick"],xt=["onClick"],yt={key:2,class:"text-gray-400 dark:text-gray-500"},vt={key:0,class:"text-center py-8"},kt={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},ft={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},ht={class:"flex items-center"},wt={class:"ml-5 w-0 flex-1"},bt={class:"text-lg font-medium text-gray-900 dark:text-white"},_t={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Ct={class:"flex items-center"},Rt={class:"ml-5 w-0 flex-1"},Tt={class:"text-lg font-medium text-gray-900 dark:text-white"},St={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},jt={class:"flex items-center"},Mt={class:"ml-5 w-0 flex-1"},Dt={class:"text-lg font-medium text-gray-900 dark:text-white"},Vt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},qt={class:"flex items-center"},Ft={class:"ml-5 w-0 flex-1"},At={class:"text-lg font-medium text-gray-900 dark:text-white"},zt={class:"mt-3"},Nt={class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},$t={class:"grid grid-cols-1 gap-4"},Et={class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},Pt=["required","placeholder"],Bt={class:"flex justify-end space-x-3 mt-6"},Ut=["disabled"],Ht={__name:"TimesheetRequests",setup(It){const f=K(),u=o([]),R=o(!1),h=o(!1),v=o(!1),l=o(""),c=o(""),p=o(""),m=o(""),i=o({start_date:"",end_date:"",notes:""}),M=y(()=>20),D=y(()=>u.value.filter(r=>r.request_type==="leave"&&r.status==="approved").reduce((r,e)=>r+(e.duration_days||0),0)),V=y(()=>26),q=y(()=>u.value.filter(r=>r.request_type==="smartworking"&&r.status==="approved").reduce((r,e)=>r+(e.duration_days||0),0)),F=y(()=>u.value.filter(r=>r.status==="pending").length),x=async()=>{R.value=!0;try{const r=new URLSearchParams;c.value&&r.append("request_type",c.value),p.value&&r.append("status",p.value),m.value&&r.append("start_date",m.value);const e=await fetch(`/api/time-off-requests/?${r}`,{headers:{"Content-Type":"application/json","X-CSRFToken":f.csrfToken}});if(e.ok){const a=await e.json();u.value=a.data||[]}}catch(r){console.error("Error loading requests:",r)}finally{R.value=!1}},w=r=>{l.value=r,i.value={start_date:"",end_date:"",notes:""},h.value=!0},b=()=>{h.value=!1,l.value=""},A=()=>{switch(l.value){case"vacation":return"Richiesta Ferie";case"leave":return"Richiesta Permesso";case"smart_working":return"Richiesta Smart Working";default:return"Nuova Richiesta"}},z=async()=>{v.value=!0;try{const r={request_type:l.value,...i.value};(await fetch("/api/time-off-requests/",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":f.csrfToken},body:JSON.stringify(r)})).ok&&(await x(),b())}catch(r){console.error("Error submitting request:",r)}finally{v.value=!1}},N=r=>{console.log("Edit request:",r)},$=async r=>{if(confirm("Sei sicuro di voler eliminare questa richiesta?"))try{(await fetch(`/api/time-off-requests/${r}`,{method:"DELETE",headers:{"Content-Type":"application/json","X-CSRFToken":f.csrfToken}})).ok&&await x()}catch(e){console.error("Error deleting request:",e)}},_=r=>new Date(r).toLocaleDateString("it-IT"),E=r=>`${_(r.start_date)} - ${_(r.end_date)}`,P=r=>`${r.duration_days||0} giorni`,B=r=>{switch(r){case"vacation":return"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";case"leave":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";case"smartworking":return"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"}},U=r=>{switch(r){case"vacation":return"Ferie";case"leave":return"Permesso";case"smartworking":return"Smart Working";default:return"Altro"}},I=r=>{switch(r){case"approved":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";case"rejected":return"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";default:return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"}},L=r=>{switch(r){case"approved":return"Approvato";case"rejected":return"Rifiutato";default:return"In Attesa"}};return H([c,p,m],()=>{x()}),X(()=>{x()}),(r,e)=>(n(),d("div",Q,[t("div",Y,[t("div",Z,[e[10]||(e[10]=t("div",null,[t("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Richieste"),t("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Gestisci le tue richieste di ferie, permessi e smart working ")],-1)),t("div",tt,[t("button",{onClick:e[0]||(e[0]=a=>w("vacation")),class:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"}," Richiedi Ferie "),t("button",{onClick:e[1]||(e[1]=a=>w("leave")),class:"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"}," Richiedi Permesso "),t("button",{onClick:e[2]||(e[2]=a=>w("smartworking")),class:"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium"}," Smart Working ")])])]),t("div",et,[t("div",rt,[t("div",null,[e[12]||(e[12]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Tipo Richiesta ",-1)),g(t("select",{"onUpdate:modelValue":e[3]||(e[3]=a=>c.value=a),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},e[11]||(e[11]=[t("option",{value:""},"Tutti i tipi",-1),t("option",{value:"vacation"},"Ferie",-1),t("option",{value:"leave"},"Permessi",-1),t("option",{value:"smartworking"},"Smart Working",-1)]),512),[[T,c.value]])]),t("div",null,[e[14]||(e[14]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Stato ",-1)),g(t("select",{"onUpdate:modelValue":e[4]||(e[4]=a=>p.value=a),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},e[13]||(e[13]=[t("option",{value:""},"Tutti gli stati",-1),t("option",{value:"pending"},"In Attesa",-1),t("option",{value:"approved"},"Approvato",-1),t("option",{value:"rejected"},"Rifiutato",-1)]),512),[[T,p.value]])]),t("div",null,[e[15]||(e[15]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Da Data ",-1)),g(t("input",{"onUpdate:modelValue":e[5]||(e[5]=a=>m.value=a),type:"date",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[k,m.value]])]),t("div",{class:"flex items-end"},[t("button",{onClick:x,class:"w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium"}," Filtra ")])])]),t("div",at,[e[18]||(e[18]=t("div",{class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},[t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Le Mie Richieste ")],-1)),t("div",st,[t("table",ot,[e[16]||(e[16]=t("thead",{class:"bg-gray-50 dark:bg-gray-700"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Tipo "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Periodo "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Durata "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Motivo "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Stato "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Richiesta il "),t("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Azioni ")])],-1)),t("tbody",it,[(n(!0),d(G,null,O(u.value,a=>(n(),d("tr",{key:a.id},[t("td",dt,[t("span",{class:j(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",B(a.request_type)])},s(U(a.request_type)),3)]),t("td",nt,s(E(a)),1),t("td",lt,s(P(a)),1),t("td",ut,s(a.notes||"N/A"),1),t("td",gt,[t("span",{class:j(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",I(a.status)])},s(L(a.status)),3)]),t("td",ct,s(_(a.created_at)),1),t("td",pt,[a.status==="pending"?(n(),d("button",{key:0,onClick:W=>N(a),class:"text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 mr-3"}," Modifica ",8,mt)):C("",!0),a.status==="pending"?(n(),d("button",{key:1,onClick:W=>$(a.id),class:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"}," Elimina ",8,xt)):(n(),d("span",yt,s(a.status==="approved"?"Approvata":"Rifiutata"),1))])]))),128))])]),u.value.length===0?(n(),d("div",vt,e[17]||(e[17]=[J('<div class="mx-auto h-12 w-12 text-gray-400"><svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg></div><h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Nessuna richiesta</h3><p class="mt-1 text-sm text-gray-500 dark:text-gray-400"> Non hai ancora effettuato richieste per il periodo selezionato. </p>',3)]))):C("",!0)])]),t("div",kt,[t("div",ft,[t("div",ht,[e[20]||(e[20]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])])],-1)),t("div",wt,[t("dl",null,[e[19]||(e[19]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Ferie Rimanenti ",-1)),t("dd",bt,s(M.value)+" giorni ",1)])])])]),t("div",_t,[t("div",Ct,[e[22]||(e[22]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",Rt,[t("dl",null,[e[21]||(e[21]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Permessi Usati ",-1)),t("dd",Tt,s(D.value)+" / "+s(V.value)+" giorni ",1)])])])]),t("div",St,[t("div",jt,[e[24]||(e[24]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2v0a2 2 0 012-2h6.586a1 1 0 01.707.293l2.414 2.414a1 1 0 00.707.293H19a2 2 0 012 2v0a2 2 0 00-2-2z"})])])],-1)),t("div",Mt,[t("dl",null,[e[23]||(e[23]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Smart Working ",-1)),t("dd",Dt,s(q.value)+" giorni ",1)])])])]),t("div",Vt,[t("div",qt,[e[26]||(e[26]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",Ft,[t("dl",null,[e[25]||(e[25]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," In Attesa ",-1)),t("dd",At,s(F.value),1)])])])])]),h.value?(n(),d("div",{key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:b},[t("div",{class:"relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:e[9]||(e[9]=S(()=>{},["stop"]))},[t("div",zt,[t("h3",Nt,s(A()),1),t("form",{onSubmit:S(z,["prevent"])},[t("div",$t,[t("div",null,[e[27]||(e[27]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Data Inizio ",-1)),g(t("input",{"onUpdate:modelValue":e[6]||(e[6]=a=>i.value.start_date=a),type:"date",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[k,i.value.start_date]])]),t("div",null,[e[28]||(e[28]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Data Fine ",-1)),g(t("input",{"onUpdate:modelValue":e[7]||(e[7]=a=>i.value.end_date=a),type:"date",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[k,i.value.end_date]])]),t("div",null,[t("label",Et,s(l.value==="smartworking"?"Note (opzionale)":"Motivo"),1),g(t("textarea",{"onUpdate:modelValue":e[8]||(e[8]=a=>i.value.notes=a),rows:"3",required:l.value!=="smartworking",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:l.value==="smartworking"?"Note aggiuntive...":"Descrivi il motivo della richiesta..."},null,8,Pt),[[k,i.value.notes]])])]),t("div",Bt,[t("button",{type:"button",onClick:b,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"}," Annulla "),t("button",{type:"submit",disabled:v.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"},s(v.value?"Invio...":"Invia Richiesta"),9,Ut)])],32)])])])):C("",!0)]))}};export{Ht as default};

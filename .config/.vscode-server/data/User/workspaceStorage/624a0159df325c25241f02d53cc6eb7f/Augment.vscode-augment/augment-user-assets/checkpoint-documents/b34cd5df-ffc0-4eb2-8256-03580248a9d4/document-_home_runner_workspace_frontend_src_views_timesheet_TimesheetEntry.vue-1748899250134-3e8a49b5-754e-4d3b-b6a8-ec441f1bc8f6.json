{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetEntry.vue"}, "originalCode": "<template>\n  <div class=\"space-y-6\">\n    <!-- Header -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n      <div class=\"flex justify-between items-center\">\n        <div>\n          <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\">Le Mie Ore</h1>\n          <p class=\"mt-1 text-sm text-gray-600 dark:text-gray-400\">\n            Registra le tue ore di lavoro con la griglia mensile\n          </p>\n        </div>\n\n        <!-- Controlli periodo -->\n        <div class=\"flex items-center space-x-4\">\n          <div class=\"flex items-center space-x-2\">\n            <button\n              @click=\"previousMonth\"\n              class=\"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n            >\n              <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 19l-7-7 7-7\"></path>\n              </svg>\n            </button>\n\n            <div class=\"text-center\">\n              <h2 class=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                {{ monthNames[currentMonth - 1] }} {{ currentYear }}\n              </h2>\n            </div>\n\n            <button\n              @click=\"nextMonth\"\n              class=\"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n            >\n              <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\"></path>\n              </svg>\n            </button>\n          </div>\n\n          <button\n            @click=\"showAddProjectModal = true\"\n            class=\"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n          >\n            Aggiungi Progetto\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Griglia Timesheet Mensile -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <div class=\"flex items-center justify-between\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n            Griglia Ore - {{ monthNames[currentMonth - 1] }} {{ currentYear }}\n          </h3>\n          <div class=\"flex items-center space-x-4\">\n            <div class=\"text-sm text-gray-500 dark:text-gray-400\">\n              Totale: {{ formatHours(totalHours) }} | Fatturabili: {{ formatHours(billableHours) }}\n            </div>\n            <!-- Debug button - remove in production -->\n            <button\n              @click=\"autoPopulateProjectTasks\"\n              class=\"text-xs bg-red-500 text-white px-2 py-1 rounded\"\n              v-if=\"projectTasks.length === 0 && timesheets.length > 0\"\n            >\n              DEBUG: Popola Griglia ({{ timesheets.length }} ore trovate)\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"overflow-x-auto\">\n        <table class=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n          <!-- Header con giorni del mese -->\n          <thead class=\"bg-gray-50 dark:bg-gray-700\">\n            <tr>\n              <th class=\"sticky left-0 z-10 bg-gray-50 dark:bg-gray-700 px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-r border-gray-200 dark:border-gray-600\">\n                Progetto/Task\n              </th>\n              <th\n                v-for=\"day in daysInMonth\"\n                :key=\"day\"\n                class=\"px-2 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider min-w-[50px]\"\n                :class=\"{\n                  'bg-blue-50 dark:bg-blue-900/20': isToday(day),\n                  'bg-red-50 dark:bg-red-900/20': isWeekend(day)\n                }\"\n              >\n                <div>{{ day }}</div>\n                <div class=\"text-xs text-gray-400\">{{ getDayName(day) }}</div>\n              </th>\n              <th class=\"px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-l border-gray-200 dark:border-gray-600\">\n                Totale\n              </th>\n            </tr>\n          </thead>\n\n          <!-- Righe progetti/task -->\n          <tbody class=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n            <tr v-if=\"projectTasks.length === 0\">\n              <td :colspan=\"daysInMonth.length + 2\" class=\"px-6 py-8 text-center text-gray-500 dark:text-gray-400\">\n                <div class=\"space-y-2\">\n                  <p>Nessun progetto configurato</p>\n                  <button\n                    @click=\"showAddProjectModal = true\"\n                    class=\"text-primary-600 hover:text-primary-700 dark:text-primary-400\"\n                  >\n                    Aggiungi il tuo primo progetto\n                  </button>\n                </div>\n              </td>\n            </tr>\n\n            <tr v-for=\"projectTask in projectTasks\" :key=\"`${projectTask.project_id}-${projectTask.task_id || 'no-task'}`\">\n              <!-- Colonna progetto/task -->\n              <td class=\"sticky left-0 z-10 bg-white dark:bg-gray-800 px-4 py-3 border-r border-gray-200 dark:border-gray-600\">\n                <div class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  {{ projectTask.project_name }}\n                </div>\n                <div class=\"text-xs text-gray-500 dark:text-gray-400\">\n                  {{ projectTask.task_name || 'Nessun task specifico' }}\n                </div>\n                <div class=\"flex items-center mt-1 space-x-2\">\n                  <span\n                    class=\"inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium\"\n                    :class=\"projectTask.billable ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'\"\n                  >\n                    {{ projectTask.billable ? 'Fatt.' : 'Int.' }}\n                  </span>\n                  <button\n                    @click=\"removeProjectTask(projectTask)\"\n                    class=\"text-red-400 hover:text-red-600 dark:hover:text-red-300\"\n                    title=\"Rimuovi dalla griglia\"\n                  >\n                    <svg class=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n                    </svg>\n                  </button>\n                </div>\n              </td>\n\n              <!-- Celle ore per ogni giorno -->\n              <td\n                v-for=\"day in daysInMonth\"\n                :key=\"day\"\n                class=\"px-1 py-2 text-center\"\n                :class=\"{\n                  'bg-blue-50 dark:bg-blue-900/20': isToday(day),\n                  'bg-red-50 dark:bg-red-900/20': isWeekend(day)\n                }\"\n              >\n                <input\n                  type=\"number\"\n                  step=\"0.5\"\n                  min=\"0\"\n                  max=\"24\"\n                  :value=\"getHoursForDay(projectTask, day)\"\n                  @input=\"updateHours(projectTask, day, $event.target.value)\"\n                  @blur=\"saveEntry(projectTask, day)\"\n                  class=\"w-12 h-8 text-xs text-center border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:border-primary-500 focus:ring-1 focus:ring-primary-500\"\n                  :class=\"{\n                    'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-300 dark:border-yellow-600': hasUnsavedChanges(projectTask, day),\n                    'bg-green-50 dark:bg-green-900/20 border-green-300 dark:border-green-600': getHoursForDay(projectTask, day) > 0\n                  }\"\n                />\n              </td>\n\n              <!-- Totale riga -->\n              <td class=\"px-4 py-3 text-center text-sm font-medium text-gray-900 dark:text-white border-l border-gray-200 dark:border-gray-600\">\n                {{ formatHours(getRowTotal(projectTask)) }}\n              </td>\n            </tr>\n\n            <!-- Riga totali giornalieri -->\n            <tr class=\"bg-gray-50 dark:bg-gray-700 font-medium\">\n              <td class=\"sticky left-0 z-10 bg-gray-50 dark:bg-gray-700 px-4 py-3 text-sm text-gray-900 dark:text-white border-r border-gray-200 dark:border-gray-600\">\n                Totale Giornaliero\n              </td>\n              <td\n                v-for=\"day in daysInMonth\"\n                :key=\"day\"\n                class=\"px-2 py-3 text-center text-sm text-gray-900 dark:text-white\"\n                :class=\"{\n                  'bg-blue-100 dark:bg-blue-800': isToday(day),\n                  'bg-red-100 dark:bg-red-800': isWeekend(day)\n                }\"\n              >\n                {{ formatHours(getDayTotal(day)) }}\n              </td>\n              <td class=\"px-4 py-3 text-center text-sm font-bold text-gray-900 dark:text-white border-l border-gray-200 dark:border-gray-600\">\n                {{ formatHours(totalHours) }}\n              </td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n    </div>\n\n    <!-- Statistiche -->\n    <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Ore Totali\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(totalHours) }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Ore Fatturabili\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(billableHours) }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                In Attesa\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(pendingHours) }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Progetti Attivi\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ activeProjects }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Modal Aggiungi Progetto -->\n    <div v-if=\"showAddProjectModal\" class=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n      <div class=\"relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 shadow-lg rounded-md bg-white dark:bg-gray-800\">\n        <div class=\"mt-3\">\n          <div class=\"flex items-center justify-between mb-4\">\n            <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n              Aggiungi Progetto alla Griglia\n            </h3>\n            <button @click=\"showAddProjectModal = false\" class=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\">\n              <svg class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </button>\n          </div>\n\n          <div class=\"space-y-4\">\n            <div>\n              <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Progetto *\n              </label>\n              <select\n                v-model=\"newProjectTask.project_id\"\n                @change=\"loadTasksForProject\"\n                class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white\"\n              >\n                <option value=\"\">Seleziona progetto</option>\n                <option v-for=\"project in projects\" :key=\"project.id\" :value=\"project.id\">\n                  {{ project.name }}\n                </option>\n              </select>\n            </div>\n\n            <div v-if=\"newProjectTask.project_id\">\n              <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Task (opzionale)\n              </label>\n              <select\n                v-model=\"newProjectTask.task_id\"\n                class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white\"\n              >\n                <option value=\"\">Nessun task specifico</option>\n                <option v-for=\"task in availableTasks\" :key=\"task.id\" :value=\"task.id\">\n                  {{ task.name }}\n                </option>\n              </select>\n            </div>\n\n            <div>\n              <label class=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  v-model=\"newProjectTask.billable\"\n                  class=\"rounded border-gray-300 dark:border-gray-600\"\n                >\n                <span class=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">Ore fatturabili</span>\n              </label>\n            </div>\n\n            <div class=\"flex space-x-3\">\n              <button\n                @click=\"addProjectToGrid\"\n                :disabled=\"!newProjectTask.project_id\"\n                class=\"bg-primary-600 hover:bg-primary-700 disabled:bg-primary-400 text-white px-4 py-2 rounded-md text-sm font-medium\"\n              >\n                Aggiungi alla Griglia\n              </button>\n              <button\n                @click=\"showAddProjectModal = false\"\n                class=\"bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium\"\n              >\n                Annulla\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, watch } from 'vue'\nimport { useAuthStore } from '@/stores/auth'\n\nconst authStore = useAuthStore()\n\n// State\nconst timesheets = ref([])\nconst projects = ref([])\nconst tasks = ref([])\nconst projectTasks = ref([])\nconst availableTasks = ref([])\nconst loading = ref(false)\nconst showAddProjectModal = ref(false)\nconst unsavedChanges = ref(new Map())\n\n// Date navigation\nconst currentYear = ref(new Date().getFullYear())\nconst currentMonth = ref(new Date().getMonth() + 1)\n\n// New project/task form\nconst newProjectTask = ref({\n  project_id: '',\n  task_id: '',\n  billable: true\n})\n\n// Constants\nconst monthNames = [\n  'Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno',\n  'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'\n]\n\n// Computed\nconst daysInMonth = computed(() => {\n  const year = currentYear.value\n  const month = currentMonth.value\n  const daysCount = new Date(year, month, 0).getDate()\n  return Array.from({ length: daysCount }, (_, i) => i + 1)\n})\n\nconst totalHours = computed(() => {\n  return timesheets.value.reduce((sum, entry) => sum + entry.hours, 0)\n})\n\nconst billableHours = computed(() => {\n  return timesheets.value.reduce((sum, entry) => sum + (entry.billable ? entry.hours : 0), 0)\n})\n\nconst pendingHours = computed(() => {\n  return timesheets.value.reduce((sum, entry) => sum + (entry.status === 'pending' ? entry.hours : 0), 0)\n})\n\nconst activeProjects = computed(() => {\n  return projectTasks.value.length\n})\n\n// Methods\nconst loadProjects = async () => {\n  try {\n    const response = await fetch('/api/projects/', {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (response.ok) {\n      const result = await response.json()\n      projects.value = result.data || []\n    }\n  } catch (err) {\n    console.error('Error loading projects:', err)\n  }\n}\n\nconst loadTasks = async () => {\n  try {\n    const response = await fetch('/api/tasks/', {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (response.ok) {\n      const result = await response.json()\n      tasks.value = result.data || []\n    }\n  } catch (err) {\n    console.error('Error loading tasks:', err)\n  }\n}\n\nconst loadTimesheets = async () => {\n  loading.value = true\n\n  try {\n    // Calculate proper end date for the month\n    const year = currentYear.value\n    const month = currentMonth.value\n    const lastDay = new Date(year, month, 0).getDate()\n\n    const params = new URLSearchParams({\n      start_date: `${year}-${String(month).padStart(2, '0')}-01`,\n      end_date: `${year}-${String(month).padStart(2, '0')}-${String(lastDay).padStart(2, '0')}`\n    })\n\n    const response = await fetch(`/api/timesheets/?${params}`, {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (response.ok) {\n      const result = await response.json()\n      timesheets.value = result.data || []\n\n      // Auto-populate projectTasks from existing timesheet entries\n      autoPopulateProjectTasks()\n    }\n  } catch (err) {\n    console.error('Error loading timesheets:', err)\n  } finally {\n    loading.value = false\n  }\n}\n\nconst previousMonth = () => {\n  if (currentMonth.value === 1) {\n    currentMonth.value = 12\n    currentYear.value--\n  } else {\n    currentMonth.value--\n  }\n  loadTimesheets()\n}\n\nconst nextMonth = () => {\n  if (currentMonth.value === 12) {\n    currentMonth.value = 1\n    currentYear.value++\n  } else {\n    currentMonth.value++\n  }\n  loadTimesheets()\n}\n\n// Auto-populate projectTasks from existing timesheet entries\nconst autoPopulateProjectTasks = () => {\n  console.log('Auto-populating project tasks from', timesheets.value.length, 'timesheet entries')\n\n  const existingProjectTasks = new Set()\n\n  // Extract unique project/task combinations from timesheets\n  timesheets.value.forEach(entry => {\n    const key = `${entry.project_id}-${entry.task_id || 'no-task'}`\n    if (!existingProjectTasks.has(key)) {\n      existingProjectTasks.add(key)\n\n      // Check if this project/task is already in the grid\n      const alreadyExists = projectTasks.value.some(pt =>\n        pt.project_id === entry.project_id &&\n        pt.task_id === entry.task_id\n      )\n\n      if (!alreadyExists) {\n        // Find project name from projects list\n        const project = projects.value.find(p => p.id === entry.project_id)\n        const projectName = project?.name || entry.project?.name || `Progetto ${entry.project_id}`\n\n        // Find task name from tasks list\n        const task = tasks.value.find(t => t.id === entry.task_id)\n        const taskName = task?.name || entry.task?.name || null\n\n        const newProjectTask = {\n          project_id: entry.project_id,\n          task_id: entry.task_id,\n          project_name: projectName,\n          task_name: taskName,\n          billable: entry.billable || true\n        }\n\n        console.log('Adding project task to grid:', newProjectTask)\n        projectTasks.value.push(newProjectTask)\n      }\n    }\n  })\n\n  console.log('Final projectTasks:', projectTasks.value)\n}\n\n// Grid helper methods\nconst isToday = (day) => {\n  const today = new Date()\n  return today.getDate() === day &&\n         today.getMonth() + 1 === currentMonth.value &&\n         today.getFullYear() === currentYear.value\n}\n\nconst isWeekend = (day) => {\n  const date = new Date(currentYear.value, currentMonth.value - 1, day)\n  const dayOfWeek = date.getDay()\n  return dayOfWeek === 0 || dayOfWeek === 6 // Sunday or Saturday\n}\n\nconst getDayName = (day) => {\n  const date = new Date(currentYear.value, currentMonth.value - 1, day)\n  return date.toLocaleDateString('it-IT', { weekday: 'short' }).toUpperCase()\n}\n\nconst getHoursForDay = (projectTask, day) => {\n  const dateStr = `${currentYear.value}-${String(currentMonth.value).padStart(2, '0')}-${String(day).padStart(2, '0')}`\n  const entry = timesheets.value.find(t => {\n    const projectMatch = t.project_id === projectTask.project_id\n    const taskMatch = (t.task_id === projectTask.task_id) ||\n                     (t.task_id === null && projectTask.task_id === null) ||\n                     (t.task_id === undefined && projectTask.task_id === null)\n    const dateMatch = t.date === dateStr\n\n    return projectMatch && taskMatch && dateMatch\n  })\n  return entry ? entry.hours : 0\n}\n\nconst updateHours = (projectTask, day, value) => {\n  const key = `${projectTask.project_id}-${projectTask.task_id || 'no-task'}-${day}`\n  if (value === '' || value === '0') {\n    unsavedChanges.value.delete(key)\n  } else {\n    unsavedChanges.value.set(key, {\n      projectTask,\n      day,\n      hours: parseFloat(value) || 0\n    })\n  }\n}\n\nconst hasUnsavedChanges = (projectTask, day) => {\n  const key = `${projectTask.project_id}-${projectTask.task_id || 'no-task'}-${day}`\n  return unsavedChanges.value.has(key)\n}\n\nconst saveEntry = async (projectTask, day) => {\n  const key = `${projectTask.project_id}-${projectTask.task_id || 'no-task'}-${day}`\n  const change = unsavedChanges.value.get(key)\n\n  if (!change) return\n\n  const dateStr = `${currentYear.value}-${String(currentMonth.value).padStart(2, '0')}-${String(day).padStart(2, '0')}`\n\n  try {\n    // Find existing entry\n    const existingEntry = timesheets.value.find(t =>\n      t.project_id === projectTask.project_id &&\n      t.task_id === projectTask.task_id &&\n      t.date === dateStr\n    )\n\n    if (change.hours === 0) {\n      // Delete entry if hours is 0\n      if (existingEntry) {\n        await deleteTimesheetEntry(existingEntry.id)\n      }\n    } else {\n      // Create or update entry\n      const entryData = {\n        project_id: projectTask.project_id,\n        task_id: projectTask.task_id || null,\n        date: dateStr,\n        hours: change.hours,\n        billable: projectTask.billable,\n        description: ''\n      }\n\n      if (existingEntry) {\n        await updateTimesheetEntry(existingEntry.id, entryData)\n      } else {\n        await createTimesheetEntry(entryData)\n      }\n    }\n\n    unsavedChanges.value.delete(key)\n    await loadTimesheets()\n  } catch (err) {\n    console.error('Error saving entry:', err)\n    alert('Errore nel salvare le ore. Riprova.')\n  }\n}\n\nconst createTimesheetEntry = async (data) => {\n  const response = await fetch('/api/timesheets/', {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json',\n      'X-CSRFToken': authStore.csrfToken\n    },\n    body: JSON.stringify(data)\n  })\n\n  if (!response.ok) {\n    const error = await response.json()\n    throw new Error(error.message || 'Errore nella creazione')\n  }\n}\n\nconst updateTimesheetEntry = async (id, data) => {\n  const response = await fetch(`/api/timesheets/${id}`, {\n    method: 'PUT',\n    headers: {\n      'Content-Type': 'application/json',\n      'X-CSRFToken': authStore.csrfToken\n    },\n    body: JSON.stringify(data)\n  })\n\n  if (!response.ok) {\n    const error = await response.json()\n    throw new Error(error.message || 'Errore nell\\'aggiornamento')\n  }\n}\n\nconst deleteTimesheetEntry = async (id) => {\n  const response = await fetch(`/api/timesheets/${id}`, {\n    method: 'DELETE',\n    headers: {\n      'Content-Type': 'application/json',\n      'X-CSRFToken': authStore.csrfToken\n    }\n  })\n\n  if (!response.ok) {\n    const error = await response.json()\n    throw new Error(error.message || 'Errore nell\\'eliminazione')\n  }\n}\n\nconst getRowTotal = (projectTask) => {\n  return daysInMonth.value.reduce((sum, day) => {\n    return sum + getHoursForDay(projectTask, day)\n  }, 0)\n}\n\nconst getDayTotal = (day) => {\n  return projectTasks.value.reduce((sum, projectTask) => {\n    return sum + getHoursForDay(projectTask, day)\n  }, 0)\n}\n\n// Project/Task management\nconst loadTasksForProject = async () => {\n  if (!newProjectTask.value.project_id) {\n    availableTasks.value = []\n    return\n  }\n\n  availableTasks.value = tasks.value.filter(task =>\n    task.project_id === parseInt(newProjectTask.value.project_id)\n  )\n}\n\nconst addProjectToGrid = () => {\n  const project = projects.value.find(p => p.id === parseInt(newProjectTask.value.project_id))\n  const task = newProjectTask.value.task_id ?\n    availableTasks.value.find(t => t.id === parseInt(newProjectTask.value.task_id)) : null\n\n  const projectTaskKey = `${newProjectTask.value.project_id}-${newProjectTask.value.task_id || 'no-task'}`\n\n  // Check if already exists\n  if (projectTasks.value.some(pt =>\n    pt.project_id === parseInt(newProjectTask.value.project_id) &&\n    pt.task_id === (newProjectTask.value.task_id ? parseInt(newProjectTask.value.task_id) : null)\n  )) {\n    alert('Questo progetto/task è già presente nella griglia')\n    return\n  }\n\n  projectTasks.value.push({\n    project_id: parseInt(newProjectTask.value.project_id),\n    task_id: newProjectTask.value.task_id ? parseInt(newProjectTask.value.task_id) : null,\n    project_name: project.name,\n    task_name: task?.name || null,\n    billable: newProjectTask.value.billable\n  })\n\n  // Reset form\n  newProjectTask.value = {\n    project_id: '',\n    task_id: '',\n    billable: true\n  }\n  availableTasks.value = []\n  showAddProjectModal.value = false\n}\n\nconst removeProjectTask = (projectTask) => {\n  if (confirm('Rimuovere questo progetto/task dalla griglia? Le ore registrate non verranno eliminate.')) {\n    const index = projectTasks.value.findIndex(pt =>\n      pt.project_id === projectTask.project_id && pt.task_id === projectTask.task_id\n    )\n    if (index !== -1) {\n      projectTasks.value.splice(index, 1)\n    }\n  }\n}\n\nconst formatHours = (hours) => {\n  if (!hours || hours === 0) return '0h'\n  return hours % 1 === 0 ? `${hours}h` : `${hours.toFixed(1)}h`\n}\n\n// Lifecycle\nonMounted(() => {\n  loadProjects()\n  loadTasks()\n  loadTimesheets()\n})\n\n// Watchers\nwatch([currentMonth, currentYear], () => {\n  loadTimesheets()\n})\n</script>\n", "modifiedCode": "<template>\n  <div class=\"space-y-6\">\n    <!-- Header -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n      <div class=\"flex justify-between items-center\">\n        <div>\n          <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\">Le Mie Ore</h1>\n          <p class=\"mt-1 text-sm text-gray-600 dark:text-gray-400\">\n            Registra le tue ore di lavoro con la griglia mensile\n          </p>\n        </div>\n\n        <!-- Controlli periodo -->\n        <div class=\"flex items-center space-x-4\">\n          <div class=\"flex items-center space-x-2\">\n            <button\n              @click=\"previousMonth\"\n              class=\"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n            >\n              <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 19l-7-7 7-7\"></path>\n              </svg>\n            </button>\n\n            <div class=\"text-center\">\n              <h2 class=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                {{ monthNames[currentMonth - 1] }} {{ currentYear }}\n              </h2>\n            </div>\n\n            <button\n              @click=\"nextMonth\"\n              class=\"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n            >\n              <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\"></path>\n              </svg>\n            </button>\n          </div>\n\n          <button\n            @click=\"showAddProjectModal = true\"\n            class=\"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n          >\n            Aggiungi Progetto\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Griglia Timesheet Mensile -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <div class=\"flex items-center justify-between\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n            Griglia Ore - {{ monthNames[currentMonth - 1] }} {{ currentYear }}\n          </h3>\n          <div class=\"flex items-center space-x-4\">\n            <div class=\"text-sm text-gray-500 dark:text-gray-400\">\n              Totale: {{ formatHours(totalHours) }} | Fatturabili: {{ formatHours(billableHours) }}\n            </div>\n            <!-- Debug button - remove in production -->\n            <button\n              @click=\"autoPopulateProjectTasks\"\n              class=\"text-xs bg-red-500 text-white px-2 py-1 rounded\"\n              v-if=\"projectTasks.length === 0 && timesheets.length > 0\"\n            >\n              DEBUG: Popola Griglia ({{ timesheets.length }} ore trovate)\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"overflow-x-auto\">\n        <table class=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n          <!-- Header con giorni del mese -->\n          <thead class=\"bg-gray-50 dark:bg-gray-700\">\n            <tr>\n              <th class=\"sticky left-0 z-10 bg-gray-50 dark:bg-gray-700 px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-r border-gray-200 dark:border-gray-600\">\n                Progetto/Task\n              </th>\n              <th\n                v-for=\"day in daysInMonth\"\n                :key=\"day\"\n                class=\"px-2 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider min-w-[50px]\"\n                :class=\"{\n                  'bg-blue-50 dark:bg-blue-900/20': isToday(day),\n                  'bg-red-50 dark:bg-red-900/20': isWeekend(day)\n                }\"\n              >\n                <div>{{ day }}</div>\n                <div class=\"text-xs text-gray-400\">{{ getDayName(day) }}</div>\n              </th>\n              <th class=\"px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-l border-gray-200 dark:border-gray-600\">\n                Totale\n              </th>\n            </tr>\n          </thead>\n\n          <!-- Righe progetti/task -->\n          <tbody class=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n            <tr v-if=\"projectTasks.length === 0\">\n              <td :colspan=\"daysInMonth.length + 2\" class=\"px-6 py-8 text-center text-gray-500 dark:text-gray-400\">\n                <div class=\"space-y-2\">\n                  <p>Nessun progetto configurato</p>\n                  <button\n                    @click=\"showAddProjectModal = true\"\n                    class=\"text-primary-600 hover:text-primary-700 dark:text-primary-400\"\n                  >\n                    Aggiungi il tuo primo progetto\n                  </button>\n                </div>\n              </td>\n            </tr>\n\n            <tr v-for=\"projectTask in projectTasks\" :key=\"`${projectTask.project_id}-${projectTask.task_id || 'no-task'}`\">\n              <!-- Colonna progetto/task -->\n              <td class=\"sticky left-0 z-10 bg-white dark:bg-gray-800 px-4 py-3 border-r border-gray-200 dark:border-gray-600\">\n                <div class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  {{ projectTask.project_name }}\n                </div>\n                <div class=\"text-xs text-gray-500 dark:text-gray-400\">\n                  {{ projectTask.task_name || 'Nessun task specifico' }}\n                </div>\n                <div class=\"flex items-center mt-1 space-x-2\">\n                  <span\n                    class=\"inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium\"\n                    :class=\"projectTask.billable ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'\"\n                  >\n                    {{ projectTask.billable ? 'Fatt.' : 'Int.' }}\n                  </span>\n                  <button\n                    @click=\"removeProjectTask(projectTask)\"\n                    class=\"text-red-400 hover:text-red-600 dark:hover:text-red-300\"\n                    title=\"Rimuovi dalla griglia\"\n                  >\n                    <svg class=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n                    </svg>\n                  </button>\n                </div>\n              </td>\n\n              <!-- Celle ore per ogni giorno -->\n              <td\n                v-for=\"day in daysInMonth\"\n                :key=\"day\"\n                class=\"px-1 py-2 text-center\"\n                :class=\"{\n                  'bg-blue-50 dark:bg-blue-900/20': isToday(day),\n                  'bg-red-50 dark:bg-red-900/20': isWeekend(day)\n                }\"\n              >\n                <input\n                  type=\"number\"\n                  step=\"0.5\"\n                  min=\"0\"\n                  max=\"24\"\n                  :value=\"getHoursForDay(projectTask, day)\"\n                  @input=\"updateHours(projectTask, day, $event.target.value)\"\n                  @blur=\"saveEntry(projectTask, day)\"\n                  class=\"w-12 h-8 text-xs text-center border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:border-primary-500 focus:ring-1 focus:ring-primary-500\"\n                  :class=\"{\n                    'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-300 dark:border-yellow-600': hasUnsavedChanges(projectTask, day),\n                    'bg-green-50 dark:bg-green-900/20 border-green-300 dark:border-green-600': getHoursForDay(projectTask, day) > 0\n                  }\"\n                />\n              </td>\n\n              <!-- Totale riga -->\n              <td class=\"px-4 py-3 text-center text-sm font-medium text-gray-900 dark:text-white border-l border-gray-200 dark:border-gray-600\">\n                {{ formatHours(getRowTotal(projectTask)) }}\n              </td>\n            </tr>\n\n            <!-- Riga totali giornalieri -->\n            <tr class=\"bg-gray-50 dark:bg-gray-700 font-medium\">\n              <td class=\"sticky left-0 z-10 bg-gray-50 dark:bg-gray-700 px-4 py-3 text-sm text-gray-900 dark:text-white border-r border-gray-200 dark:border-gray-600\">\n                Totale Giornaliero\n              </td>\n              <td\n                v-for=\"day in daysInMonth\"\n                :key=\"day\"\n                class=\"px-2 py-3 text-center text-sm text-gray-900 dark:text-white\"\n                :class=\"{\n                  'bg-blue-100 dark:bg-blue-800': isToday(day),\n                  'bg-red-100 dark:bg-red-800': isWeekend(day)\n                }\"\n              >\n                {{ formatHours(getDayTotal(day)) }}\n              </td>\n              <td class=\"px-4 py-3 text-center text-sm font-bold text-gray-900 dark:text-white border-l border-gray-200 dark:border-gray-600\">\n                {{ formatHours(totalHours) }}\n              </td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n    </div>\n\n    <!-- Statistiche -->\n    <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Ore Totali\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(totalHours) }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Ore Fatturabili\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(billableHours) }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                In Attesa\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(pendingHours) }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Progetti Attivi\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ activeProjects }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Modal Aggiungi Progetto -->\n    <div v-if=\"showAddProjectModal\" class=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n      <div class=\"relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 shadow-lg rounded-md bg-white dark:bg-gray-800\">\n        <div class=\"mt-3\">\n          <div class=\"flex items-center justify-between mb-4\">\n            <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n              Aggiungi Progetto alla Griglia\n            </h3>\n            <button @click=\"showAddProjectModal = false\" class=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\">\n              <svg class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </button>\n          </div>\n\n          <div class=\"space-y-4\">\n            <div>\n              <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Progetto *\n              </label>\n              <select\n                v-model=\"newProjectTask.project_id\"\n                @change=\"loadTasksForProject\"\n                class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white\"\n              >\n                <option value=\"\">Seleziona progetto</option>\n                <option v-for=\"project in projects\" :key=\"project.id\" :value=\"project.id\">\n                  {{ project.name }}\n                </option>\n              </select>\n            </div>\n\n            <div v-if=\"newProjectTask.project_id\">\n              <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Task (opzionale)\n              </label>\n              <select\n                v-model=\"newProjectTask.task_id\"\n                class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white\"\n              >\n                <option value=\"\">Nessun task specifico</option>\n                <option v-for=\"task in availableTasks\" :key=\"task.id\" :value=\"task.id\">\n                  {{ task.name }}\n                </option>\n              </select>\n            </div>\n\n            <div>\n              <label class=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  v-model=\"newProjectTask.billable\"\n                  class=\"rounded border-gray-300 dark:border-gray-600\"\n                >\n                <span class=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">Ore fatturabili</span>\n              </label>\n            </div>\n\n            <div class=\"flex space-x-3\">\n              <button\n                @click=\"addProjectToGrid\"\n                :disabled=\"!newProjectTask.project_id\"\n                class=\"bg-primary-600 hover:bg-primary-700 disabled:bg-primary-400 text-white px-4 py-2 rounded-md text-sm font-medium\"\n              >\n                Aggiungi alla Griglia\n              </button>\n              <button\n                @click=\"showAddProjectModal = false\"\n                class=\"bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium\"\n              >\n                Annulla\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, watch } from 'vue'\nimport { useAuthStore } from '@/stores/auth'\n\nconst authStore = useAuthStore()\n\n// State\nconst timesheets = ref([])\nconst projects = ref([])\nconst tasks = ref([])\nconst projectTasks = ref([])\nconst availableTasks = ref([])\nconst loading = ref(false)\nconst showAddProjectModal = ref(false)\nconst unsavedChanges = ref(new Map())\n\n// Date navigation\nconst currentYear = ref(new Date().getFullYear())\nconst currentMonth = ref(new Date().getMonth() + 1)\n\n// New project/task form\nconst newProjectTask = ref({\n  project_id: '',\n  task_id: '',\n  billable: true\n})\n\n// Constants\nconst monthNames = [\n  'Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno',\n  'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'\n]\n\n// Computed\nconst daysInMonth = computed(() => {\n  const year = currentYear.value\n  const month = currentMonth.value\n  const daysCount = new Date(year, month, 0).getDate()\n  return Array.from({ length: daysCount }, (_, i) => i + 1)\n})\n\nconst totalHours = computed(() => {\n  return timesheets.value.reduce((sum, entry) => sum + entry.hours, 0)\n})\n\nconst billableHours = computed(() => {\n  return timesheets.value.reduce((sum, entry) => sum + (entry.billable ? entry.hours : 0), 0)\n})\n\nconst pendingHours = computed(() => {\n  return timesheets.value.reduce((sum, entry) => sum + (entry.status === 'pending' ? entry.hours : 0), 0)\n})\n\nconst activeProjects = computed(() => {\n  return projectTasks.value.length\n})\n\n// Methods\nconst loadProjects = async () => {\n  try {\n    const response = await fetch('/api/projects/', {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (response.ok) {\n      const result = await response.json()\n      projects.value = result.data || []\n    }\n  } catch (err) {\n    console.error('Error loading projects:', err)\n  }\n}\n\nconst loadTasks = async () => {\n  try {\n    const response = await fetch('/api/tasks/', {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (response.ok) {\n      const result = await response.json()\n      tasks.value = result.data || []\n    }\n  } catch (err) {\n    console.error('Error loading tasks:', err)\n  }\n}\n\nconst loadTimesheets = async () => {\n  loading.value = true\n\n  try {\n    // Calculate proper end date for the month\n    const year = currentYear.value\n    const month = currentMonth.value\n    const lastDay = new Date(year, month, 0).getDate()\n\n    const params = new URLSearchParams({\n      start_date: `${year}-${String(month).padStart(2, '0')}-01`,\n      end_date: `${year}-${String(month).padStart(2, '0')}-${String(lastDay).padStart(2, '0')}`\n    })\n\n    const response = await fetch(`/api/timesheets/?${params}`, {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (response.ok) {\n      const result = await response.json()\n      timesheets.value = result.data || []\n\n      // Auto-populate projectTasks from existing timesheet entries\n      autoPopulateProjectTasks()\n    }\n  } catch (err) {\n    console.error('Error loading timesheets:', err)\n  } finally {\n    loading.value = false\n  }\n}\n\nconst previousMonth = () => {\n  if (currentMonth.value === 1) {\n    currentMonth.value = 12\n    currentYear.value--\n  } else {\n    currentMonth.value--\n  }\n  loadTimesheets()\n}\n\nconst nextMonth = () => {\n  if (currentMonth.value === 12) {\n    currentMonth.value = 1\n    currentYear.value++\n  } else {\n    currentMonth.value++\n  }\n  loadTimesheets()\n}\n\n// Auto-populate projectTasks from existing timesheet entries\nconst autoPopulateProjectTasks = () => {\n  console.log('Auto-populating project tasks from', timesheets.value.length, 'timesheet entries')\n\n  const existingProjectTasks = new Set()\n\n  // Extract unique project/task combinations from timesheets\n  timesheets.value.forEach(entry => {\n    const key = `${entry.project_id}-${entry.task_id || 'no-task'}`\n    if (!existingProjectTasks.has(key)) {\n      existingProjectTasks.add(key)\n\n      // Check if this project/task is already in the grid\n      const alreadyExists = projectTasks.value.some(pt =>\n        pt.project_id === entry.project_id &&\n        pt.task_id === entry.task_id\n      )\n\n      if (!alreadyExists) {\n        // Find project name from projects list\n        const project = projects.value.find(p => p.id === entry.project_id)\n        const projectName = project?.name || entry.project?.name || `Progetto ${entry.project_id}`\n\n        // Find task name from tasks list\n        const task = tasks.value.find(t => t.id === entry.task_id)\n        const taskName = task?.name || entry.task?.name || null\n\n        const newProjectTask = {\n          project_id: entry.project_id,\n          task_id: entry.task_id,\n          project_name: projectName,\n          task_name: taskName,\n          billable: entry.billable || true\n        }\n\n        console.log('Adding project task to grid:', newProjectTask)\n        projectTasks.value.push(newProjectTask)\n      }\n    }\n  })\n\n  console.log('Final projectTasks:', projectTasks.value)\n}\n\n// Grid helper methods\nconst isToday = (day) => {\n  const today = new Date()\n  return today.getDate() === day &&\n         today.getMonth() + 1 === currentMonth.value &&\n         today.getFullYear() === currentYear.value\n}\n\nconst isWeekend = (day) => {\n  const date = new Date(currentYear.value, currentMonth.value - 1, day)\n  const dayOfWeek = date.getDay()\n  return dayOfWeek === 0 || dayOfWeek === 6 // Sunday or Saturday\n}\n\nconst getDayName = (day) => {\n  const date = new Date(currentYear.value, currentMonth.value - 1, day)\n  return date.toLocaleDateString('it-IT', { weekday: 'short' }).toUpperCase()\n}\n\nconst getHoursForDay = (projectTask, day) => {\n  const dateStr = `${currentYear.value}-${String(currentMonth.value).padStart(2, '0')}-${String(day).padStart(2, '0')}`\n  const entry = timesheets.value.find(t => {\n    const projectMatch = t.project_id === projectTask.project_id\n    const taskMatch = (t.task_id === projectTask.task_id) ||\n                     (t.task_id === null && projectTask.task_id === null) ||\n                     (t.task_id === undefined && projectTask.task_id === null)\n    const dateMatch = t.date === dateStr\n\n    return projectMatch && taskMatch && dateMatch\n  })\n  return entry ? entry.hours : 0\n}\n\nconst updateHours = (projectTask, day, value) => {\n  const key = `${projectTask.project_id}-${projectTask.task_id || 'no-task'}-${day}`\n  if (value === '' || value === '0') {\n    unsavedChanges.value.delete(key)\n  } else {\n    unsavedChanges.value.set(key, {\n      projectTask,\n      day,\n      hours: parseFloat(value) || 0\n    })\n  }\n}\n\nconst hasUnsavedChanges = (projectTask, day) => {\n  const key = `${projectTask.project_id}-${projectTask.task_id || 'no-task'}-${day}`\n  return unsavedChanges.value.has(key)\n}\n\nconst saveEntry = async (projectTask, day) => {\n  const key = `${projectTask.project_id}-${projectTask.task_id || 'no-task'}-${day}`\n  const change = unsavedChanges.value.get(key)\n\n  if (!change) return\n\n  const dateStr = `${currentYear.value}-${String(currentMonth.value).padStart(2, '0')}-${String(day).padStart(2, '0')}`\n\n  try {\n    // Find existing entry\n    const existingEntry = timesheets.value.find(t =>\n      t.project_id === projectTask.project_id &&\n      t.task_id === projectTask.task_id &&\n      t.date === dateStr\n    )\n\n    if (change.hours === 0) {\n      // Delete entry if hours is 0\n      if (existingEntry) {\n        await deleteTimesheetEntry(existingEntry.id)\n      }\n    } else {\n      // Create or update entry\n      const entryData = {\n        project_id: projectTask.project_id,\n        task_id: projectTask.task_id || null,\n        date: dateStr,\n        hours: change.hours,\n        billable: projectTask.billable,\n        description: ''\n      }\n\n      if (existingEntry) {\n        await updateTimesheetEntry(existingEntry.id, entryData)\n      } else {\n        await createTimesheetEntry(entryData)\n      }\n    }\n\n    unsavedChanges.value.delete(key)\n    await loadTimesheets()\n  } catch (err) {\n    console.error('Error saving entry:', err)\n    alert('Errore nel salvare le ore. Riprova.')\n  }\n}\n\nconst createTimesheetEntry = async (data) => {\n  const response = await fetch('/api/timesheets/', {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json',\n      'X-CSRFToken': authStore.csrfToken\n    },\n    body: JSON.stringify(data)\n  })\n\n  if (!response.ok) {\n    const error = await response.json()\n    throw new Error(error.message || 'Errore nella creazione')\n  }\n}\n\nconst updateTimesheetEntry = async (id, data) => {\n  const response = await fetch(`/api/timesheets/${id}`, {\n    method: 'PUT',\n    headers: {\n      'Content-Type': 'application/json',\n      'X-CSRFToken': authStore.csrfToken\n    },\n    body: JSON.stringify(data)\n  })\n\n  if (!response.ok) {\n    const error = await response.json()\n    throw new Error(error.message || 'Errore nell\\'aggiornamento')\n  }\n}\n\nconst deleteTimesheetEntry = async (id) => {\n  const response = await fetch(`/api/timesheets/${id}`, {\n    method: 'DELETE',\n    headers: {\n      'Content-Type': 'application/json',\n      'X-CSRFToken': authStore.csrfToken\n    }\n  })\n\n  if (!response.ok) {\n    const error = await response.json()\n    throw new Error(error.message || 'Errore nell\\'eliminazione')\n  }\n}\n\nconst getRowTotal = (projectTask) => {\n  return daysInMonth.value.reduce((sum, day) => {\n    return sum + getHoursForDay(projectTask, day)\n  }, 0)\n}\n\nconst getDayTotal = (day) => {\n  return projectTasks.value.reduce((sum, projectTask) => {\n    return sum + getHoursForDay(projectTask, day)\n  }, 0)\n}\n\n// Project/Task management\nconst loadTasksForProject = async () => {\n  if (!newProjectTask.value.project_id) {\n    availableTasks.value = []\n    return\n  }\n\n  availableTasks.value = tasks.value.filter(task =>\n    task.project_id === parseInt(newProjectTask.value.project_id)\n  )\n}\n\nconst addProjectToGrid = () => {\n  const project = projects.value.find(p => p.id === parseInt(newProjectTask.value.project_id))\n  const task = newProjectTask.value.task_id ?\n    availableTasks.value.find(t => t.id === parseInt(newProjectTask.value.task_id)) : null\n\n  const projectTaskKey = `${newProjectTask.value.project_id}-${newProjectTask.value.task_id || 'no-task'}`\n\n  // Check if already exists\n  if (projectTasks.value.some(pt =>\n    pt.project_id === parseInt(newProjectTask.value.project_id) &&\n    pt.task_id === (newProjectTask.value.task_id ? parseInt(newProjectTask.value.task_id) : null)\n  )) {\n    alert('Questo progetto/task è già presente nella griglia')\n    return\n  }\n\n  projectTasks.value.push({\n    project_id: parseInt(newProjectTask.value.project_id),\n    task_id: newProjectTask.value.task_id ? parseInt(newProjectTask.value.task_id) : null,\n    project_name: project.name,\n    task_name: task?.name || null,\n    billable: newProjectTask.value.billable\n  })\n\n  // Reset form\n  newProjectTask.value = {\n    project_id: '',\n    task_id: '',\n    billable: true\n  }\n  availableTasks.value = []\n  showAddProjectModal.value = false\n}\n\nconst removeProjectTask = (projectTask) => {\n  if (confirm('Rimuovere questo progetto/task dalla griglia? Le ore registrate non verranno eliminate.')) {\n    const index = projectTasks.value.findIndex(pt =>\n      pt.project_id === projectTask.project_id && pt.task_id === projectTask.task_id\n    )\n    if (index !== -1) {\n      projectTasks.value.splice(index, 1)\n    }\n  }\n}\n\nconst formatHours = (hours) => {\n  if (!hours || hours === 0) return '0h'\n  return hours % 1 === 0 ? `${hours}h` : `${hours.toFixed(1)}h`\n}\n\n// Lifecycle\nonMounted(async () => {\n  // Load projects and tasks first, then timesheets\n  await Promise.all([\n    loadProjects(),\n    loadTasks()\n  ])\n  // Now load timesheets and auto-populate grid\n  await loadTimesheets()\n})\n\n// Watchers\nwatch([currentMonth, currentYear], () => {\n  loadTimesheets()\n})\n</script>\n"}
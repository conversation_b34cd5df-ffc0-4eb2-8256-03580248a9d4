{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/stores/projects.js"}, "originalCode": "import { defineStore } from 'pinia'\nimport { ref, computed } from 'vue'\nimport api from '@/utils/api'\n\nexport const useProjectsStore = defineStore('projects', () => {\n  // State\n  const projects = ref([])\n  const currentProject = ref(null)\n  const loading = ref(false)\n  const error = ref(null)\n  \n  // Cache per progetti singoli\n  const projectsCache = ref(new Map())\n  \n  // Pagination\n  const pagination = ref({\n    page: 1,\n    perPage: 20,\n    total: 0,\n    totalPages: 0\n  })\n  \n  // Filters\n  const filters = ref({\n    search: '',\n    status: '',\n    client: '',\n    type: ''\n  })\n\n  // Getters\n  const filteredProjects = computed(() => {\n    let filtered = projects.value\n\n    if (filters.value.search) {\n      const search = filters.value.search.toLowerCase()\n      filtered = filtered.filter(project => \n        project.name.toLowerCase().includes(search) ||\n        project.description?.toLowerCase().includes(search) ||\n        project.client?.name?.toLowerCase().includes(search)\n      )\n    }\n\n    if (filters.value.status) {\n      filtered = filtered.filter(project => project.status === filters.value.status)\n    }\n\n    if (filters.value.client) {\n      filtered = filtered.filter(project => project.client_id === filters.value.client)\n    }\n\n    if (filters.value.type) {\n      filtered = filtered.filter(project => project.project_type === filters.value.type)\n    }\n\n    return filtered\n  })\n\n  const projectsByStatus = computed(() => {\n    const grouped = {}\n    projects.value.forEach(project => {\n      if (!grouped[project.status]) {\n        grouped[project.status] = []\n      }\n      grouped[project.status].push(project)\n    })\n    return grouped\n  })\n\n  // Actions\n  const fetchProjects = async (params = {}) => {\n    loading.value = true\n    error.value = null\n    \n    try {\n      const queryParams = new URLSearchParams({\n        page: params.page || pagination.value.page,\n        per_page: params.perPage || pagination.value.perPage,\n        search: params.search || filters.value.search,\n        status: params.status || filters.value.status,\n        client: params.client || filters.value.client,\n        type: params.type || filters.value.type\n      })\n\n      const response = await api.get(`/api/projects?${queryParams}`)\n      \n      if (response.data.success) {\n        projects.value = response.data.data.projects\n        pagination.value = response.data.data.pagination\n      }\n    } catch (err) {\n      error.value = err.response?.data?.message || 'Errore nel caricamento progetti'\n      console.error('Error fetching projects:', err)\n    } finally {\n      loading.value = false\n    }\n  }\n\n  const fetchProject = async (projectId, forceRefresh = false) => {\n    // Controlla se il progetto è già in cache e non forziamo il refresh\n    if (!forceRefresh && projectsCache.value.has(projectId)) {\n      const cachedProject = projectsCache.value.get(projectId)\n      currentProject.value = cachedProject\n      return cachedProject\n    }\n    \n    loading.value = true\n    error.value = null\n    \n    try {\n      const response = await api.get(`/api/projects/${projectId}`)\n      \n      if (response.data.success) {\n        const project = response.data.data.project\n        currentProject.value = project\n        \n        // Salva nel cache\n        projectsCache.value.set(projectId, project)\n        \n        return project\n      }\n    } catch (err) {\n      error.value = err.response?.data?.message || 'Errore nel caricamento progetto'\n      console.error('Error fetching project:', err)\n      throw err\n    } finally {\n      loading.value = false\n    }\n  }\n\n  const createProject = async (projectData) => {\n    loading.value = true\n    error.value = null\n    \n    try {\n      const response = await api.post('/api/projects', projectData)\n      \n      if (response.data.success) {\n        const newProject = response.data.data.project\n        projects.value.unshift(newProject)\n        return newProject\n      }\n    } catch (err) {\n      error.value = err.response?.data?.message || 'Errore nella creazione progetto'\n      console.error('Error creating project:', err)\n      throw err\n    } finally {\n      loading.value = false\n    }\n  }\n\n  const updateProject = async (projectId, projectData) => {\n    loading.value = true\n    error.value = null\n    \n    try {\n      const response = await api.put(`/api/projects/${projectId}`, projectData)\n      \n      if (response.data.success) {\n        const updatedProject = response.data.data.project\n        \n        // Update in projects list\n        const index = projects.value.findIndex(p => p.id === projectId)\n        if (index !== -1) {\n          projects.value[index] = updatedProject\n        }\n        \n        // Update current project if it's the same\n        if (currentProject.value?.id === projectId) {\n          currentProject.value = updatedProject\n        }\n        \n        // Update cache\n        projectsCache.value.set(projectId, updatedProject)\n        \n        return updatedProject\n      }\n    } catch (err) {\n      error.value = err.response?.data?.message || 'Errore nell\\'aggiornamento progetto'\n      console.error('Error updating project:', err)\n      throw err\n    } finally {\n      loading.value = false\n    }\n  }\n\n  const deleteProject = async (projectId) => {\n    loading.value = true\n    error.value = null\n    \n    try {\n      const response = await api.delete(`/api/projects/${projectId}`)\n      \n      if (response.data.success) {\n        // Remove from projects list\n        projects.value = projects.value.filter(p => p.id !== projectId)\n        \n        // Clear current project if it's the same\n        if (currentProject.value?.id === projectId) {\n          currentProject.value = null\n        }\n        \n        // Remove from cache\n        projectsCache.value.delete(projectId)\n      }\n    } catch (err) {\n      error.value = err.response?.data?.message || 'Errore nell\\'eliminazione progetto'\n      console.error('Error deleting project:', err)\n      throw err\n    } finally {\n      loading.value = false\n    }\n  }\n\n  const setFilters = (newFilters) => {\n    filters.value = { ...filters.value, ...newFilters }\n  }\n\n  const clearFilters = () => {\n    filters.value = {\n      search: '',\n      status: '',\n      client: '',\n      type: ''\n    }\n  }\n\n  const setCurrentProject = (project) => {\n    currentProject.value = project\n  }\n\n  const clearCurrentProject = () => {\n    currentProject.value = null\n  }\n\n  // Cache utilities\n  const clearCache = () => {\n    projectsCache.value.clear()\n  }\n  \n  const refreshProject = async (projectId) => {\n    return await fetchProject(projectId, true)\n  }\n  \n  const getCachedProject = (projectId) => {\n    return projectsCache.value.get(projectId)\n  }\n\n  // Reset store\n  const $reset = () => {\n    projects.value = []\n    currentProject.value = null\n    loading.value = false\n    error.value = null\n    projectsCache.value.clear()\n    pagination.value = {\n      page: 1,\n      perPage: 20,\n      total: 0,\n      totalPages: 0\n    }\n    filters.value = {\n      search: '',\n      status: '',\n      client: '',\n      type: ''\n    }\n  }\n\n  return {\n    // State\n    projects,\n    currentProject,\n    loading,\n    error,\n    pagination,\n    filters,\n    \n    // Getters\n    filteredProjects,\n    projectsByStatus,\n    \n    // Actions\n    fetchProjects,\n    fetchProject,\n    createProject,\n    updateProject,\n    deleteProject,\n    setFilters,\n    clearFilters,\n    setCurrentProject,\n    clearCurrentProject,\n    \n    // Cache utilities\n    clearCache,\n    refreshProject,\n    getCachedProject,\n    \n    $reset\n  }\n})\n", "modifiedCode": "import { defineStore } from 'pinia'\nimport { ref, computed } from 'vue'\nimport api from '@/utils/api'\n\nexport const useProjectsStore = defineStore('projects', () => {\n  // State\n  const projects = ref([])\n  const currentProject = ref(null)\n  const loading = ref(false)\n  const error = ref(null)\n  \n  // Cache per progetti singoli\n  const projectsCache = ref(new Map())\n  \n  // Pagination\n  const pagination = ref({\n    page: 1,\n    perPage: 20,\n    total: 0,\n    totalPages: 0\n  })\n  \n  // Filters\n  const filters = ref({\n    search: '',\n    status: '',\n    client: '',\n    type: ''\n  })\n\n  // Getters\n  const filteredProjects = computed(() => {\n    let filtered = projects.value\n\n    if (filters.value.search) {\n      const search = filters.value.search.toLowerCase()\n      filtered = filtered.filter(project => \n        project.name.toLowerCase().includes(search) ||\n        project.description?.toLowerCase().includes(search) ||\n        project.client?.name?.toLowerCase().includes(search)\n      )\n    }\n\n    if (filters.value.status) {\n      filtered = filtered.filter(project => project.status === filters.value.status)\n    }\n\n    if (filters.value.client) {\n      filtered = filtered.filter(project => project.client_id === filters.value.client)\n    }\n\n    if (filters.value.type) {\n      filtered = filtered.filter(project => project.project_type === filters.value.type)\n    }\n\n    return filtered\n  })\n\n  const projectsByStatus = computed(() => {\n    const grouped = {}\n    projects.value.forEach(project => {\n      if (!grouped[project.status]) {\n        grouped[project.status] = []\n      }\n      grouped[project.status].push(project)\n    })\n    return grouped\n  })\n\n  // Actions\n  const fetchProjects = async (params = {}) => {\n    loading.value = true\n    error.value = null\n    \n    try {\n      const queryParams = new URLSearchParams({\n        page: params.page || pagination.value.page,\n        per_page: params.perPage || pagination.value.perPage,\n        search: params.search || filters.value.search,\n        status: params.status || filters.value.status,\n        client: params.client || filters.value.client,\n        type: params.type || filters.value.type\n      })\n\n      const response = await api.get(`/api/projects?${queryParams}`)\n\n      if (response.data.success) {\n        // Handle both paginated and non-paginated responses\n        if (response.data.data.items) {\n          projects.value = response.data.data.items\n          pagination.value = response.data.data.pagination\n        } else if (response.data.data.projects) {\n          projects.value = response.data.data.projects\n          pagination.value = response.data.data.pagination\n        } else if (Array.isArray(response.data.data)) {\n          projects.value = response.data.data\n          pagination.value = { total: response.data.data.length, page: 1, per_page: response.data.data.length }\n        } else {\n          projects.value = []\n          pagination.value = { total: 0, page: 1, per_page: 10 }\n        }\n      }\n    } catch (err) {\n      error.value = err.response?.data?.message || 'Errore nel caricamento progetti'\n      console.error('Error fetching projects:', err)\n    } finally {\n      loading.value = false\n    }\n  }\n\n  const fetchProject = async (projectId, forceRefresh = false) => {\n    // Controlla se il progetto è già in cache e non forziamo il refresh\n    if (!forceRefresh && projectsCache.value.has(projectId)) {\n      const cachedProject = projectsCache.value.get(projectId)\n      currentProject.value = cachedProject\n      return cachedProject\n    }\n    \n    loading.value = true\n    error.value = null\n    \n    try {\n      const response = await api.get(`/api/projects/${projectId}`)\n      \n      if (response.data.success) {\n        const project = response.data.data.project\n        currentProject.value = project\n        \n        // Salva nel cache\n        projectsCache.value.set(projectId, project)\n        \n        return project\n      }\n    } catch (err) {\n      error.value = err.response?.data?.message || 'Errore nel caricamento progetto'\n      console.error('Error fetching project:', err)\n      throw err\n    } finally {\n      loading.value = false\n    }\n  }\n\n  const createProject = async (projectData) => {\n    loading.value = true\n    error.value = null\n    \n    try {\n      const response = await api.post('/api/projects', projectData)\n      \n      if (response.data.success) {\n        const newProject = response.data.data.project\n        projects.value.unshift(newProject)\n        return newProject\n      }\n    } catch (err) {\n      error.value = err.response?.data?.message || 'Errore nella creazione progetto'\n      console.error('Error creating project:', err)\n      throw err\n    } finally {\n      loading.value = false\n    }\n  }\n\n  const updateProject = async (projectId, projectData) => {\n    loading.value = true\n    error.value = null\n    \n    try {\n      const response = await api.put(`/api/projects/${projectId}`, projectData)\n      \n      if (response.data.success) {\n        const updatedProject = response.data.data.project\n        \n        // Update in projects list\n        const index = projects.value.findIndex(p => p.id === projectId)\n        if (index !== -1) {\n          projects.value[index] = updatedProject\n        }\n        \n        // Update current project if it's the same\n        if (currentProject.value?.id === projectId) {\n          currentProject.value = updatedProject\n        }\n        \n        // Update cache\n        projectsCache.value.set(projectId, updatedProject)\n        \n        return updatedProject\n      }\n    } catch (err) {\n      error.value = err.response?.data?.message || 'Errore nell\\'aggiornamento progetto'\n      console.error('Error updating project:', err)\n      throw err\n    } finally {\n      loading.value = false\n    }\n  }\n\n  const deleteProject = async (projectId) => {\n    loading.value = true\n    error.value = null\n    \n    try {\n      const response = await api.delete(`/api/projects/${projectId}`)\n      \n      if (response.data.success) {\n        // Remove from projects list\n        projects.value = projects.value.filter(p => p.id !== projectId)\n        \n        // Clear current project if it's the same\n        if (currentProject.value?.id === projectId) {\n          currentProject.value = null\n        }\n        \n        // Remove from cache\n        projectsCache.value.delete(projectId)\n      }\n    } catch (err) {\n      error.value = err.response?.data?.message || 'Errore nell\\'eliminazione progetto'\n      console.error('Error deleting project:', err)\n      throw err\n    } finally {\n      loading.value = false\n    }\n  }\n\n  const setFilters = (newFilters) => {\n    filters.value = { ...filters.value, ...newFilters }\n  }\n\n  const clearFilters = () => {\n    filters.value = {\n      search: '',\n      status: '',\n      client: '',\n      type: ''\n    }\n  }\n\n  const setCurrentProject = (project) => {\n    currentProject.value = project\n  }\n\n  const clearCurrentProject = () => {\n    currentProject.value = null\n  }\n\n  // Cache utilities\n  const clearCache = () => {\n    projectsCache.value.clear()\n  }\n  \n  const refreshProject = async (projectId) => {\n    return await fetchProject(projectId, true)\n  }\n  \n  const getCachedProject = (projectId) => {\n    return projectsCache.value.get(projectId)\n  }\n\n  // Reset store\n  const $reset = () => {\n    projects.value = []\n    currentProject.value = null\n    loading.value = false\n    error.value = null\n    projectsCache.value.clear()\n    pagination.value = {\n      page: 1,\n      perPage: 20,\n      total: 0,\n      totalPages: 0\n    }\n    filters.value = {\n      search: '',\n      status: '',\n      client: '',\n      type: ''\n    }\n  }\n\n  return {\n    // State\n    projects,\n    currentProject,\n    loading,\n    error,\n    pagination,\n    filters,\n    \n    // Getters\n    filteredProjects,\n    projectsByStatus,\n    \n    // Actions\n    fetchProjects,\n    fetchProject,\n    createProject,\n    updateProject,\n    deleteProject,\n    setFilters,\n    clearFilters,\n    setCurrentProject,\n    clearCurrentProject,\n    \n    // Cache utilities\n    clearCache,\n    refreshProject,\n    getCachedProject,\n    \n    $reset\n  }\n})\n"}
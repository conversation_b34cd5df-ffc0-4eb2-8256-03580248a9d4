{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetApprovals.vue"}, "originalCode": "<template>\n  <div class=\"space-y-6\">\n    <!-- Header with AI Analysis -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n      <div class=\"flex items-center justify-between\">\n        <div>\n          <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\">Approvazioni Timesheet</h1>\n          <p class=\"mt-1 text-sm text-gray-600 dark:text-gray-400\">\n            Gestisci approvazioni con assistenza AI e operazioni bulk\n          </p>\n        </div>\n        <div class=\"flex items-center space-x-3\">\n          <!-- AI Anomaly Detection -->\n          <button \n            @click=\"runAnomalyDetection\" \n            :disabled=\"analyzingAnomalies\"\n            class=\"bg-purple-600 hover:bg-purple-700 disabled:bg-purple-400 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center\"\n          >\n            <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\" />\n            </svg>\n            {{ analyzingAnomalies ? 'Analizzando...' : 'Rileva Anomalie AI' }}\n          </button>\n          \n          <!-- Bulk Actions -->\n          <div class=\"relative\" v-if=\"selectedTimesheets.length\">\n            <button \n              @click=\"showBulkMenu = !showBulkMenu\" \n              class=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n            >\n              Azioni Multiple ({{ selectedTimesheets.length }})\n            </button>\n            <div v-if=\"showBulkMenu\" class=\"absolute right-0 mt-2 w-48 bg-white dark:bg-gray-700 rounded-md shadow-lg z-10\">\n              <div class=\"py-1\">\n                <button \n                  @click=\"bulkApprove\"\n                  class=\"block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600\"\n                >\n                  Approva Tutti\n                </button>\n                <button \n                  @click=\"bulkReject\"\n                  class=\"block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600\"\n                >\n                  Rifiuta Tutti\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- AI Anomalies Alert -->\n      <div v-if=\"anomalies.length\" class=\"mt-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4\">\n        <div class=\"flex items-start\">\n          <svg class=\"w-5 h-5 text-red-400 mt-0.5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z\" />\n          </svg>\n          <div class=\"ml-3\">\n            <h3 class=\"text-sm font-medium text-red-800 dark:text-red-200\">\n              Anomalie Rilevate ({{ anomalies.length }})\n            </h3>\n            <div class=\"mt-2 space-y-1\">\n              <div v-for=\"anomaly in anomalies\" :key=\"anomaly.id\" class=\"text-sm text-red-700 dark:text-red-300\">\n                • {{ anomaly.user_name }}: {{ anomaly.description }}\n                <button \n                  @click=\"viewAnomalyDetails(anomaly)\"\n                  class=\"ml-2 text-red-600 dark:text-red-400 underline\"\n                >\n                  Dettagli\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Stats Cards -->\n    <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-yellow-100 dark:bg-yellow-900/20 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-yellow-600 dark:text-yellow-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-600 dark:text-gray-400\">Da Approvare</p>\n            <p class=\"text-2xl font-semibold text-gray-900 dark:text-white\">{{ stats.pending }}</p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-green-600 dark:text-green-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-600 dark:text-gray-400\">Approvati</p>\n            <p class=\"text-2xl font-semibold text-gray-900 dark:text-white\">{{ stats.approved }}</p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-red-100 dark:bg-red-900/20 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-red-600 dark:text-red-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z\" />\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-600 dark:text-gray-400\">Con Anomalie</p>\n            <p class=\"text-2xl font-semibold text-gray-900 dark:text-white\">{{ anomalies.length }}</p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-blue-600 dark:text-blue-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-600 dark:text-gray-400\">Ore Totali</p>\n            <p class=\"text-2xl font-semibold text-gray-900 dark:text-white\">{{ stats.totalHours }}h</p>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Filters -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-4\">\n      <div class=\"grid grid-cols-1 md:grid-cols-5 gap-4\">\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Mese</label>\n          <select v-model=\"selectedMonth\" @change=\"loadTimesheets\" class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white\">\n            <option v-for=\"month in months\" :key=\"month.value\" :value=\"month.value\">{{ month.label }}</option>\n          </select>\n        </div>\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Stato</label>\n          <select v-model=\"selectedStatus\" @change=\"loadTimesheets\" class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white\">\n            <option value=\"\">Tutti</option>\n            <option value=\"submitted\">Da Approvare</option>\n            <option value=\"approved\">Approvati</option>\n            <option value=\"rejected\">Rifiutati</option>\n          </select>\n        </div>\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Membro Team</label>\n          <select v-model=\"selectedMember\" @change=\"loadTimesheets\" class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white\">\n            <option value=\"\">Tutti</option>\n            <option v-for=\"member in teamMembers\" :key=\"member.id\" :value=\"member.id\">{{ member.full_name }}</option>\n          </select>\n        </div>\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Anomalie</label>\n          <select v-model=\"showOnlyAnomalies\" @change=\"loadTimesheets\" class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white\">\n            <option :value=\"false\">Tutti</option>\n            <option :value=\"true\">Solo con Anomalie</option>\n          </select>\n        </div>\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Cerca</label>\n          <input \n            v-model=\"searchQuery\" \n            @input=\"loadTimesheets\"\n            type=\"text\" \n            placeholder=\"Nome utente...\"\n            class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white\"\n          >\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue'\nimport { useAuthStore } from '@/stores/auth'\n\nconst authStore = useAuthStore()\n\n// State\nconst timesheets = ref([])\nconst teamMembers = ref([])\nconst anomalies = ref([])\nconst selectedTimesheets = ref([])\nconst loading = ref(false)\nconst analyzingAnomalies = ref(false)\nconst showBulkMenu = ref(false)\n\n// Filters\nconst selectedMonth = ref(new Date().getMonth() + 1)\nconst selectedStatus = ref('submitted')\nconst selectedMember = ref('')\nconst showOnlyAnomalies = ref(false)\nconst searchQuery = ref('')\n\n// Computed\nconst stats = computed(() => {\n  const pending = timesheets.value.filter(t => t.status === 'submitted').length\n  const approved = timesheets.value.filter(t => t.status === 'approved').length\n  const totalHours = timesheets.value.reduce((sum, t) => sum + (t.total_hours || 0), 0)\n  \n  return { pending, approved, totalHours }\n})\n\nconst months = computed(() => [\n  { value: 1, label: 'Gennaio' },\n  { value: 2, label: 'Febbraio' },\n  { value: 3, label: 'Marzo' },\n  { value: 4, label: 'Aprile' },\n  { value: 5, label: 'Maggio' },\n  { value: 6, label: 'Giugno' },\n  { value: 7, label: 'Luglio' },\n  { value: 8, label: 'Agosto' },\n  { value: 9, label: 'Settembre' },\n  { value: 10, label: 'Ottobre' },\n  { value: 11, label: 'Novembre' },\n  { value: 12, label: 'Dicembre' }\n])\n\n// Methods\nconst loadTimesheets = async () => {\n  loading.value = true\n  \n  try {\n    const params = new URLSearchParams({\n      month: selectedMonth.value,\n      year: new Date().getFullYear()\n    })\n    \n    if (selectedStatus.value) params.append('status', selectedStatus.value)\n    if (selectedMember.value) params.append('user_id', selectedMember.value)\n    \n    const response = await fetch(`/api/monthly-timesheets/?${params}`, {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (response.ok) {\n      const result = await response.json()\n      timesheets.value = result.data || []\n    }\n  } catch (err) {\n    console.error('Error loading timesheets:', err)\n  } finally {\n    loading.value = false\n  }\n}\n\nconst loadTeamMembers = async () => {\n  try {\n    const response = await fetch('/api/personnel/team-members', {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (response.ok) {\n      const result = await response.json()\n      teamMembers.value = result.data || []\n    }\n  } catch (err) {\n    console.error('Error loading team members:', err)\n  }\n}\n\nconst runAnomalyDetection = async () => {\n  analyzingAnomalies.value = true\n  \n  try {\n    // Simulate AI analysis with timeout\n    await new Promise(resolve => setTimeout(resolve, 2000))\n    \n    // Mock anomalies for demonstration\n    anomalies.value = [\n      {\n        id: 1,\n        user_name: 'Mario Rossi',\n        description: 'Ore eccessive nel weekend (16h sabato)',\n        confidence: 95,\n        type: 'weekend_overtime'\n      },\n      {\n        id: 2,\n        user_name: 'Giulia Bianchi',\n        description: 'Pattern insolito: 12h consecutive senza pause',\n        confidence: 87,\n        type: 'unusual_pattern'\n      }\n    ]\n  } catch (err) {\n    console.error('Error running anomaly detection:', err)\n  } finally {\n    analyzingAnomalies.value = false\n  }\n}\n\nconst viewAnomalyDetails = (anomaly) => {\n  // Show detailed anomaly information\n  alert(`Anomalia: ${anomaly.description}\\nConfidenza: ${anomaly.confidence}%`)\n}\n\nconst bulkApprove = async () => {\n  // Implement bulk approval\n  console.log('Bulk approve:', selectedTimesheets.value)\n  showBulkMenu.value = false\n}\n\nconst bulkReject = async () => {\n  // Implement bulk rejection\n  console.log('Bulk reject:', selectedTimesheets.value)\n  showBulkMenu.value = false\n}\n\n// Lifecycle\nonMounted(() => {\n  loadTimesheets()\n  loadTeamMembers()\n})\n</script>\n", "modifiedCode": "<template>\n  <div class=\"space-y-6\">\n    <!-- Header with AI Analysis -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n      <div class=\"flex items-center justify-between\">\n        <div>\n          <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\">Approvazioni Timesheet</h1>\n          <p class=\"mt-1 text-sm text-gray-600 dark:text-gray-400\">\n            Gestisci approvazioni con assistenza AI e operazioni bulk\n          </p>\n        </div>\n        <div class=\"flex items-center space-x-3\">\n          <!-- AI Anomaly Detection -->\n          <button \n            @click=\"runAnomalyDetection\" \n            :disabled=\"analyzingAnomalies\"\n            class=\"bg-purple-600 hover:bg-purple-700 disabled:bg-purple-400 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center\"\n          >\n            <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\" />\n            </svg>\n            {{ analyzingAnomalies ? 'Analizzando...' : 'Rileva Anomalie AI' }}\n          </button>\n          \n          <!-- Bulk Actions -->\n          <div class=\"relative\" v-if=\"selectedTimesheets.length\">\n            <button \n              @click=\"showBulkMenu = !showBulkMenu\" \n              class=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n            >\n              Azioni Multiple ({{ selectedTimesheets.length }})\n            </button>\n            <div v-if=\"showBulkMenu\" class=\"absolute right-0 mt-2 w-48 bg-white dark:bg-gray-700 rounded-md shadow-lg z-10\">\n              <div class=\"py-1\">\n                <button \n                  @click=\"bulkApprove\"\n                  class=\"block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600\"\n                >\n                  Approva Tutti\n                </button>\n                <button \n                  @click=\"bulkReject\"\n                  class=\"block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600\"\n                >\n                  Rifiuta Tutti\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- AI Anomalies Alert -->\n      <div v-if=\"anomalies.length\" class=\"mt-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4\">\n        <div class=\"flex items-start\">\n          <svg class=\"w-5 h-5 text-red-400 mt-0.5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z\" />\n          </svg>\n          <div class=\"ml-3\">\n            <h3 class=\"text-sm font-medium text-red-800 dark:text-red-200\">\n              Anomalie Rilevate ({{ anomalies.length }})\n            </h3>\n            <div class=\"mt-2 space-y-1\">\n              <div v-for=\"anomaly in anomalies\" :key=\"anomaly.id\" class=\"text-sm text-red-700 dark:text-red-300\">\n                • {{ anomaly.user_name }}: {{ anomaly.description }}\n                <button \n                  @click=\"viewAnomalyDetails(anomaly)\"\n                  class=\"ml-2 text-red-600 dark:text-red-400 underline\"\n                >\n                  Dettagli\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Stats Cards -->\n    <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-yellow-100 dark:bg-yellow-900/20 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-yellow-600 dark:text-yellow-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-600 dark:text-gray-400\">Da Approvare</p>\n            <p class=\"text-2xl font-semibold text-gray-900 dark:text-white\">{{ stats.pending }}</p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-green-600 dark:text-green-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\" />\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-600 dark:text-gray-400\">Approvati</p>\n            <p class=\"text-2xl font-semibold text-gray-900 dark:text-white\">{{ stats.approved }}</p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-red-100 dark:bg-red-900/20 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-red-600 dark:text-red-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z\" />\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-600 dark:text-gray-400\">Con Anomalie</p>\n            <p class=\"text-2xl font-semibold text-gray-900 dark:text-white\">{{ anomalies.length }}</p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-blue-600 dark:text-blue-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-600 dark:text-gray-400\">Ore Totali</p>\n            <p class=\"text-2xl font-semibold text-gray-900 dark:text-white\">{{ stats.totalHours }}h</p>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Filters -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-4\">\n      <div class=\"grid grid-cols-1 md:grid-cols-5 gap-4\">\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Mese</label>\n          <select v-model=\"selectedMonth\" @change=\"loadTimesheets\" class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white\">\n            <option v-for=\"month in months\" :key=\"month.value\" :value=\"month.value\">{{ month.label }}</option>\n          </select>\n        </div>\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Stato</label>\n          <select v-model=\"selectedStatus\" @change=\"loadTimesheets\" class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white\">\n            <option value=\"\">Tutti</option>\n            <option value=\"submitted\">Da Approvare</option>\n            <option value=\"approved\">Approvati</option>\n            <option value=\"rejected\">Rifiutati</option>\n          </select>\n        </div>\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Membro Team</label>\n          <select v-model=\"selectedMember\" @change=\"loadTimesheets\" class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white\">\n            <option value=\"\">Tutti</option>\n            <option v-for=\"member in teamMembers\" :key=\"member.id\" :value=\"member.id\">{{ member.full_name }}</option>\n          </select>\n        </div>\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Anomalie</label>\n          <select v-model=\"showOnlyAnomalies\" @change=\"loadTimesheets\" class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white\">\n            <option :value=\"false\">Tutti</option>\n            <option :value=\"true\">Solo con Anomalie</option>\n          </select>\n        </div>\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Cerca</label>\n          <input \n            v-model=\"searchQuery\" \n            @input=\"loadTimesheets\"\n            type=\"text\" \n            placeholder=\"Nome utente...\"\n            class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white\"\n          >\n        </div>\n      </div>\n    </div>\n\n    <!-- Timesheet Table -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">Timesheet da Approvare</h3>\n      </div>\n\n      <div class=\"overflow-x-auto\">\n        <table class=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n          <thead class=\"bg-gray-50 dark:bg-gray-700\">\n            <tr>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                <input\n                  type=\"checkbox\"\n                  @change=\"toggleSelectAll\"\n                  :checked=\"selectedTimesheets.length === timesheets.length && timesheets.length > 0\"\n                  class=\"rounded border-gray-300 dark:border-gray-600\"\n                >\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Dipendente\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Periodo\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Ore Totali\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Ore Fatturabili\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Stato\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Sottomesso\n              </th>\n              <th class=\"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Azioni\n              </th>\n            </tr>\n          </thead>\n          <tbody class=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n            <tr v-if=\"loading\">\n              <td colspan=\"8\" class=\"px-6 py-4 text-center text-gray-500 dark:text-gray-400\">\n                Caricamento...\n              </td>\n            </tr>\n            <tr v-else-if=\"timesheets.length === 0\">\n              <td colspan=\"8\" class=\"px-6 py-4 text-center text-gray-500 dark:text-gray-400\">\n                Nessun timesheet trovato\n              </td>\n            </tr>\n            <tr v-else v-for=\"timesheet in timesheets\" :key=\"timesheet.id\"\n                :class=\"{ 'bg-red-50 dark:bg-red-900/10': hasAnomaly(timesheet) }\">\n              <td class=\"px-6 py-4 whitespace-nowrap\">\n                <input\n                  type=\"checkbox\"\n                  :value=\"timesheet.id\"\n                  v-model=\"selectedTimesheets\"\n                  class=\"rounded border-gray-300 dark:border-gray-600\"\n                >\n              </td>\n              <td class=\"px-6 py-4 whitespace-nowrap\">\n                <div class=\"flex items-center\">\n                  <div class=\"flex-shrink-0 h-10 w-10\">\n                    <div class=\"h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center\">\n                      <span class=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                        {{ getInitials(timesheet.user?.full_name) }}\n                      </span>\n                    </div>\n                  </div>\n                  <div class=\"ml-4\">\n                    <div class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {{ timesheet.user?.full_name }}\n                    </div>\n                    <div class=\"text-sm text-gray-500 dark:text-gray-400\">\n                      {{ timesheet.user?.email }}\n                    </div>\n                  </div>\n                  <div v-if=\"hasAnomaly(timesheet)\" class=\"ml-2\">\n                    <svg class=\"w-5 h-5 text-red-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z\" />\n                    </svg>\n                  </div>\n                </div>\n              </td>\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                {{ timesheet.month }}/{{ timesheet.year }}\n              </td>\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                {{ timesheet.total_hours }}h\n              </td>\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                {{ timesheet.billable_hours }}h\n              </td>\n              <td class=\"px-6 py-4 whitespace-nowrap\">\n                <span :class=\"getStatusClass(timesheet.status)\" class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\">\n                  {{ getStatusText(timesheet.status) }}\n                </span>\n              </td>\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">\n                {{ formatDate(timesheet.submission_date) }}\n              </td>\n              <td class=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                <div class=\"flex items-center justify-end space-x-2\">\n                  <button\n                    @click=\"viewDetails(timesheet)\"\n                    class=\"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300\"\n                  >\n                    Dettagli\n                  </button>\n                  <button\n                    v-if=\"timesheet.status === 'submitted'\"\n                    @click=\"approveTimesheet(timesheet)\"\n                    class=\"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300\"\n                  >\n                    Approva\n                  </button>\n                  <button\n                    v-if=\"timesheet.status === 'submitted'\"\n                    @click=\"rejectTimesheet(timesheet)\"\n                    class=\"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300\"\n                  >\n                    Rifiuta\n                  </button>\n                </div>\n              </td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n    </div>\n\n    <!-- Modals -->\n    <div v-if=\"showDetailModal\" class=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n      <div class=\"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white dark:bg-gray-800\">\n        <div class=\"mt-3\">\n          <div class=\"flex items-center justify-between mb-4\">\n            <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n              Dettagli Timesheet - {{ selectedTimesheet?.user?.full_name }}\n            </h3>\n            <button @click=\"showDetailModal = false\" class=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\">\n              <svg class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </button>\n          </div>\n\n          <div class=\"space-y-4\">\n            <div class=\"grid grid-cols-2 gap-4\">\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">Periodo</label>\n                <p class=\"text-sm text-gray-900 dark:text-white\">{{ selectedTimesheet?.month }}/{{ selectedTimesheet?.year }}</p>\n              </div>\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">Stato</label>\n                <span :class=\"getStatusClass(selectedTimesheet?.status)\" class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\">\n                  {{ getStatusText(selectedTimesheet?.status) }}\n                </span>\n              </div>\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">Ore Totali</label>\n                <p class=\"text-sm text-gray-900 dark:text-white\">{{ selectedTimesheet?.total_hours }}h</p>\n              </div>\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">Ore Fatturabili</label>\n                <p class=\"text-sm text-gray-900 dark:text-white\">{{ selectedTimesheet?.billable_hours }}h</p>\n              </div>\n            </div>\n\n            <div v-if=\"selectedTimesheet?.status === 'submitted'\" class=\"flex space-x-3\">\n              <button\n                @click=\"approveTimesheet(selectedTimesheet)\"\n                class=\"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n              >\n                Approva\n              </button>\n              <button\n                @click=\"rejectTimesheet(selectedTimesheet)\"\n                class=\"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n              >\n                Rifiuta\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Reject Modal -->\n    <div v-if=\"showRejectModal\" class=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n      <div class=\"relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 shadow-lg rounded-md bg-white dark:bg-gray-800\">\n        <div class=\"mt-3\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n            Rifiuta Timesheet\n          </h3>\n\n          <div class=\"space-y-4\">\n            <div>\n              <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Motivo del rifiuto *\n              </label>\n              <textarea\n                v-model=\"rejectionReason\"\n                rows=\"4\"\n                class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white\"\n                placeholder=\"Specifica il motivo del rifiuto...\"\n              ></textarea>\n            </div>\n\n            <div class=\"flex space-x-3\">\n              <button\n                @click=\"confirmReject\"\n                :disabled=\"!rejectionReason.trim()\"\n                class=\"bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white px-4 py-2 rounded-md text-sm font-medium\"\n              >\n                Conferma Rifiuto\n              </button>\n              <button\n                @click=\"showRejectModal = false\"\n                class=\"bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium\"\n              >\n                Annulla\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue'\nimport { useAuthStore } from '@/stores/auth'\n\nconst authStore = useAuthStore()\n\n// State\nconst timesheets = ref([])\nconst teamMembers = ref([])\nconst anomalies = ref([])\nconst selectedTimesheets = ref([])\nconst loading = ref(false)\nconst analyzingAnomalies = ref(false)\nconst showBulkMenu = ref(false)\n\n// Filters\nconst selectedMonth = ref(new Date().getMonth() + 1)\nconst selectedStatus = ref('submitted')\nconst selectedMember = ref('')\nconst showOnlyAnomalies = ref(false)\nconst searchQuery = ref('')\n\n// Computed\nconst stats = computed(() => {\n  const pending = timesheets.value.filter(t => t.status === 'submitted').length\n  const approved = timesheets.value.filter(t => t.status === 'approved').length\n  const totalHours = timesheets.value.reduce((sum, t) => sum + (t.total_hours || 0), 0)\n  \n  return { pending, approved, totalHours }\n})\n\nconst months = computed(() => [\n  { value: 1, label: 'Gennaio' },\n  { value: 2, label: 'Febbraio' },\n  { value: 3, label: 'Marzo' },\n  { value: 4, label: 'Aprile' },\n  { value: 5, label: 'Maggio' },\n  { value: 6, label: 'Giugno' },\n  { value: 7, label: 'Luglio' },\n  { value: 8, label: 'Agosto' },\n  { value: 9, label: 'Settembre' },\n  { value: 10, label: 'Ottobre' },\n  { value: 11, label: 'Novembre' },\n  { value: 12, label: 'Dicembre' }\n])\n\n// Methods\nconst loadTimesheets = async () => {\n  loading.value = true\n  \n  try {\n    const params = new URLSearchParams({\n      month: selectedMonth.value,\n      year: new Date().getFullYear()\n    })\n    \n    if (selectedStatus.value) params.append('status', selectedStatus.value)\n    if (selectedMember.value) params.append('user_id', selectedMember.value)\n    \n    const response = await fetch(`/api/monthly-timesheets/?${params}`, {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (response.ok) {\n      const result = await response.json()\n      timesheets.value = result.data || []\n    }\n  } catch (err) {\n    console.error('Error loading timesheets:', err)\n  } finally {\n    loading.value = false\n  }\n}\n\nconst loadTeamMembers = async () => {\n  try {\n    const response = await fetch('/api/personnel/team-members', {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (response.ok) {\n      const result = await response.json()\n      teamMembers.value = result.data || []\n    }\n  } catch (err) {\n    console.error('Error loading team members:', err)\n  }\n}\n\nconst runAnomalyDetection = async () => {\n  analyzingAnomalies.value = true\n  \n  try {\n    // Simulate AI analysis with timeout\n    await new Promise(resolve => setTimeout(resolve, 2000))\n    \n    // Mock anomalies for demonstration\n    anomalies.value = [\n      {\n        id: 1,\n        user_name: 'Mario Rossi',\n        description: 'Ore eccessive nel weekend (16h sabato)',\n        confidence: 95,\n        type: 'weekend_overtime'\n      },\n      {\n        id: 2,\n        user_name: 'Giulia Bianchi',\n        description: 'Pattern insolito: 12h consecutive senza pause',\n        confidence: 87,\n        type: 'unusual_pattern'\n      }\n    ]\n  } catch (err) {\n    console.error('Error running anomaly detection:', err)\n  } finally {\n    analyzingAnomalies.value = false\n  }\n}\n\nconst viewAnomalyDetails = (anomaly) => {\n  // Show detailed anomaly information\n  alert(`Anomalia: ${anomaly.description}\\nConfidenza: ${anomaly.confidence}%`)\n}\n\nconst bulkApprove = async () => {\n  // Implement bulk approval\n  console.log('Bulk approve:', selectedTimesheets.value)\n  showBulkMenu.value = false\n}\n\nconst bulkReject = async () => {\n  // Implement bulk rejection\n  console.log('Bulk reject:', selectedTimesheets.value)\n  showBulkMenu.value = false\n}\n\n// Lifecycle\nonMounted(() => {\n  loadTimesheets()\n  loadTeamMembers()\n})\n</script>\n"}
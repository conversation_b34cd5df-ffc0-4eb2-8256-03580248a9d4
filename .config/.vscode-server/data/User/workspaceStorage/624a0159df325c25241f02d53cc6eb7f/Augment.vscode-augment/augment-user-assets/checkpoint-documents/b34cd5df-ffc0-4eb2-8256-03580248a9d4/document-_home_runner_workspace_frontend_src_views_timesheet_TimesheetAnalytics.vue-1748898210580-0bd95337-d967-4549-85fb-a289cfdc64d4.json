{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetAnalytics.vue"}, "originalCode": "<template>\n  <div class=\"space-y-6\">\n    <!-- Header -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n      <div class=\"flex items-center justify-between\">\n        <div>\n          <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\">Analytics Timesheet</h1>\n          <p class=\"mt-1 text-sm text-gray-600 dark:text-gray-400\">\n            Report avanzati e analisi delle performance del team\n          </p>\n        </div>\n        <div class=\"flex items-center space-x-3\">\n          <button \n            @click=\"exportData\"\n            class=\"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center\"\n          >\n            <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n            </svg>\n            Esporta Report\n          </button>\n          <button \n            @click=\"refreshData\"\n            :disabled=\"loading\"\n            class=\"bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center\"\n          >\n            <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\n            </svg>\n            {{ loading ? 'Caricando...' : 'Aggiorna' }}\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Filters -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-4\">\n      <div class=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Periodo</label>\n          <select v-model=\"selectedPeriod\" @change=\"refreshData\" class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white\">\n            <option value=\"current_month\">Mese Corrente</option>\n            <option value=\"last_month\">Mese Scorso</option>\n            <option value=\"current_quarter\">Trimestre Corrente</option>\n            <option value=\"last_quarter\">Trimestre Scorso</option>\n            <option value=\"current_year\">Anno Corrente</option>\n            <option value=\"custom\">Personalizzato</option>\n          </select>\n        </div>\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Dipartimento</label>\n          <select v-model=\"selectedDepartment\" @change=\"refreshData\" class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white\">\n            <option value=\"\">Tutti</option>\n            <option v-for=\"dept in departments\" :key=\"dept.id\" :value=\"dept.id\">{{ dept.name }}</option>\n          </select>\n        </div>\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Progetto</label>\n          <select v-model=\"selectedProject\" @change=\"refreshData\" class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white\">\n            <option value=\"\">Tutti</option>\n            <option v-for=\"project in projects\" :key=\"project.id\" :value=\"project.id\">{{ project.name }}</option>\n          </select>\n        </div>\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Tipo Analisi</label>\n          <select v-model=\"selectedAnalysisType\" @change=\"refreshData\" class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white\">\n            <option value=\"productivity\">Produttività</option>\n            <option value=\"utilization\">Utilizzo</option>\n            <option value=\"billing\">Fatturazione</option>\n            <option value=\"trends\">Trend</option>\n          </select>\n        </div>\n      </div>\n    </div>\n\n    <!-- Key Metrics -->\n    <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-blue-600 dark:text-blue-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-600 dark:text-gray-400\">Ore Totali</p>\n            <p class=\"text-2xl font-semibold text-gray-900 dark:text-white\">{{ metrics.totalHours }}h</p>\n            <p class=\"text-xs text-gray-500 dark:text-gray-400\">\n              <span :class=\"metrics.totalHoursChange >= 0 ? 'text-green-600' : 'text-red-600'\">\n                {{ metrics.totalHoursChange >= 0 ? '+' : '' }}{{ metrics.totalHoursChange }}%\n              </span>\n              vs periodo precedente\n            </p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-green-600 dark:text-green-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-600 dark:text-gray-400\">Produttività Media</p>\n            <p class=\"text-2xl font-semibold text-gray-900 dark:text-white\">{{ metrics.avgProductivity }}%</p>\n            <p class=\"text-xs text-gray-500 dark:text-gray-400\">\n              <span :class=\"metrics.productivityChange >= 0 ? 'text-green-600' : 'text-red-600'\">\n                {{ metrics.productivityChange >= 0 ? '+' : '' }}{{ metrics.productivityChange }}%\n              </span>\n              vs periodo precedente\n            </p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-yellow-100 dark:bg-yellow-900/20 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-yellow-600 dark:text-yellow-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-600 dark:text-gray-400\">Revenue Generato</p>\n            <p class=\"text-2xl font-semibold text-gray-900 dark:text-white\">€{{ formatCurrency(metrics.revenue) }}</p>\n            <p class=\"text-xs text-gray-500 dark:text-gray-400\">\n              <span :class=\"metrics.revenueChange >= 0 ? 'text-green-600' : 'text-red-600'\">\n                {{ metrics.revenueChange >= 0 ? '+' : '' }}{{ metrics.revenueChange }}%\n              </span>\n              vs periodo precedente\n            </p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-purple-600 dark:text-purple-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\" />\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-600 dark:text-gray-400\">Utilizzo Risorse</p>\n            <p class=\"text-2xl font-semibold text-gray-900 dark:text-white\">{{ metrics.utilization }}%</p>\n            <p class=\"text-xs text-gray-500 dark:text-gray-400\">\n              <span :class=\"metrics.utilizationChange >= 0 ? 'text-green-600' : 'text-red-600'\">\n                {{ metrics.utilizationChange >= 0 ? '+' : '' }}{{ metrics.utilizationChange }}%\n              </span>\n              vs periodo precedente\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Charts Section -->\n    <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n      <!-- Productivity Trend Chart -->\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center justify-between mb-4\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">Trend Produttività</h3>\n          <div class=\"flex items-center space-x-2\">\n            <button \n              @click=\"chartPeriod = 'week'\"\n              :class=\"chartPeriod === 'week' ? 'bg-blue-100 text-blue-700' : 'text-gray-500'\"\n              class=\"px-3 py-1 rounded text-sm\"\n            >\n              Settimana\n            </button>\n            <button \n              @click=\"chartPeriod = 'month'\"\n              :class=\"chartPeriod === 'month' ? 'bg-blue-100 text-blue-700' : 'text-gray-500'\"\n              class=\"px-3 py-1 rounded text-sm\"\n            >\n              Mese\n            </button>\n          </div>\n        </div>\n        <div class=\"h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded\">\n          <p class=\"text-gray-500 dark:text-gray-400\">Grafico Produttività (Chart.js placeholder)</p>\n        </div>\n      </div>\n\n      <!-- Team Performance Chart -->\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <h3 class=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">Performance Team</h3>\n        <div class=\"h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded\">\n          <p class=\"text-gray-500 dark:text-gray-400\">Grafico Performance Team (Chart.js placeholder)</p>\n        </div>\n      </div>\n    </div>\n\n    <!-- Detailed Analytics Table -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">Analisi Dettagliata per Dipendente</h3>\n      </div>\n      \n      <div class=\"overflow-x-auto\">\n        <table class=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n          <thead class=\"bg-gray-50 dark:bg-gray-700\">\n            <tr>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Dipendente\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Ore Lavorate\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Ore Fatturabili\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Produttività\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Revenue\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Progetti Attivi\n              </th>\n            </tr>\n          </thead>\n          <tbody class=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n            <tr v-if=\"loading\">\n              <td colspan=\"6\" class=\"px-6 py-4 text-center text-gray-500 dark:text-gray-400\">\n                Caricamento dati analytics...\n              </td>\n            </tr>\n            <tr v-else-if=\"analyticsData.length === 0\">\n              <td colspan=\"6\" class=\"px-6 py-4 text-center text-gray-500 dark:text-gray-400\">\n                Nessun dato disponibile per il periodo selezionato\n              </td>\n            </tr>\n            <tr v-else v-for=\"employee in analyticsData\" :key=\"employee.id\">\n              <td class=\"px-6 py-4 whitespace-nowrap\">\n                <div class=\"flex items-center\">\n                  <div class=\"flex-shrink-0 h-10 w-10\">\n                    <div class=\"h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center\">\n                      <span class=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                        {{ getInitials(employee.full_name) }}\n                      </span>\n                    </div>\n                  </div>\n                  <div class=\"ml-4\">\n                    <div class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {{ employee.full_name }}\n                    </div>\n                    <div class=\"text-sm text-gray-500 dark:text-gray-400\">\n                      {{ employee.department }}\n                    </div>\n                  </div>\n                </div>\n              </td>\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                {{ employee.total_hours }}h\n              </td>\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                {{ employee.billable_hours }}h\n                <span class=\"text-xs text-gray-500 dark:text-gray-400\">\n                  ({{ Math.round((employee.billable_hours / employee.total_hours) * 100) }}%)\n                </span>\n              </td>\n              <td class=\"px-6 py-4 whitespace-nowrap\">\n                <div class=\"flex items-center\">\n                  <div class=\"flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2\">\n                    <div \n                      class=\"h-2 rounded-full\"\n                      :class=\"getProductivityColor(employee.productivity)\"\n                      :style=\"{ width: employee.productivity + '%' }\"\n                    ></div>\n                  </div>\n                  <span class=\"text-sm text-gray-900 dark:text-white\">{{ employee.productivity }}%</span>\n                </div>\n              </td>\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                €{{ formatCurrency(employee.revenue) }}\n              </td>\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                {{ employee.active_projects }}\n              </td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue'\nimport { useAuthStore } from '@/stores/auth'\n\nconst authStore = useAuthStore()\n\n// State\nconst loading = ref(false)\nconst departments = ref([])\nconst projects = ref([])\nconst analyticsData = ref([])\nconst chartPeriod = ref('month')\n\n// Filters\nconst selectedPeriod = ref('current_month')\nconst selectedDepartment = ref('')\nconst selectedProject = ref('')\nconst selectedAnalysisType = ref('productivity')\n\n// Metrics data\nconst metrics = ref({\n  totalHours: 0,\n  totalHoursChange: 0,\n  avgProductivity: 0,\n  productivityChange: 0,\n  revenue: 0,\n  revenueChange: 0,\n  utilization: 0,\n  utilizationChange: 0\n})\n\n// Methods\nconst refreshData = async () => {\n  loading.value = true\n  \n  try {\n    await Promise.all([\n      loadAnalyticsData(),\n      loadDepartments(),\n      loadProjects()\n    ])\n  } finally {\n    loading.value = false\n  }\n}\n\nconst loadAnalyticsData = async () => {\n  try {\n    // Calculate date range based on selected period\n    const { startDate, endDate } = getDateRange()\n\n    const params = new URLSearchParams({\n      start_date: startDate,\n      end_date: endDate\n    })\n\n    if (selectedDepartment.value) {\n      params.append('department_id', selectedDepartment.value)\n    }\n\n    if (selectedProject.value) {\n      params.append('project_id', selectedProject.value)\n    }\n\n    const response = await fetch(`/api/timesheets/?${params}`, {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (response.ok) {\n      const result = await response.json()\n      const timesheets = result.data || []\n\n      // Group by user and calculate analytics\n      const userAnalytics = {}\n\n      timesheets.forEach(entry => {\n        const userId = entry.user_id\n        if (!userAnalytics[userId]) {\n          userAnalytics[userId] = {\n            id: userId,\n            full_name: entry.user?.full_name || 'Unknown User',\n            department: entry.user?.department || 'N/A',\n            total_hours: 0,\n            billable_hours: 0,\n            projects: new Set(),\n            entries: []\n          }\n        }\n\n        userAnalytics[userId].total_hours += entry.hours\n        if (entry.billable) {\n          userAnalytics[userId].billable_hours += entry.hours\n        }\n        if (entry.project_id) {\n          userAnalytics[userId].projects.add(entry.project_id)\n        }\n        userAnalytics[userId].entries.push(entry)\n      })\n\n      // Convert to array and calculate additional metrics\n      analyticsData.value = Object.values(userAnalytics).map(user => ({\n        ...user,\n        active_projects: user.projects.size,\n        productivity: user.total_hours > 0 ? (user.billable_hours / user.total_hours) * 100 : 0,\n        revenue: user.billable_hours * 50 // Assuming €50/hour average rate\n      }))\n    } else {\n      console.error('Failed to load analytics data')\n      analyticsData.value = []\n    }\n  } catch (err) {\n    console.error('Error loading analytics data:', err)\n    analyticsData.value = []\n  }\n}\n\nconst loadDepartments = async () => {\n  // Mock departments\n  departments.value = [\n    { id: 1, name: 'Sviluppo' },\n    { id: 2, name: 'Design' },\n    { id: 3, name: 'Marketing' }\n  ]\n}\n\nconst loadProjects = async () => {\n  // Mock projects\n  projects.value = [\n    { id: 1, name: 'Progetto Alpha' },\n    { id: 2, name: 'Progetto Beta' },\n    { id: 3, name: 'Progetto Gamma' }\n  ]\n}\n\nconst exportData = () => {\n  // Implement CSV export\n  alert('Funzionalità di export in sviluppo')\n}\n\nconst getInitials = (fullName) => {\n  if (!fullName) return '??'\n  return fullName.split(' ').map(n => n[0]).join('').toUpperCase()\n}\n\nconst getProductivityColor = (productivity) => {\n  if (productivity >= 90) return 'bg-green-500'\n  if (productivity >= 75) return 'bg-yellow-500'\n  return 'bg-red-500'\n}\n\nconst formatCurrency = (amount) => {\n  return new Intl.NumberFormat('it-IT').format(amount)\n}\n\n// Lifecycle\nonMounted(() => {\n  refreshData()\n})\n</script>\n", "modifiedCode": "<template>\n  <div class=\"space-y-6\">\n    <!-- Header -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n      <div class=\"flex items-center justify-between\">\n        <div>\n          <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\">Analytics Timesheet</h1>\n          <p class=\"mt-1 text-sm text-gray-600 dark:text-gray-400\">\n            Report avanzati e analisi delle performance del team\n          </p>\n        </div>\n        <div class=\"flex items-center space-x-3\">\n          <button \n            @click=\"exportData\"\n            class=\"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center\"\n          >\n            <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n            </svg>\n            Esporta Report\n          </button>\n          <button \n            @click=\"refreshData\"\n            :disabled=\"loading\"\n            class=\"bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center\"\n          >\n            <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\n            </svg>\n            {{ loading ? 'Caricando...' : 'Aggiorna' }}\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Filters -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-4\">\n      <div class=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Periodo</label>\n          <select v-model=\"selectedPeriod\" @change=\"refreshData\" class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white\">\n            <option value=\"current_month\">Mese Corrente</option>\n            <option value=\"last_month\">Mese Scorso</option>\n            <option value=\"current_quarter\">Trimestre Corrente</option>\n            <option value=\"last_quarter\">Trimestre Scorso</option>\n            <option value=\"current_year\">Anno Corrente</option>\n            <option value=\"custom\">Personalizzato</option>\n          </select>\n        </div>\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Dipartimento</label>\n          <select v-model=\"selectedDepartment\" @change=\"refreshData\" class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white\">\n            <option value=\"\">Tutti</option>\n            <option v-for=\"dept in departments\" :key=\"dept.id\" :value=\"dept.id\">{{ dept.name }}</option>\n          </select>\n        </div>\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Progetto</label>\n          <select v-model=\"selectedProject\" @change=\"refreshData\" class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white\">\n            <option value=\"\">Tutti</option>\n            <option v-for=\"project in projects\" :key=\"project.id\" :value=\"project.id\">{{ project.name }}</option>\n          </select>\n        </div>\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Tipo Analisi</label>\n          <select v-model=\"selectedAnalysisType\" @change=\"refreshData\" class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white\">\n            <option value=\"productivity\">Produttività</option>\n            <option value=\"utilization\">Utilizzo</option>\n            <option value=\"billing\">Fatturazione</option>\n            <option value=\"trends\">Trend</option>\n          </select>\n        </div>\n      </div>\n    </div>\n\n    <!-- Key Metrics -->\n    <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-blue-600 dark:text-blue-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-600 dark:text-gray-400\">Ore Totali</p>\n            <p class=\"text-2xl font-semibold text-gray-900 dark:text-white\">{{ metrics.totalHours }}h</p>\n            <p class=\"text-xs text-gray-500 dark:text-gray-400\">\n              <span :class=\"metrics.totalHoursChange >= 0 ? 'text-green-600' : 'text-red-600'\">\n                {{ metrics.totalHoursChange >= 0 ? '+' : '' }}{{ metrics.totalHoursChange }}%\n              </span>\n              vs periodo precedente\n            </p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-green-600 dark:text-green-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-600 dark:text-gray-400\">Produttività Media</p>\n            <p class=\"text-2xl font-semibold text-gray-900 dark:text-white\">{{ metrics.avgProductivity }}%</p>\n            <p class=\"text-xs text-gray-500 dark:text-gray-400\">\n              <span :class=\"metrics.productivityChange >= 0 ? 'text-green-600' : 'text-red-600'\">\n                {{ metrics.productivityChange >= 0 ? '+' : '' }}{{ metrics.productivityChange }}%\n              </span>\n              vs periodo precedente\n            </p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-yellow-100 dark:bg-yellow-900/20 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-yellow-600 dark:text-yellow-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-600 dark:text-gray-400\">Revenue Generato</p>\n            <p class=\"text-2xl font-semibold text-gray-900 dark:text-white\">€{{ formatCurrency(metrics.revenue) }}</p>\n            <p class=\"text-xs text-gray-500 dark:text-gray-400\">\n              <span :class=\"metrics.revenueChange >= 0 ? 'text-green-600' : 'text-red-600'\">\n                {{ metrics.revenueChange >= 0 ? '+' : '' }}{{ metrics.revenueChange }}%\n              </span>\n              vs periodo precedente\n            </p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-purple-600 dark:text-purple-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\" />\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-600 dark:text-gray-400\">Utilizzo Risorse</p>\n            <p class=\"text-2xl font-semibold text-gray-900 dark:text-white\">{{ metrics.utilization }}%</p>\n            <p class=\"text-xs text-gray-500 dark:text-gray-400\">\n              <span :class=\"metrics.utilizationChange >= 0 ? 'text-green-600' : 'text-red-600'\">\n                {{ metrics.utilizationChange >= 0 ? '+' : '' }}{{ metrics.utilizationChange }}%\n              </span>\n              vs periodo precedente\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Charts Section -->\n    <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n      <!-- Productivity Trend Chart -->\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center justify-between mb-4\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">Trend Produttività</h3>\n          <div class=\"flex items-center space-x-2\">\n            <button \n              @click=\"chartPeriod = 'week'\"\n              :class=\"chartPeriod === 'week' ? 'bg-blue-100 text-blue-700' : 'text-gray-500'\"\n              class=\"px-3 py-1 rounded text-sm\"\n            >\n              Settimana\n            </button>\n            <button \n              @click=\"chartPeriod = 'month'\"\n              :class=\"chartPeriod === 'month' ? 'bg-blue-100 text-blue-700' : 'text-gray-500'\"\n              class=\"px-3 py-1 rounded text-sm\"\n            >\n              Mese\n            </button>\n          </div>\n        </div>\n        <div class=\"h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded\">\n          <p class=\"text-gray-500 dark:text-gray-400\">Grafico Produttività (Chart.js placeholder)</p>\n        </div>\n      </div>\n\n      <!-- Team Performance Chart -->\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <h3 class=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">Performance Team</h3>\n        <div class=\"h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded\">\n          <p class=\"text-gray-500 dark:text-gray-400\">Grafico Performance Team (Chart.js placeholder)</p>\n        </div>\n      </div>\n    </div>\n\n    <!-- Detailed Analytics Table -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">Analisi Dettagliata per Dipendente</h3>\n      </div>\n      \n      <div class=\"overflow-x-auto\">\n        <table class=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n          <thead class=\"bg-gray-50 dark:bg-gray-700\">\n            <tr>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Dipendente\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Ore Lavorate\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Ore Fatturabili\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Produttività\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Revenue\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Progetti Attivi\n              </th>\n            </tr>\n          </thead>\n          <tbody class=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n            <tr v-if=\"loading\">\n              <td colspan=\"6\" class=\"px-6 py-4 text-center text-gray-500 dark:text-gray-400\">\n                Caricamento dati analytics...\n              </td>\n            </tr>\n            <tr v-else-if=\"analyticsData.length === 0\">\n              <td colspan=\"6\" class=\"px-6 py-4 text-center text-gray-500 dark:text-gray-400\">\n                Nessun dato disponibile per il periodo selezionato\n              </td>\n            </tr>\n            <tr v-else v-for=\"employee in analyticsData\" :key=\"employee.id\">\n              <td class=\"px-6 py-4 whitespace-nowrap\">\n                <div class=\"flex items-center\">\n                  <div class=\"flex-shrink-0 h-10 w-10\">\n                    <div class=\"h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center\">\n                      <span class=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                        {{ getInitials(employee.full_name) }}\n                      </span>\n                    </div>\n                  </div>\n                  <div class=\"ml-4\">\n                    <div class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {{ employee.full_name }}\n                    </div>\n                    <div class=\"text-sm text-gray-500 dark:text-gray-400\">\n                      {{ employee.department }}\n                    </div>\n                  </div>\n                </div>\n              </td>\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                {{ employee.total_hours }}h\n              </td>\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                {{ employee.billable_hours }}h\n                <span class=\"text-xs text-gray-500 dark:text-gray-400\">\n                  ({{ Math.round((employee.billable_hours / employee.total_hours) * 100) }}%)\n                </span>\n              </td>\n              <td class=\"px-6 py-4 whitespace-nowrap\">\n                <div class=\"flex items-center\">\n                  <div class=\"flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2\">\n                    <div \n                      class=\"h-2 rounded-full\"\n                      :class=\"getProductivityColor(employee.productivity)\"\n                      :style=\"{ width: employee.productivity + '%' }\"\n                    ></div>\n                  </div>\n                  <span class=\"text-sm text-gray-900 dark:text-white\">{{ employee.productivity }}%</span>\n                </div>\n              </td>\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                €{{ formatCurrency(employee.revenue) }}\n              </td>\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                {{ employee.active_projects }}\n              </td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue'\nimport { useAuthStore } from '@/stores/auth'\n\nconst authStore = useAuthStore()\n\n// State\nconst loading = ref(false)\nconst departments = ref([])\nconst projects = ref([])\nconst analyticsData = ref([])\nconst chartPeriod = ref('month')\n\n// Filters\nconst selectedPeriod = ref('current_month')\nconst selectedDepartment = ref('')\nconst selectedProject = ref('')\nconst selectedAnalysisType = ref('productivity')\n\n// Metrics data\nconst metrics = ref({\n  totalHours: 0,\n  totalHoursChange: 0,\n  avgProductivity: 0,\n  productivityChange: 0,\n  revenue: 0,\n  revenueChange: 0,\n  utilization: 0,\n  utilizationChange: 0\n})\n\n// Methods\nconst getDateRange = () => {\n  const today = new Date()\n  let startDate, endDate\n\n  switch (selectedPeriod.value) {\n    case 'current_month':\n      startDate = new Date(today.getFullYear(), today.getMonth(), 1)\n      endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0)\n      break\n    case 'last_month':\n      startDate = new Date(today.getFullYear(), today.getMonth() - 1, 1)\n      endDate = new Date(today.getFullYear(), today.getMonth(), 0)\n      break\n    case 'current_quarter':\n      const currentQuarter = Math.floor(today.getMonth() / 3)\n      startDate = new Date(today.getFullYear(), currentQuarter * 3, 1)\n      endDate = new Date(today.getFullYear(), (currentQuarter + 1) * 3, 0)\n      break\n    case 'last_quarter':\n      const lastQuarter = Math.floor(today.getMonth() / 3) - 1\n      const year = lastQuarter < 0 ? today.getFullYear() - 1 : today.getFullYear()\n      const quarter = lastQuarter < 0 ? 3 : lastQuarter\n      startDate = new Date(year, quarter * 3, 1)\n      endDate = new Date(year, (quarter + 1) * 3, 0)\n      break\n    case 'current_year':\n      startDate = new Date(today.getFullYear(), 0, 1)\n      endDate = new Date(today.getFullYear(), 11, 31)\n      break\n    default:\n      startDate = new Date(today.getFullYear(), today.getMonth(), 1)\n      endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0)\n  }\n\n  return {\n    startDate: startDate.toISOString().split('T')[0],\n    endDate: endDate.toISOString().split('T')[0]\n  }\n}\n\nconst calculateMetrics = () => {\n  const totalHours = analyticsData.value.reduce((sum, user) => sum + user.total_hours, 0)\n  const totalBillableHours = analyticsData.value.reduce((sum, user) => sum + user.billable_hours, 0)\n  const totalRevenue = analyticsData.value.reduce((sum, user) => sum + user.revenue, 0)\n\n  const avgProductivity = analyticsData.value.length > 0\n    ? analyticsData.value.reduce((sum, user) => sum + user.productivity, 0) / analyticsData.value.length\n    : 0\n\n  const utilization = totalHours > 0 ? (totalBillableHours / totalHours) * 100 : 0\n\n  metrics.value = {\n    totalHours: Math.round(totalHours),\n    totalHoursChange: 0, // TODO: Calculate vs previous period\n    avgProductivity: Math.round(avgProductivity),\n    productivityChange: 0, // TODO: Calculate vs previous period\n    revenue: Math.round(totalRevenue),\n    revenueChange: 0, // TODO: Calculate vs previous period\n    utilization: Math.round(utilization),\n    utilizationChange: 0 // TODO: Calculate vs previous period\n  }\n}\n\nconst refreshData = async () => {\n  loading.value = true\n\n  try {\n    await Promise.all([\n      loadAnalyticsData(),\n      loadDepartments(),\n      loadProjects()\n    ])\n    calculateMetrics()\n  } finally {\n    loading.value = false\n  }\n}\n\nconst loadAnalyticsData = async () => {\n  try {\n    // Calculate date range based on selected period\n    const { startDate, endDate } = getDateRange()\n\n    const params = new URLSearchParams({\n      start_date: startDate,\n      end_date: endDate\n    })\n\n    if (selectedDepartment.value) {\n      params.append('department_id', selectedDepartment.value)\n    }\n\n    if (selectedProject.value) {\n      params.append('project_id', selectedProject.value)\n    }\n\n    const response = await fetch(`/api/timesheets/?${params}`, {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (response.ok) {\n      const result = await response.json()\n      const timesheets = result.data || []\n\n      // Group by user and calculate analytics\n      const userAnalytics = {}\n\n      timesheets.forEach(entry => {\n        const userId = entry.user_id\n        if (!userAnalytics[userId]) {\n          userAnalytics[userId] = {\n            id: userId,\n            full_name: entry.user?.full_name || 'Unknown User',\n            department: entry.user?.department || 'N/A',\n            total_hours: 0,\n            billable_hours: 0,\n            projects: new Set(),\n            entries: []\n          }\n        }\n\n        userAnalytics[userId].total_hours += entry.hours\n        if (entry.billable) {\n          userAnalytics[userId].billable_hours += entry.hours\n        }\n        if (entry.project_id) {\n          userAnalytics[userId].projects.add(entry.project_id)\n        }\n        userAnalytics[userId].entries.push(entry)\n      })\n\n      // Convert to array and calculate additional metrics\n      analyticsData.value = Object.values(userAnalytics).map(user => ({\n        ...user,\n        active_projects: user.projects.size,\n        productivity: user.total_hours > 0 ? (user.billable_hours / user.total_hours) * 100 : 0,\n        revenue: user.billable_hours * 50 // Assuming €50/hour average rate\n      }))\n    } else {\n      console.error('Failed to load analytics data')\n      analyticsData.value = []\n    }\n  } catch (err) {\n    console.error('Error loading analytics data:', err)\n    analyticsData.value = []\n  }\n}\n\nconst loadDepartments = async () => {\n  // Mock departments\n  departments.value = [\n    { id: 1, name: 'Sviluppo' },\n    { id: 2, name: 'Design' },\n    { id: 3, name: 'Marketing' }\n  ]\n}\n\nconst loadProjects = async () => {\n  // Mock projects\n  projects.value = [\n    { id: 1, name: 'Progetto Alpha' },\n    { id: 2, name: 'Progetto Beta' },\n    { id: 3, name: 'Progetto Gamma' }\n  ]\n}\n\nconst exportData = () => {\n  // Implement CSV export\n  alert('Funzionalità di export in sviluppo')\n}\n\nconst getInitials = (fullName) => {\n  if (!fullName) return '??'\n  return fullName.split(' ').map(n => n[0]).join('').toUpperCase()\n}\n\nconst getProductivityColor = (productivity) => {\n  if (productivity >= 90) return 'bg-green-500'\n  if (productivity >= 75) return 'bg-yellow-500'\n  return 'bg-red-500'\n}\n\nconst formatCurrency = (amount) => {\n  return new Intl.NumberFormat('it-IT').format(amount)\n}\n\n// Lifecycle\nonMounted(() => {\n  refreshData()\n})\n</script>\n"}
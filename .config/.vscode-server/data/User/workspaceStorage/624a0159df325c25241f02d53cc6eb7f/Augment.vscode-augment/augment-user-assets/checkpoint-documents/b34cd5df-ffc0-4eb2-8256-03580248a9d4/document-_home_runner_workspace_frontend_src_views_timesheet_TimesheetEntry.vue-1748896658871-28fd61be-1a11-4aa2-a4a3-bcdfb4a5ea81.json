{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetEntry.vue"}, "originalCode": "<template>\n  <div class=\"space-y-6\">\n    <!-- Header -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n      <div class=\"flex justify-between items-center\">\n        <div>\n          <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\">Le Mie Ore</h1>\n          <p class=\"mt-1 text-sm text-gray-600 dark:text-gray-400\">\n            Registra le tue ore di lavoro con la griglia mensile\n          </p>\n        </div>\n\n        <!-- Controlli periodo -->\n        <div class=\"flex items-center space-x-4\">\n          <div class=\"flex items-center space-x-2\">\n            <button\n              @click=\"previousMonth\"\n              class=\"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n            >\n              <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 19l-7-7 7-7\"></path>\n              </svg>\n            </button>\n\n            <div class=\"text-center\">\n              <h2 class=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                {{ monthNames[currentMonth - 1] }} {{ currentYear }}\n              </h2>\n            </div>\n\n            <button\n              @click=\"nextMonth\"\n              class=\"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n            >\n              <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\"></path>\n              </svg>\n            </button>\n          </div>\n\n          <button\n            @click=\"showAddProjectModal = true\"\n            class=\"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n          >\n            Aggiungi Progetto\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Griglia Timesheet Mensile -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <div class=\"flex items-center justify-between\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n            Griglia Ore - {{ monthNames[currentMonth - 1] }} {{ currentYear }}\n          </h3>\n          <div class=\"text-sm text-gray-500 dark:text-gray-400\">\n            Totale: {{ formatHours(totalHours) }} | Fatturabili: {{ formatHours(billableHours) }}\n          </div>\n        </div>\n      </div>\n\n      <div class=\"overflow-x-auto\">\n        <table class=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n          <!-- Header con giorni del mese -->\n          <thead class=\"bg-gray-50 dark:bg-gray-700\">\n            <tr>\n              <th class=\"sticky left-0 z-10 bg-gray-50 dark:bg-gray-700 px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-r border-gray-200 dark:border-gray-600\">\n                Progetto/Task\n              </th>\n              <th\n                v-for=\"day in daysInMonth\"\n                :key=\"day\"\n                class=\"px-2 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider min-w-[50px]\"\n                :class=\"{\n                  'bg-blue-50 dark:bg-blue-900/20': isToday(day),\n                  'bg-red-50 dark:bg-red-900/20': isWeekend(day)\n                }\"\n              >\n                <div>{{ day }}</div>\n                <div class=\"text-xs text-gray-400\">{{ getDayName(day) }}</div>\n              </th>\n              <th class=\"px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-l border-gray-200 dark:border-gray-600\">\n                Totale\n              </th>\n            </tr>\n          </thead>\n\n          <!-- Righe progetti/task -->\n          <tbody class=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n            <tr v-if=\"projectTasks.length === 0\">\n              <td :colspan=\"daysInMonth.length + 2\" class=\"px-6 py-8 text-center text-gray-500 dark:text-gray-400\">\n                <div class=\"space-y-2\">\n                  <p>Nessun progetto configurato</p>\n                  <button\n                    @click=\"showAddProjectModal = true\"\n                    class=\"text-primary-600 hover:text-primary-700 dark:text-primary-400\"\n                  >\n                    Aggiungi il tuo primo progetto\n                  </button>\n                </div>\n              </td>\n            </tr>\n\n            <tr v-for=\"projectTask in projectTasks\" :key=\"`${projectTask.project_id}-${projectTask.task_id || 'no-task'}`\">\n              <!-- Colonna progetto/task -->\n              <td class=\"sticky left-0 z-10 bg-white dark:bg-gray-800 px-4 py-3 border-r border-gray-200 dark:border-gray-600\">\n                <div class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  {{ projectTask.project_name }}\n                </div>\n                <div v-if=\"projectTask.task_name\" class=\"text-xs text-gray-500 dark:text-gray-400\">\n                  {{ projectTask.task_name }}\n                </div>\n                <div class=\"flex items-center mt-1 space-x-2\">\n                  <span\n                    class=\"inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium\"\n                    :class=\"projectTask.billable ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'\"\n                  >\n                    {{ projectTask.billable ? 'Fatt.' : 'Int.' }}\n                  </span>\n                  <button\n                    @click=\"removeProjectTask(projectTask)\"\n                    class=\"text-red-400 hover:text-red-600 dark:hover:text-red-300\"\n                    title=\"Rimuovi dalla griglia\"\n                  >\n                    <svg class=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n                    </svg>\n                  </button>\n                </div>\n              </td>\n\n              <!-- Celle ore per ogni giorno -->\n              <td\n                v-for=\"day in daysInMonth\"\n                :key=\"day\"\n                class=\"px-1 py-2 text-center\"\n                :class=\"{\n                  'bg-blue-50 dark:bg-blue-900/20': isToday(day),\n                  'bg-red-50 dark:bg-red-900/20': isWeekend(day)\n                }\"\n              >\n                <input\n                  type=\"number\"\n                  step=\"0.5\"\n                  min=\"0\"\n                  max=\"24\"\n                  :value=\"getHoursForDay(projectTask, day)\"\n                  @input=\"updateHours(projectTask, day, $event.target.value)\"\n                  @blur=\"saveEntry(projectTask, day)\"\n                  class=\"w-12 h-8 text-xs text-center border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:border-primary-500 focus:ring-1 focus:ring-primary-500\"\n                  :class=\"{\n                    'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-300 dark:border-yellow-600': hasUnsavedChanges(projectTask, day),\n                    'bg-green-50 dark:bg-green-900/20 border-green-300 dark:border-green-600': getHoursForDay(projectTask, day) > 0\n                  }\"\n                />\n              </td>\n\n              <!-- Totale riga -->\n              <td class=\"px-4 py-3 text-center text-sm font-medium text-gray-900 dark:text-white border-l border-gray-200 dark:border-gray-600\">\n                {{ formatHours(getRowTotal(projectTask)) }}\n              </td>\n            </tr>\n\n            <!-- Riga totali giornalieri -->\n            <tr class=\"bg-gray-50 dark:bg-gray-700 font-medium\">\n              <td class=\"sticky left-0 z-10 bg-gray-50 dark:bg-gray-700 px-4 py-3 text-sm text-gray-900 dark:text-white border-r border-gray-200 dark:border-gray-600\">\n                Totale Giornaliero\n              </td>\n              <td\n                v-for=\"day in daysInMonth\"\n                :key=\"day\"\n                class=\"px-2 py-3 text-center text-sm text-gray-900 dark:text-white\"\n                :class=\"{\n                  'bg-blue-100 dark:bg-blue-800': isToday(day),\n                  'bg-red-100 dark:bg-red-800': isWeekend(day)\n                }\"\n              >\n                {{ formatHours(getDayTotal(day)) }}\n              </td>\n              <td class=\"px-4 py-3 text-center text-sm font-bold text-gray-900 dark:text-white border-l border-gray-200 dark:border-gray-600\">\n                {{ formatHours(totalHours) }}\n              </td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n    </div>\n\n    <!-- Statistiche -->\n    <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Ore Totali\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(totalHours) }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Ore Fatturabili\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(billableHours) }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                In Attesa\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(pendingHours) }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Progetti Attivi\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ activeProjects }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, watch } from 'vue'\nimport { useAuthStore } from '@/stores/auth'\n\nconst authStore = useAuthStore()\n\n// State\nconst timesheets = ref([])\nconst projects = ref([])\nconst tasks = ref([])\nconst loading = ref(false)\nconst showAddModal = ref(false)\n\n// Filters\nconst selectedProject = ref('')\nconst selectedTask = ref('')\n\n// Date navigation\nconst currentYear = ref(new Date().getFullYear())\nconst currentMonth = ref(new Date().getMonth() + 1)\n\n// Constants\nconst monthNames = [\n  'Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno',\n  'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'\n]\n\n// Computed\nconst filteredTasks = computed(() => {\n  if (!selectedProject.value) return []\n  return tasks.value.filter(task => task.project_id === parseInt(selectedProject.value))\n})\n\nconst totalHours = computed(() => {\n  return timesheets.value.reduce((sum, entry) => sum + entry.hours, 0)\n})\n\nconst billableHours = computed(() => {\n  return timesheets.value.reduce((sum, entry) => sum + (entry.billable ? entry.hours : 0), 0)\n})\n\nconst pendingHours = computed(() => {\n  return timesheets.value.reduce((sum, entry) => sum + (entry.status === 'pending' ? entry.hours : 0), 0)\n})\n\nconst activeProjects = computed(() => {\n  const projectIds = new Set(timesheets.value.map(entry => entry.project_id))\n  return projectIds.size\n})\n\n// Methods\nconst loadProjects = async () => {\n  try {\n    const response = await fetch('/api/projects/', {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n    \n    if (response.ok) {\n      const result = await response.json()\n      projects.value = result.data || []\n    }\n  } catch (err) {\n    console.error('Error loading projects:', err)\n  }\n}\n\nconst loadTasks = async () => {\n  try {\n    const response = await fetch('/api/tasks/', {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n    \n    if (response.ok) {\n      const result = await response.json()\n      tasks.value = result.data || []\n    }\n  } catch (err) {\n    console.error('Error loading tasks:', err)\n  }\n}\n\nconst loadTimesheets = async () => {\n  loading.value = true\n  \n  try {\n    const params = new URLSearchParams({\n      start_date: `${currentYear.value}-${String(currentMonth.value).padStart(2, '0')}-01`,\n      end_date: `${currentYear.value}-${String(currentMonth.value).padStart(2, '0')}-31`\n    })\n    \n    if (selectedProject.value) {\n      params.append('project_id', selectedProject.value)\n    }\n    \n    if (selectedTask.value) {\n      params.append('task_id', selectedTask.value)\n    }\n\n    const response = await fetch(`/api/timesheets/?${params}`, {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (response.ok) {\n      const result = await response.json()\n      timesheets.value = result.data || []\n    }\n  } catch (err) {\n    console.error('Error loading timesheets:', err)\n  } finally {\n    loading.value = false\n  }\n}\n\nconst previousMonth = () => {\n  if (currentMonth.value === 1) {\n    currentMonth.value = 12\n    currentYear.value--\n  } else {\n    currentMonth.value--\n  }\n  loadTimesheets()\n}\n\nconst nextMonth = () => {\n  if (currentMonth.value === 12) {\n    currentMonth.value = 1\n    currentYear.value++\n  } else {\n    currentMonth.value++\n  }\n  loadTimesheets()\n}\n\nconst formatDate = (dateString) => {\n  const date = new Date(dateString)\n  return date.toLocaleDateString('it-IT')\n}\n\nconst formatHours = (hours) => {\n  if (!hours || hours === 0) return '0h'\n  return hours % 1 === 0 ? `${hours}h` : `${hours.toFixed(2)}h`\n}\n\nconst getStatusClass = (status) => {\n  switch (status) {\n    case 'approved':\n      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'\n    case 'rejected':\n      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'\n    default:\n      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'\n  }\n}\n\nconst getStatusText = (status) => {\n  switch (status) {\n    case 'approved':\n      return 'Approvato'\n    case 'rejected':\n      return 'Rifiutato'\n    default:\n      return 'In Attesa'\n  }\n}\n\nconst editEntry = (entry) => {\n  // TODO: Implementare modal di modifica\n  console.log('Edit entry:', entry)\n}\n\nconst deleteEntry = async (entryId) => {\n  if (!confirm('Sei sicuro di voler eliminare questa registrazione?')) return\n  \n  try {\n    const response = await fetch(`/api/timesheets/${entryId}`, {\n      method: 'DELETE',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (response.ok) {\n      await loadTimesheets()\n    }\n  } catch (err) {\n    console.error('Error deleting entry:', err)\n  }\n}\n\n// Watchers\nwatch([selectedProject, selectedTask], () => {\n  loadTimesheets()\n})\n\n// Lifecycle\nonMounted(() => {\n  loadProjects()\n  loadTasks()\n  loadTimesheets()\n})\n</script>\n", "modifiedCode": "<template>\n  <div class=\"space-y-6\">\n    <!-- Header -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n      <div class=\"flex justify-between items-center\">\n        <div>\n          <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\">Le Mie Ore</h1>\n          <p class=\"mt-1 text-sm text-gray-600 dark:text-gray-400\">\n            Registra le tue ore di lavoro con la griglia mensile\n          </p>\n        </div>\n\n        <!-- Controlli periodo -->\n        <div class=\"flex items-center space-x-4\">\n          <div class=\"flex items-center space-x-2\">\n            <button\n              @click=\"previousMonth\"\n              class=\"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n            >\n              <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 19l-7-7 7-7\"></path>\n              </svg>\n            </button>\n\n            <div class=\"text-center\">\n              <h2 class=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                {{ monthNames[currentMonth - 1] }} {{ currentYear }}\n              </h2>\n            </div>\n\n            <button\n              @click=\"nextMonth\"\n              class=\"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n            >\n              <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\"></path>\n              </svg>\n            </button>\n          </div>\n\n          <button\n            @click=\"showAddProjectModal = true\"\n            class=\"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n          >\n            Aggiungi Progetto\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Griglia Timesheet Mensile -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <div class=\"flex items-center justify-between\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n            Griglia Ore - {{ monthNames[currentMonth - 1] }} {{ currentYear }}\n          </h3>\n          <div class=\"text-sm text-gray-500 dark:text-gray-400\">\n            Totale: {{ formatHours(totalHours) }} | Fatturabili: {{ formatHours(billableHours) }}\n          </div>\n        </div>\n      </div>\n\n      <div class=\"overflow-x-auto\">\n        <table class=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n          <!-- Header con giorni del mese -->\n          <thead class=\"bg-gray-50 dark:bg-gray-700\">\n            <tr>\n              <th class=\"sticky left-0 z-10 bg-gray-50 dark:bg-gray-700 px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-r border-gray-200 dark:border-gray-600\">\n                Progetto/Task\n              </th>\n              <th\n                v-for=\"day in daysInMonth\"\n                :key=\"day\"\n                class=\"px-2 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider min-w-[50px]\"\n                :class=\"{\n                  'bg-blue-50 dark:bg-blue-900/20': isToday(day),\n                  'bg-red-50 dark:bg-red-900/20': isWeekend(day)\n                }\"\n              >\n                <div>{{ day }}</div>\n                <div class=\"text-xs text-gray-400\">{{ getDayName(day) }}</div>\n              </th>\n              <th class=\"px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-l border-gray-200 dark:border-gray-600\">\n                Totale\n              </th>\n            </tr>\n          </thead>\n\n          <!-- Righe progetti/task -->\n          <tbody class=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n            <tr v-if=\"projectTasks.length === 0\">\n              <td :colspan=\"daysInMonth.length + 2\" class=\"px-6 py-8 text-center text-gray-500 dark:text-gray-400\">\n                <div class=\"space-y-2\">\n                  <p>Nessun progetto configurato</p>\n                  <button\n                    @click=\"showAddProjectModal = true\"\n                    class=\"text-primary-600 hover:text-primary-700 dark:text-primary-400\"\n                  >\n                    Aggiungi il tuo primo progetto\n                  </button>\n                </div>\n              </td>\n            </tr>\n\n            <tr v-for=\"projectTask in projectTasks\" :key=\"`${projectTask.project_id}-${projectTask.task_id || 'no-task'}`\">\n              <!-- Colonna progetto/task -->\n              <td class=\"sticky left-0 z-10 bg-white dark:bg-gray-800 px-4 py-3 border-r border-gray-200 dark:border-gray-600\">\n                <div class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  {{ projectTask.project_name }}\n                </div>\n                <div v-if=\"projectTask.task_name\" class=\"text-xs text-gray-500 dark:text-gray-400\">\n                  {{ projectTask.task_name }}\n                </div>\n                <div class=\"flex items-center mt-1 space-x-2\">\n                  <span\n                    class=\"inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium\"\n                    :class=\"projectTask.billable ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'\"\n                  >\n                    {{ projectTask.billable ? 'Fatt.' : 'Int.' }}\n                  </span>\n                  <button\n                    @click=\"removeProjectTask(projectTask)\"\n                    class=\"text-red-400 hover:text-red-600 dark:hover:text-red-300\"\n                    title=\"Rimuovi dalla griglia\"\n                  >\n                    <svg class=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n                    </svg>\n                  </button>\n                </div>\n              </td>\n\n              <!-- Celle ore per ogni giorno -->\n              <td\n                v-for=\"day in daysInMonth\"\n                :key=\"day\"\n                class=\"px-1 py-2 text-center\"\n                :class=\"{\n                  'bg-blue-50 dark:bg-blue-900/20': isToday(day),\n                  'bg-red-50 dark:bg-red-900/20': isWeekend(day)\n                }\"\n              >\n                <input\n                  type=\"number\"\n                  step=\"0.5\"\n                  min=\"0\"\n                  max=\"24\"\n                  :value=\"getHoursForDay(projectTask, day)\"\n                  @input=\"updateHours(projectTask, day, $event.target.value)\"\n                  @blur=\"saveEntry(projectTask, day)\"\n                  class=\"w-12 h-8 text-xs text-center border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:border-primary-500 focus:ring-1 focus:ring-primary-500\"\n                  :class=\"{\n                    'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-300 dark:border-yellow-600': hasUnsavedChanges(projectTask, day),\n                    'bg-green-50 dark:bg-green-900/20 border-green-300 dark:border-green-600': getHoursForDay(projectTask, day) > 0\n                  }\"\n                />\n              </td>\n\n              <!-- Totale riga -->\n              <td class=\"px-4 py-3 text-center text-sm font-medium text-gray-900 dark:text-white border-l border-gray-200 dark:border-gray-600\">\n                {{ formatHours(getRowTotal(projectTask)) }}\n              </td>\n            </tr>\n\n            <!-- Riga totali giornalieri -->\n            <tr class=\"bg-gray-50 dark:bg-gray-700 font-medium\">\n              <td class=\"sticky left-0 z-10 bg-gray-50 dark:bg-gray-700 px-4 py-3 text-sm text-gray-900 dark:text-white border-r border-gray-200 dark:border-gray-600\">\n                Totale Giornaliero\n              </td>\n              <td\n                v-for=\"day in daysInMonth\"\n                :key=\"day\"\n                class=\"px-2 py-3 text-center text-sm text-gray-900 dark:text-white\"\n                :class=\"{\n                  'bg-blue-100 dark:bg-blue-800': isToday(day),\n                  'bg-red-100 dark:bg-red-800': isWeekend(day)\n                }\"\n              >\n                {{ formatHours(getDayTotal(day)) }}\n              </td>\n              <td class=\"px-4 py-3 text-center text-sm font-bold text-gray-900 dark:text-white border-l border-gray-200 dark:border-gray-600\">\n                {{ formatHours(totalHours) }}\n              </td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n    </div>\n\n    <!-- Statistiche -->\n    <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Ore Totali\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(totalHours) }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Ore Fatturabili\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(billableHours) }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                In Attesa\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(pendingHours) }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Progetti Attivi\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ activeProjects }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Modal Aggiungi Progetto -->\n    <div v-if=\"showAddProjectModal\" class=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n      <div class=\"relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 shadow-lg rounded-md bg-white dark:bg-gray-800\">\n        <div class=\"mt-3\">\n          <div class=\"flex items-center justify-between mb-4\">\n            <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n              Aggiungi Progetto alla Griglia\n            </h3>\n            <button @click=\"showAddProjectModal = false\" class=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\">\n              <svg class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </button>\n          </div>\n\n          <div class=\"space-y-4\">\n            <div>\n              <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Progetto *\n              </label>\n              <select\n                v-model=\"newProjectTask.project_id\"\n                @change=\"loadTasksForProject\"\n                class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white\"\n              >\n                <option value=\"\">Seleziona progetto</option>\n                <option v-for=\"project in projects\" :key=\"project.id\" :value=\"project.id\">\n                  {{ project.name }}\n                </option>\n              </select>\n            </div>\n\n            <div v-if=\"newProjectTask.project_id\">\n              <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Task (opzionale)\n              </label>\n              <select\n                v-model=\"newProjectTask.task_id\"\n                class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white\"\n              >\n                <option value=\"\">Nessun task specifico</option>\n                <option v-for=\"task in availableTasks\" :key=\"task.id\" :value=\"task.id\">\n                  {{ task.name }}\n                </option>\n              </select>\n            </div>\n\n            <div>\n              <label class=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  v-model=\"newProjectTask.billable\"\n                  class=\"rounded border-gray-300 dark:border-gray-600\"\n                >\n                <span class=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">Ore fatturabili</span>\n              </label>\n            </div>\n\n            <div class=\"flex space-x-3\">\n              <button\n                @click=\"addProjectToGrid\"\n                :disabled=\"!newProjectTask.project_id\"\n                class=\"bg-primary-600 hover:bg-primary-700 disabled:bg-primary-400 text-white px-4 py-2 rounded-md text-sm font-medium\"\n              >\n                Aggiungi alla Griglia\n              </button>\n              <button\n                @click=\"showAddProjectModal = false\"\n                class=\"bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium\"\n              >\n                Annulla\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, watch } from 'vue'\nimport { useAuthStore } from '@/stores/auth'\n\nconst authStore = useAuthStore()\n\n// State\nconst timesheets = ref([])\nconst projects = ref([])\nconst tasks = ref([])\nconst loading = ref(false)\nconst showAddModal = ref(false)\n\n// Filters\nconst selectedProject = ref('')\nconst selectedTask = ref('')\n\n// Date navigation\nconst currentYear = ref(new Date().getFullYear())\nconst currentMonth = ref(new Date().getMonth() + 1)\n\n// Constants\nconst monthNames = [\n  'Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno',\n  'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'\n]\n\n// Computed\nconst filteredTasks = computed(() => {\n  if (!selectedProject.value) return []\n  return tasks.value.filter(task => task.project_id === parseInt(selectedProject.value))\n})\n\nconst totalHours = computed(() => {\n  return timesheets.value.reduce((sum, entry) => sum + entry.hours, 0)\n})\n\nconst billableHours = computed(() => {\n  return timesheets.value.reduce((sum, entry) => sum + (entry.billable ? entry.hours : 0), 0)\n})\n\nconst pendingHours = computed(() => {\n  return timesheets.value.reduce((sum, entry) => sum + (entry.status === 'pending' ? entry.hours : 0), 0)\n})\n\nconst activeProjects = computed(() => {\n  const projectIds = new Set(timesheets.value.map(entry => entry.project_id))\n  return projectIds.size\n})\n\n// Methods\nconst loadProjects = async () => {\n  try {\n    const response = await fetch('/api/projects/', {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n    \n    if (response.ok) {\n      const result = await response.json()\n      projects.value = result.data || []\n    }\n  } catch (err) {\n    console.error('Error loading projects:', err)\n  }\n}\n\nconst loadTasks = async () => {\n  try {\n    const response = await fetch('/api/tasks/', {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n    \n    if (response.ok) {\n      const result = await response.json()\n      tasks.value = result.data || []\n    }\n  } catch (err) {\n    console.error('Error loading tasks:', err)\n  }\n}\n\nconst loadTimesheets = async () => {\n  loading.value = true\n  \n  try {\n    const params = new URLSearchParams({\n      start_date: `${currentYear.value}-${String(currentMonth.value).padStart(2, '0')}-01`,\n      end_date: `${currentYear.value}-${String(currentMonth.value).padStart(2, '0')}-31`\n    })\n    \n    if (selectedProject.value) {\n      params.append('project_id', selectedProject.value)\n    }\n    \n    if (selectedTask.value) {\n      params.append('task_id', selectedTask.value)\n    }\n\n    const response = await fetch(`/api/timesheets/?${params}`, {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (response.ok) {\n      const result = await response.json()\n      timesheets.value = result.data || []\n    }\n  } catch (err) {\n    console.error('Error loading timesheets:', err)\n  } finally {\n    loading.value = false\n  }\n}\n\nconst previousMonth = () => {\n  if (currentMonth.value === 1) {\n    currentMonth.value = 12\n    currentYear.value--\n  } else {\n    currentMonth.value--\n  }\n  loadTimesheets()\n}\n\nconst nextMonth = () => {\n  if (currentMonth.value === 12) {\n    currentMonth.value = 1\n    currentYear.value++\n  } else {\n    currentMonth.value++\n  }\n  loadTimesheets()\n}\n\nconst formatDate = (dateString) => {\n  const date = new Date(dateString)\n  return date.toLocaleDateString('it-IT')\n}\n\nconst formatHours = (hours) => {\n  if (!hours || hours === 0) return '0h'\n  return hours % 1 === 0 ? `${hours}h` : `${hours.toFixed(2)}h`\n}\n\nconst getStatusClass = (status) => {\n  switch (status) {\n    case 'approved':\n      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'\n    case 'rejected':\n      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'\n    default:\n      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'\n  }\n}\n\nconst getStatusText = (status) => {\n  switch (status) {\n    case 'approved':\n      return 'Approvato'\n    case 'rejected':\n      return 'Rifiutato'\n    default:\n      return 'In Attesa'\n  }\n}\n\nconst editEntry = (entry) => {\n  // TODO: Implementare modal di modifica\n  console.log('Edit entry:', entry)\n}\n\nconst deleteEntry = async (entryId) => {\n  if (!confirm('Sei sicuro di voler eliminare questa registrazione?')) return\n  \n  try {\n    const response = await fetch(`/api/timesheets/${entryId}`, {\n      method: 'DELETE',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (response.ok) {\n      await loadTimesheets()\n    }\n  } catch (err) {\n    console.error('Error deleting entry:', err)\n  }\n}\n\n// Watchers\nwatch([selectedProject, selectedTask], () => {\n  loadTimesheets()\n})\n\n// Lifecycle\nonMounted(() => {\n  loadProjects()\n  loadTasks()\n  loadTimesheets()\n})\n</script>\n"}
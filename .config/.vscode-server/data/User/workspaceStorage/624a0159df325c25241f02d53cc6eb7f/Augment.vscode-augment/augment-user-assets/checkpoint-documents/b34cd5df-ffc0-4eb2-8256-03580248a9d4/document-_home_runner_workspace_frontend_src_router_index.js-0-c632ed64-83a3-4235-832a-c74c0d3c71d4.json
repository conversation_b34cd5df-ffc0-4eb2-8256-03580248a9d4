{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/router/index.js"}, "originalCode": "import { createRouter, createWebHistory } from 'vue-router'\nimport { useAuthStore } from '@/stores/auth'\n\n// Layout components\nimport AppLayout from '@/components/layout/AppLayout.vue'\nimport PublicLayout from '@/components/layout/PublicLayout.vue'\n\n// Public views\nimport Home from '@/views/public/Home.vue'\nimport About from '@/views/public/About.vue'\nimport Contact from '@/views/public/Contact.vue'\nimport Services from '@/views/public/Services.vue'\n\n// Auth views\nimport Login from '@/views/auth/Login.vue'\nimport Register from '@/views/auth/Register.vue'\n\n// Protected views\nimport Dashboard from '@/views/dashboard/Dashboard.vue'\nimport Projects from '@/views/projects/Projects.vue'\n\nconst routes = [\n  // Public routes\n  {\n    path: '/',\n    component: PublicLayout,\n    children: [\n      { path: '', name: 'home', component: Home },\n      { path: 'about', name: 'about', component: About },\n      { path: 'contact', name: 'contact', component: Contact },\n      { path: 'services', name: 'services', component: Services }\n    ]\n  },\n\n  // Auth routes\n  {\n    path: '/auth',\n    component: PublicLayout,\n    children: [\n      { path: 'login', name: 'login', component: Login },\n      { path: 'register', name: 'register', component: Register }\n    ]\n  },\n\n  // Protected routes\n  {\n    path: '/app',\n    component: AppLayout,\n    meta: { requiresAuth: true },\n    children: [\n      { path: '', redirect: '/app/dashboard' },\n      {\n        path: 'dashboard',\n        name: 'dashboard',\n        component: Dashboard,\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'view_dashboard'\n        }\n      },\n      {\n        path: 'projects',\n        name: 'projects',\n        component: Projects,\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'view_all_projects'\n        }\n      },\n      {\n        path: 'projects/create',\n        name: 'projects-create',\n        component: () => import('@/views/projects/ProjectCreate.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'edit_project'\n        }\n      },\n      {\n        path: 'projects/:id',\n        name: 'project-view',\n        component: () => import('@/views/projects/ProjectView.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'view_all_projects'\n        }\n      },\n      {\n        path: 'projects/:id/edit',\n        name: 'project-edit',\n        component: () => import('@/views/projects/ProjectEdit.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'edit_project'\n        }\n      },\n      // Timesheet routes\n      {\n        path: 'timesheet',\n        redirect: '/app/timesheet/entry'\n      },\n      {\n        path: 'timesheet/entry',\n        name: 'timesheet-entry',\n        component: () => import('@/views/timesheet/TimesheetEntry.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'manage_timesheets'\n        }\n      },\n      {\n        path: 'timesheet/requests',\n        name: 'timesheet-requests',\n        component: () => import('@/views/timesheet/TimesheetRequests.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'manage_timesheets'\n        }\n      },\n      {\n        path: 'timesheet/dashboard',\n        name: 'timesheet-dashboard',\n        component: () => import('@/views/timesheet/TimesheetDashboard.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'view_all_projects'\n        }\n      },\n      {\n        path: 'timesheet/history',\n        name: 'timesheet-history',\n        component: () => import('@/views/timesheet/TimesheetHistory.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'manage_timesheets'\n        }\n      },\n      {\n        path: 'timesheet/status',\n        name: 'timesheet-status',\n        component: () => import('@/views/timesheet/TimesheetStatus.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'manage_timesheets'\n        }\n      },\n      {\n        path: 'timesheet/approvals',\n        name: 'timesheet-approvals',\n        component: () => import('@/views/timesheet/TimesheetApprovals.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'manage_users'\n        }\n      },\n      {\n        path: 'timesheet/analytics',\n        name: 'timesheet-analytics',\n        component: () => import('@/views/timesheet/TimesheetAnalytics.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'admin_access'\n        }\n      },\n      // Personnel routes\n      {\n        path: 'personnel',\n        name: 'personnel',\n        component: () => import('@/views/personnel/PersonnelDirectory.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'view_personnel_data'\n        }\n      },\n      {\n        path: 'personnel/orgchart',\n        name: 'personnel-orgchart',\n        component: () => import('@/views/personnel/PersonnelOrgChart.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'view_personnel_data'\n        }\n      },\n      {\n        path: 'personnel/skills',\n        name: 'personnel-skills',\n        component: () => import('@/views/personnel/SkillsMatrix.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'view_personnel_data'\n        }\n      },\n      {\n        path: 'personnel/departments',\n        name: 'personnel-departments',\n        component: () => import('@/views/personnel/DepartmentList.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'manage_users'\n        }\n      },\n      {\n        path: 'personnel/departments/create',\n        name: 'department-create',\n        component: () => import('@/views/personnel/DepartmentCreate.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'manage_users'\n        }\n      },\n      {\n        path: 'personnel/departments/:id',\n        name: 'department-view',\n        component: () => import('@/views/personnel/DepartmentView.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'view_personnel_data'\n        }\n      },\n      {\n        path: 'personnel/departments/:id/edit',\n        name: 'department-edit',\n        component: () => import('@/views/personnel/DepartmentEdit.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'manage_users'\n        }\n      },\n      {\n        path: 'personnel/allocation',\n        name: 'personnel-allocation',\n        component: () => import('@/views/personnel/PersonnelAllocation.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'view_personnel_data'\n        }\n      },\n      {\n        path: 'personnel/admin',\n        name: 'personnel-admin',\n        component: () => import('@/views/personnel/PersonnelAdmin.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'admin_access'\n        }\n      },\n      {\n        path: 'personnel/:id',\n        name: 'personnel-profile',\n        component: () => import('@/views/personnel/PersonnelProfile.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'view_personnel_data'\n        }\n      },\n      {\n        path: 'admin',\n        redirect: '/app/admin/users'\n      },\n      {\n        path: 'admin/users',\n        name: 'admin-users',\n        component: () => import('@/views/admin/Admin.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'admin_access'\n        }\n      },\n      {\n        path: 'admin/kpi-templates',\n        name: 'admin-kpi-templates',\n        component: () => import('@/views/admin/KPITemplates.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'admin_access'\n        }\n      },\n      {\n        path: 'profile',\n        name: 'profile',\n        component: () => import('@/views/user/Profile.vue'),\n        meta: {\n          requiresAuth: true\n        }\n      },\n      {\n        path: 'settings',\n        name: 'settings',\n        component: () => import('@/views/user/Settings.vue'),\n        meta: {\n          requiresAuth: true\n        }\n      }\n    ]\n  }\n]\n\nconst router = createRouter({\n  history: createWebHistory(),\n  routes\n})\n\n// Navigation guard per autenticazione e autorizzazioni\nrouter.beforeEach(async (to, from, next) => {\n  const authStore = useAuthStore()\n\n  if (to.meta.requiresAuth) {\n    // Controlla l'autenticazione se non è già stata verificata\n    if (!authStore.sessionChecked) {\n      await authStore.initializeAuth()\n    }\n\n    if (!authStore.isAuthenticated) {\n      next('/auth/login')\n      return\n    }\n\n    // Controllo permessi se specificato nella route\n    if (to.meta.requiredPermission) {\n      if (!authStore.hasPermission(to.meta.requiredPermission)) {\n        // Redirect a pagina di accesso negato o dashboard\n        console.warn(`Accesso negato a ${to.path}: permesso '${to.meta.requiredPermission}' richiesto`)\n        next('/app/dashboard')\n        return\n      }\n    }\n  }\n\n  next()\n})\n\nexport default router", "modifiedCode": "import { createRouter, createWebHistory } from 'vue-router'\nimport { useAuthStore } from '@/stores/auth'\n\n// Layout components\nimport AppLayout from '@/components/layout/AppLayout.vue'\nimport PublicLayout from '@/components/layout/PublicLayout.vue'\n\n// Public views\nimport Home from '@/views/public/Home.vue'\nimport About from '@/views/public/About.vue'\nimport Contact from '@/views/public/Contact.vue'\nimport Services from '@/views/public/Services.vue'\n\n// Auth views\nimport Login from '@/views/auth/Login.vue'\nimport Register from '@/views/auth/Register.vue'\n\n// Protected views\nimport Dashboard from '@/views/dashboard/Dashboard.vue'\nimport Projects from '@/views/projects/Projects.vue'\n\nconst routes = [\n  // Public routes\n  {\n    path: '/',\n    component: PublicLayout,\n    children: [\n      { path: '', name: 'home', component: Home },\n      { path: 'about', name: 'about', component: About },\n      { path: 'contact', name: 'contact', component: Contact },\n      { path: 'services', name: 'services', component: Services }\n    ]\n  },\n\n  // Auth routes\n  {\n    path: '/auth',\n    component: PublicLayout,\n    children: [\n      { path: 'login', name: 'login', component: Login },\n      { path: 'register', name: 'register', component: Register }\n    ]\n  },\n\n  // Protected routes\n  {\n    path: '/app',\n    component: AppLayout,\n    meta: { requiresAuth: true },\n    children: [\n      { path: '', redirect: '/app/dashboard' },\n      {\n        path: 'dashboard',\n        name: 'dashboard',\n        component: Dashboard,\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'view_dashboard'\n        }\n      },\n      {\n        path: 'projects',\n        name: 'projects',\n        component: Projects,\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'view_all_projects'\n        }\n      },\n      {\n        path: 'projects/create',\n        name: 'projects-create',\n        component: () => import('@/views/projects/ProjectCreate.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'edit_project'\n        }\n      },\n      {\n        path: 'projects/:id',\n        name: 'project-view',\n        component: () => import('@/views/projects/ProjectView.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'view_all_projects'\n        }\n      },\n      {\n        path: 'projects/:id/edit',\n        name: 'project-edit',\n        component: () => import('@/views/projects/ProjectEdit.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'edit_project'\n        }\n      },\n      // Timesheet routes\n      {\n        path: 'timesheet',\n        redirect: '/app/timesheet/entry'\n      },\n      {\n        path: 'timesheet/entry',\n        name: 'timesheet-entry',\n        component: () => import('@/views/timesheet/TimesheetEntry.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'manage_timesheets'\n        }\n      },\n      {\n        path: 'timesheet/requests',\n        name: 'timesheet-requests',\n        component: () => import('@/views/timesheet/TimesheetRequests.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'manage_timesheets'\n        }\n      },\n      {\n        path: 'timesheet/dashboard',\n        name: 'timesheet-dashboard',\n        component: () => import('@/views/timesheet/TimesheetDashboard.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'view_all_projects'\n        }\n      },\n      {\n        path: 'timesheet/history',\n        name: 'timesheet-history',\n        component: () => import('@/views/timesheet/TimesheetHistory.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'manage_timesheets'\n        }\n      },\n      {\n        path: 'timesheet/status',\n        name: 'timesheet-status',\n        component: () => import('@/views/timesheet/TimesheetStatus.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'manage_timesheets'\n        }\n      },\n      {\n        path: 'timesheet/approvals',\n        name: 'timesheet-approvals',\n        component: () => import('@/views/timesheet/TimesheetApprovals.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'manage_users'\n        }\n      },\n      {\n        path: 'timesheet/analytics',\n        name: 'timesheet-analytics',\n        component: () => import('@/views/timesheet/TimesheetAnalytics.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'admin_access'\n        }\n      },\n      // Personnel routes\n      {\n        path: 'personnel',\n        name: 'personnel',\n        component: () => import('@/views/personnel/PersonnelDirectory.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'view_personnel_data'\n        }\n      },\n      {\n        path: 'personnel/orgchart',\n        name: 'personnel-orgchart',\n        component: () => import('@/views/personnel/PersonnelOrgChart.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'view_personnel_data'\n        }\n      },\n      {\n        path: 'personnel/skills',\n        name: 'personnel-skills',\n        component: () => import('@/views/personnel/SkillsMatrix.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'view_personnel_data'\n        }\n      },\n      {\n        path: 'personnel/departments',\n        name: 'personnel-departments',\n        component: () => import('@/views/personnel/DepartmentList.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'manage_users'\n        }\n      },\n      {\n        path: 'personnel/departments/create',\n        name: 'department-create',\n        component: () => import('@/views/personnel/DepartmentCreate.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'manage_users'\n        }\n      },\n      {\n        path: 'personnel/departments/:id',\n        name: 'department-view',\n        component: () => import('@/views/personnel/DepartmentView.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'view_personnel_data'\n        }\n      },\n      {\n        path: 'personnel/departments/:id/edit',\n        name: 'department-edit',\n        component: () => import('@/views/personnel/DepartmentEdit.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'manage_users'\n        }\n      },\n      {\n        path: 'personnel/allocation',\n        name: 'personnel-allocation',\n        component: () => import('@/views/personnel/PersonnelAllocation.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'view_personnel_data'\n        }\n      },\n      {\n        path: 'personnel/admin',\n        name: 'personnel-admin',\n        component: () => import('@/views/personnel/PersonnelAdmin.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'admin_access'\n        }\n      },\n      {\n        path: 'personnel/:id',\n        name: 'personnel-profile',\n        component: () => import('@/views/personnel/PersonnelProfile.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'view_personnel_data'\n        }\n      },\n      {\n        path: 'admin',\n        redirect: '/app/admin/users'\n      },\n      {\n        path: 'admin/users',\n        name: 'admin-users',\n        component: () => import('@/views/admin/Admin.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'admin_access'\n        }\n      },\n      {\n        path: 'admin/kpi-templates',\n        name: 'admin-kpi-templates',\n        component: () => import('@/views/admin/KPITemplates.vue'),\n        meta: {\n          requiresAuth: true,\n          requiredPermission: 'admin_access'\n        }\n      },\n      {\n        path: 'profile',\n        name: 'profile',\n        component: () => import('@/views/user/Profile.vue'),\n        meta: {\n          requiresAuth: true\n        }\n      },\n      {\n        path: 'settings',\n        name: 'settings',\n        component: () => import('@/views/user/Settings.vue'),\n        meta: {\n          requiresAuth: true\n        }\n      }\n    ]\n  }\n]\n\nconst router = createRouter({\n  history: createWebHistory(),\n  routes\n})\n\n// Navigation guard per autenticazione e autorizzazioni\nrouter.beforeEach(async (to, from, next) => {\n  const authStore = useAuthStore()\n\n  if (to.meta.requiresAuth) {\n    // Controlla l'autenticazione se non è già stata verificata\n    if (!authStore.sessionChecked) {\n      await authStore.initializeAuth()\n    }\n\n    if (!authStore.isAuthenticated) {\n      next('/auth/login')\n      return\n    }\n\n    // Controllo permessi se specificato nella route\n    if (to.meta.requiredPermission) {\n      if (!authStore.hasPermission(to.meta.requiredPermission)) {\n        // Redirect a pagina di accesso negato o dashboard\n        console.warn(`Accesso negato a ${to.path}: permesso '${to.meta.requiredPermission}' richiesto`)\n        next('/app/dashboard')\n        return\n      }\n    }\n  }\n\n  next()\n})\n\nexport default router"}
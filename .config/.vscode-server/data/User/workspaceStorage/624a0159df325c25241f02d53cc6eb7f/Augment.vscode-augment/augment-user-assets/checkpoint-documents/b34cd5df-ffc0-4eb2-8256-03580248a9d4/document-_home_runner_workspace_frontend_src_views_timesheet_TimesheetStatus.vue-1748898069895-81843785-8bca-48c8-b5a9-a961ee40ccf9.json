{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetStatus.vue"}, "originalCode": "<template>\n  <div class=\"space-y-6\">\n    <!-- Header -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n      <div class=\"flex justify-between items-center\">\n        <div>\n          <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\">Stato Approvazioni</h1>\n          <p class=\"mt-1 text-sm text-gray-600 dark:text-gray-400\">\n            Monitora lo stato delle tue approvazioni timesheet mensili\n          </p>\n        </div>\n        \n        <div class=\"flex space-x-3\">\n          <button \n            @click=\"generateCurrentMonth\"\n            :disabled=\"generating\"\n            class=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium disabled:opacity-50\"\n          >\n            {{ generating ? 'Generazione...' : 'Genera Mese Corrente' }}\n          </button>\n          <router-link \n            to=\"/app/timesheet/entry\"\n            class=\"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n          >\n            Registra Ore\n          </router-link>\n        </div>\n      </div>\n    </div>\n\n    <!-- Timesheet Corrente -->\n    <div v-if=\"currentMonthTimesheet\" class=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <div class=\"flex justify-between items-center\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n            Timesheet {{ currentMonthTimesheet.month }}/{{ currentMonthTimesheet.year }}\n          </h3>\n          <div class=\"flex items-center space-x-3\">\n            <span \n              class=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium\"\n              :class=\"getStatusClass(currentMonthTimesheet.status)\"\n            >\n              {{ getStatusText(currentMonthTimesheet.status) }}\n            </span>\n            <button \n              v-if=\"currentMonthTimesheet.status === 'draft' && currentMonthTimesheet.total_hours > 0\"\n              @click=\"submitForApproval(currentMonthTimesheet.id)\"\n              :disabled=\"submitting\"\n              class=\"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium disabled:opacity-50\"\n            >\n              {{ submitting ? 'Invio...' : 'Sottometti per Approvazione' }}\n            </button>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"p-6\">\n        <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n          <div class=\"text-center\">\n            <div class=\"text-2xl font-bold text-gray-900 dark:text-white\">\n              {{ formatHours(currentMonthTimesheet.total_hours) }}\n            </div>\n            <div class=\"text-sm text-gray-500 dark:text-gray-400\">Ore Totali</div>\n          </div>\n          <div class=\"text-center\">\n            <div class=\"text-2xl font-bold text-green-600\">\n              {{ formatHours(currentMonthTimesheet.billable_hours) }}\n            </div>\n            <div class=\"text-sm text-gray-500 dark:text-gray-400\">Ore Fatturabili</div>\n          </div>\n          <div class=\"text-center\">\n            <div class=\"text-2xl font-bold text-blue-600\">\n              {{ currentMonthTimesheet.entries_count || 0 }}\n            </div>\n            <div class=\"text-sm text-gray-500 dark:text-gray-400\">Registrazioni</div>\n          </div>\n          <div class=\"text-center\">\n            <div class=\"text-2xl font-bold text-purple-600\">\n              {{ Math.round((currentMonthTimesheet.billable_hours / Math.max(currentMonthTimesheet.total_hours, 1)) * 100) }}%\n            </div>\n            <div class=\"text-sm text-gray-500 dark:text-gray-400\">% Fatturabile</div>\n          </div>\n        </div>\n        \n        <!-- Feedback approvazione -->\n        <div v-if=\"currentMonthTimesheet.rejection_reason\" class=\"mt-6 p-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-lg\">\n          <div class=\"flex\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-5 w-5 text-red-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"></path>\n              </svg>\n            </div>\n            <div class=\"ml-3\">\n              <h3 class=\"text-sm font-medium text-red-800 dark:text-red-200\">\n                Timesheet Rifiutato\n              </h3>\n              <div class=\"mt-2 text-sm text-red-700 dark:text-red-300\">\n                <p>{{ currentMonthTimesheet.rejection_reason }}</p>\n              </div>\n            </div>\n          </div>\n        </div>\n        \n        <!-- Date importanti -->\n        <div v-if=\"currentMonthTimesheet.submission_date || currentMonthTimesheet.approval_date\" class=\"mt-6 border-t border-gray-200 dark:border-gray-600 pt-4\">\n          <dl class=\"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\">\n            <div v-if=\"currentMonthTimesheet.submission_date\">\n              <dt class=\"text-gray-500 dark:text-gray-400\">Data Sottomissione</dt>\n              <dd class=\"text-gray-900 dark:text-white font-medium\">\n                {{ formatDateTime(currentMonthTimesheet.submission_date) }}\n              </dd>\n            </div>\n            <div v-if=\"currentMonthTimesheet.approval_date\">\n              <dt class=\"text-gray-500 dark:text-gray-400\">Data Approvazione</dt>\n              <dd class=\"text-gray-900 dark:text-white font-medium\">\n                {{ formatDateTime(currentMonthTimesheet.approval_date) }}\n              </dd>\n            </div>\n          </dl>\n        </div>\n      </div>\n    </div>\n\n    <!-- Storico Timesheet Mensili -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n          Storico Approvazioni\n        </h3>\n      </div>\n      \n      <div class=\"overflow-x-auto\">\n        <table class=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n          <thead class=\"bg-gray-50 dark:bg-gray-700\">\n            <tr>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Periodo\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Ore Totali\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Ore Fatturabili\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Stato\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Data Sottomissione\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Data Approvazione\n              </th>\n              <th class=\"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Azioni\n              </th>\n            </tr>\n          </thead>\n          <tbody class=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n            <tr v-for=\"timesheet in monthlyTimesheets\" :key=\"timesheet.id\">\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                {{ timesheet.month }}/{{ timesheet.year }}\n              </td>\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                {{ formatHours(timesheet.total_hours) }}\n              </td>\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                {{ formatHours(timesheet.billable_hours) }}\n              </td>\n              <td class=\"px-6 py-4 whitespace-nowrap\">\n                <span \n                  class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\"\n                  :class=\"getStatusClass(timesheet.status)\"\n                >\n                  {{ getStatusText(timesheet.status) }}\n                </span>\n              </td>\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                {{ timesheet.submission_date ? formatDate(timesheet.submission_date) : '-' }}\n              </td>\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                {{ timesheet.approval_date ? formatDate(timesheet.approval_date) : '-' }}\n              </td>\n              <td class=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                <button \n                  @click=\"viewDetails(timesheet)\"\n                  class=\"text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 mr-3\"\n                >\n                  Dettagli\n                </button>\n                <button \n                  v-if=\"timesheet.status === 'draft'\"\n                  @click=\"submitForApproval(timesheet.id)\"\n                  class=\"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300\"\n                >\n                  Sottometti\n                </button>\n                <button \n                  v-else-if=\"timesheet.status === 'rejected'\"\n                  @click=\"reopenTimesheet(timesheet.id)\"\n                  class=\"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300\"\n                >\n                  Riapri\n                </button>\n              </td>\n            </tr>\n          </tbody>\n        </table>\n        \n        <!-- Empty state -->\n        <div v-if=\"monthlyTimesheets.length === 0\" class=\"text-center py-8\">\n          <div class=\"mx-auto h-12 w-12 text-gray-400\">\n            <svg fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\n            </svg>\n          </div>\n          <h3 class=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">Nessun timesheet mensile</h3>\n          <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n            Inizia registrando le tue ore per generare il primo timesheet mensile.\n          </p>\n        </div>\n      </div>\n    </div>\n\n    <!-- Richieste Time-Off -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <div class=\"flex justify-between items-center\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n            Richieste Ferie/Permessi\n          </h3>\n          <router-link \n            to=\"/app/timesheet/requests\"\n            class=\"text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 text-sm\"\n          >\n            Vedi Tutte →\n          </router-link>\n        </div>\n      </div>\n      \n      <div class=\"p-6\">\n        <div class=\"space-y-4\">\n          <div v-for=\"request in recentTimeOffRequests\" :key=\"request.id\" class=\"flex items-center justify-between\">\n            <div class=\"flex items-center space-x-3\">\n              <div class=\"flex-shrink-0\">\n                <span \n                  class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\"\n                  :class=\"getTypeClass(request.request_type)\"\n                >\n                  {{ getTypeText(request.request_type) }}\n                </span>\n              </div>\n              <div>\n                <p class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  {{ formatDate(request.start_date) }} - {{ formatDate(request.end_date) }}\n                </p>\n                <p class=\"text-sm text-gray-500 dark:text-gray-400\">\n                  {{ request.duration_days }} giorni\n                </p>\n              </div>\n            </div>\n            <div class=\"flex items-center space-x-3\">\n              <span \n                class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\"\n                :class=\"getStatusClass(request.status)\"\n              >\n                {{ getStatusText(request.status) }}\n              </span>\n            </div>\n          </div>\n        </div>\n        \n        <div v-if=\"recentTimeOffRequests.length === 0\" class=\"text-center py-4\">\n          <p class=\"text-gray-500 dark:text-gray-400\">Nessuna richiesta recente</p>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue'\nimport { useAuthStore } from '@/stores/auth'\n\nconst authStore = useAuthStore()\n\n// State\nconst monthlyTimesheets = ref([])\nconst currentMonthTimesheet = ref(null)\nconst recentTimeOffRequests = ref([])\nconst loading = ref(false)\nconst generating = ref(false)\nconst submitting = ref(false)\n\n// Methods\nconst loadMonthlyTimesheets = async () => {\n  loading.value = true\n  \n  try {\n    const response = await fetch('/api/monthly-timesheets/', {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (response.ok) {\n      const result = await response.json()\n      monthlyTimesheets.value = result.data || []\n      \n      // Find current month timesheet\n      const currentDate = new Date()\n      const currentYear = currentDate.getFullYear()\n      const currentMonth = currentDate.getMonth() + 1\n      \n      currentMonthTimesheet.value = monthlyTimesheets.value.find(\n        ts => ts.year === currentYear && ts.month === currentMonth\n      )\n    }\n  } catch (err) {\n    console.error('Error loading monthly timesheets:', err)\n  } finally {\n    loading.value = false\n  }\n}\n\nconst loadRecentTimeOffRequests = async () => {\n  try {\n    const response = await fetch('/api/time-off-requests/?limit=5', {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (response.ok) {\n      const result = await response.json()\n      recentTimeOffRequests.value = result.data || []\n    }\n  } catch (err) {\n    console.error('Error loading time-off requests:', err)\n  }\n}\n\nconst generateCurrentMonth = async () => {\n  generating.value = true\n  \n  try {\n    const currentDate = new Date()\n    const response = await fetch('/api/monthly-timesheets/generate', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      },\n      body: JSON.stringify({\n        year: currentDate.getFullYear(),\n        month: currentDate.getMonth() + 1\n      })\n    })\n\n    if (response.ok) {\n      await loadMonthlyTimesheets()\n    }\n  } catch (err) {\n    console.error('Error generating monthly timesheet:', err)\n  } finally {\n    generating.value = false\n  }\n}\n\nconst submitForApproval = async (timesheetId) => {\n  submitting.value = true\n  \n  try {\n    const response = await fetch(`/api/monthly-timesheets/${timesheetId}/submit`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (response.ok) {\n      await loadMonthlyTimesheets()\n    }\n  } catch (err) {\n    console.error('Error submitting timesheet:', err)\n  } finally {\n    submitting.value = false\n  }\n}\n\nconst reopenTimesheet = async (timesheetId) => {\n  try {\n    const response = await fetch(`/api/monthly-timesheets/${timesheetId}/reopen`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (response.ok) {\n      await loadMonthlyTimesheets()\n    }\n  } catch (err) {\n    console.error('Error reopening timesheet:', err)\n  }\n}\n\nconst viewDetails = (timesheet) => {\n  // TODO: Implement details modal or navigation\n  console.log('View details:', timesheet)\n}\n\nconst formatHours = (hours) => {\n  if (!hours || hours === 0) return '0h'\n  return hours % 1 === 0 ? `${hours}h` : `${hours.toFixed(1)}h`\n}\n\nconst formatDate = (dateString) => {\n  const date = new Date(dateString)\n  return date.toLocaleDateString('it-IT')\n}\n\nconst formatDateTime = (dateString) => {\n  const date = new Date(dateString)\n  return date.toLocaleDateString('it-IT') + ' ' + date.toLocaleTimeString('it-IT', { hour: '2-digit', minute: '2-digit' })\n}\n\nconst getStatusClass = (status) => {\n  switch (status) {\n    case 'approved':\n      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'\n    case 'submitted':\n      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'\n    case 'rejected':\n      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'\n    default:\n      return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'\n  }\n}\n\nconst getStatusText = (status) => {\n  switch (status) {\n    case 'approved':\n      return 'Approvato'\n    case 'submitted':\n      return 'In Attesa'\n    case 'rejected':\n      return 'Rifiutato'\n    default:\n      return 'Bozza'\n  }\n}\n\nconst getTypeClass = (type) => {\n  switch (type) {\n    case 'vacation':\n      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'\n    case 'leave':\n      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'\n    case 'smartworking':\n      return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'\n    default:\n      return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'\n  }\n}\n\nconst getTypeText = (type) => {\n  switch (type) {\n    case 'vacation':\n      return 'Ferie'\n    case 'leave':\n      return 'Permesso'\n    case 'smartworking':\n      return 'Smart Working'\n    default:\n      return 'Altro'\n  }\n}\n\n// Lifecycle\nonMounted(() => {\n  loadMonthlyTimesheets()\n  loadRecentTimeOffRequests()\n})\n</script>\n"}